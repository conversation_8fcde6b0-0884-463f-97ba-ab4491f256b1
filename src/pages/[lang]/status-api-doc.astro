---
import BaseApiDocLayout from '../../component/api-doc/BaseApiDocLayout.astro';
import ApiDataTable from '../../component/api-doc/ApiDataTable.astro';
import {useI18n} from '@/locales';

const {t} = useI18n()

export function getStaticPaths() {
  const langs = ['de', 'en', 'es', 'fr', 'it', 'jp', 'pt', 'tw', 'zh']

  return langs.map(item => ({
    params: {
      lang: item
    }
  }))
}

const statusList = [
  {
    title: '200',
    descHTML: t('apiDocKey32')
  },
  {
    title: '400',
    descHTML: t('apiDocKey33')
  },
  {
    title: '401',
    descHTML: t('apiDocKey34')
  },
  {
    title: '404',
    descHTML: t('apiDocKey35')
  },
  {
    title: '413',
    descHTML: t('apiDoc<PERSON>ey36')
  },
  {
    title: '429',
    descHTML: t('apiDocKey37')
  },
  {
    title: '500',
    descHTML: t('apiDocKey38')
  },
]
const taskList = [
  {
    title: '-11',
    descHTML: t('apiDocKey42'),
  },
  {
    title: '-8',
    descHTML: t('apiDocKey43'),
  },
  {
    title: '-7',
    descHTML: t('apiDocKey44'),
  },
  {
    title: '-5',
    descHTML: t('apiDocKey45'),
  },
  {
    title: '-4',
    descHTML: t('apiDocKey46'),
  },
  {
    title: '-3',
    descHTML: t('apiDocKey47'),
  },
  {
    title: '-2',
    descHTML: t('apiDocKey48'),
  },
  {
    title: '-1',
    descHTML: t('apiDocKey49'),
  },
  {
    title: '0',
    descHTML: t('apiDocKey50'),
  },
  {
    title: '1',
    descHTML: t('apiDocKey51'),
  },
  {
    title: '2',
    descHTML: t('apiDocKey52'),
  },
  {
    title: '3',
    descHTML: t('apiDocKey53'),
  },
  {
    title: '4',
    descHTML: t('apiDocKey54'),
  },
  {
    title: '5',
    descHTML: t('apiDocKey55'),
  },
]
---

<BaseApiDocLayout>
  <div class="p-[50px]">
    <div>
      <h2 class="text-[24px] font-bold">{t('apiDocKey28')}</h2>
      <p class="text-[16px] opacity-70 mt-[16px]">
        {t('apiDocKey29')}
      </p>
      <div class="px-6 lg:px-0 w-full lg:w-300 lg:max-w-90% mt-[30px]">
        <ApiDataTable
          tableHeadKey={t('apiDocKey30')}
          tableHeadValue={t('apiDocKey31')}
          tableList={statusList}
        />
      </div>
    </div>

    <div class="mt-[40px]">
      <h2 class="text-[24px] font-bold">{t('apiDocKey39')}</h2>
      <p class="text-[16px] opacity-70 mt-[16px]">
        {t('apiDocKey40')}
      </p>
      <div class="px-6 lg:px-0 w-full lg:w-300 lg:max-w-90% mt-[30px]">
        <ApiDataTable
          tableHeadKey={t('apiDocKey39')}
          tableHeadValue={t('apiDocKey41')}
          tableList={taskList}
        />
      </div>
    </div>
  </div>
</BaseApiDocLayout>


<style is:global>
  thead tr th span {
    opacity: 0.7;
    font-weight: normal !important;
  }
</style>

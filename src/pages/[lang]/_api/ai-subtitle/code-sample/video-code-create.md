```bash
curl --location 'https://techsz.aoscdn.com/api/tasks/media/subtitle' \
--header 'TaskSource: ApiKey' \
--header 'Content-Type: application/json' \
--header 'x-api-key: {YOUR-API-KEY}' \
--data '
{
  "url": "oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4",
  "filename": "demo",
  "primary_subtitles": [
    {
      "start": 0,
      "end": 2880,
      "text": "如何在极短的时间了解5000年历史"
    },
    {
      "start": 2880,
      "end": 5360,
      "text": "40分钟浓缩精华版的中国历史"
    }
  ],
  "subtitle_style": {
    "alignment": 2,
    "backcolor": "0,0,0,0.8",
    "font": 9,
    "font_bold": false,
    "font_color": "255,255,255,1",
    "font_italic": false,
    "font_size": 29,
    "font_underline": false,
    "position": 48
  }
}
'
```

```csharp
using System; using System.IO; using System.Net; using System.Text; using System.Threading.Tasks;

class Program {
  static async Task Main() => await SendRequest();

  static async Task SendRequest() {
    string url = "https://techsz.aoscdn.com/api/tasks/media/subtitle";
    try {
      var request = (HttpWebRequest)WebRequest.Create(url);
      request.Method = "POST";
      request.Headers["TaskSource"] = "ApiKey";
      request.Headers["Content-Type"] = request.ContentType = "application/json";
      request.Headers["x-api-key"] = "{YOUR-API-KEY}";

      string jsonData = @"{""url"":""oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4"",""filename"":""demo"",""primary_subtitles"":[{""start"":0,""end"":2880,""text"":""如何在极短的时间了解5000年历史""},{""start"":2880,""end"":5360,""text"":""40分钟浓缩精华版的中国历史""}],""subtitle_style"":{""alignment"":2,""backcolor"":""0,0,0,0.8"",""font"":9,""font_bold"":false,""font_color"":""255,255,255,1"",""font_italic"":false,""font_size"":29,""font_underline"":false,""position"":48}}";
      byte[] byteArray = Encoding.UTF8.GetBytes(jsonData);
      request.ContentLength = byteArray.Length;

      using (var requestStream = request.GetRequestStream())
        await requestStream.WriteAsync(byteArray, 0, byteArray.Length);

      using (var response = await request.GetResponseAsync())
      using (var responseStream = response.GetResponseStream())
      using (var reader = new StreamReader(responseStream))
        Console.WriteLine(await reader.ReadToEndAsync());
    }
    catch (WebException e) {
      Console.WriteLine($"Request failed: {e.Message}");
      if (e.Response != null) {
        using (var errorStream = e.Response.GetResponseStream())
        using (var reader = new StreamReader(errorStream))
          Console.WriteLine($"Error details: {await reader.ReadToEndAsync()}");
      }
    }
    catch (Exception e) {
      Console.WriteLine($"An error occurred: {e.Message}");
    }
  }
}
```

```java
import java.io.*; import java.net.*; import java.nio.charset.StandardCharsets;

public class ApiRequest {
  public static void main(String[] args) {
    try {
      URL url = new URL("https://techsz.aoscdn.com/api/tasks/media/subtitle");
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestMethod("POST");
      connection.setRequestProperty("TaskSource", "ApiKey");
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setRequestProperty("x-api-key", "{YOUR-API-KEY}");
      connection.setDoOutput(true);

      String jsonData = "{\"url\":\"oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4\",\"filename\":\"demo\",\"primary_subtitles\":[{\"start\":0,\"end\":2880,\"text\":\"如何在极短的时间了解5000年历史\"},{\"start\":2880,\"end\":5360,\"text\":\"40分钟浓缩精华版的中国历史\"}],\"subtitle_style\":{\"alignment\":2,\"backcolor\":\"0,0,0,0.8\",\"font\":9,\"font_bold\":false,\"font_color\":\"255,255,255,1\",\"font_italic\":false,\"font_size\":29,\"font_underline\":false,\"position\":48}}";
      try (OutputStream os = connection.getOutputStream()) {
        os.write(jsonData.getBytes(StandardCharsets.UTF_8));
      }

      int statusCode = connection.getResponseCode();
      System.out.println("Response status code: " + statusCode);

      try (BufferedReader br = new BufferedReader(
          new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = br.readLine()) != null) response.append(line);
        System.out.println("Response content: " + response);
      }
    } catch (Exception e) {
      System.out.println("Request failed: " + e.getMessage());
      e.printStackTrace();
    }
  }
}
```

```js
async function sendRequest() {
  const url = "https://techsz.aoscdn.com/api/tasks/media/subtitle";
  const headers = {
    "TaskSource": "ApiKey",
    "Content-Type": "application/json",
    "x-api-key": "{YOUR-API-KEY}"
  };

  const requestData = {
    "url": "oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4",
    "filename": "demo",
    "primary_subtitles": [
      {
        "start": 0,
        "end": 2880,
        "text": "如何在极短的时间了解5000年历史"
      },
      {
        "start": 2880,
        "end": 5360,
        "text": "40分钟浓缩精华版的中国历史"
      }
    ],
    "subtitle_style": {
      "alignment": 2,
      "backcolor": "0,0,0,0.8",
      "font": 9,
      "font_bold": false,
      "font_color": "255,255,255,1",
      "font_italic": false,
      "font_size": 29,
      "font_underline": false,
      "position": 48
    }
  };

  try {
    const response = await fetch(url, {method: "POST", headers, body: JSON.stringify(requestData)});
    console.log(await response.json());
  } catch (error) {
    console.error("Request failed:", error);
  }
}

sendRequest();
```

```python
import json, urllib.request

url = "https://techsz.aoscdn.com/api/tasks/media/subtitle"
headers = {"TaskSource": "ApiKey", "Content-Type": "application/json", "x-api-key": "{YOUR-API-KEY}"}
request_data = {
  "url": "oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4",
  "filename": "demo",
  "primary_subtitles": [
    {
      "start": 0,
      "end": 2880,
      "text": "如何在极短的时间了解5000年历史"
    },
    {
      "start": 2880,
      "end": 5360,
      "text": "40分钟浓缩精华版的中国历史"
    }
  ],
  "subtitle_style": {
    "alignment": 2,
    "backcolor": "0,0,0,0.8",
    "font": 9,
    "font_bold": False,
    "font_color": "255,255,255,1",
    "font_italic": False,
    "font_size": 29,
    "font_underline": False,
    "position": 48
  }
}

req = urllib.request.Request(
  url=url,
  data=json.dumps(request_data).encode('utf-8'),
  headers=headers,
  method="POST"
)

try:
  with urllib.request.urlopen(req) as response:
    result = json.loads(response.read().decode('utf-8'))
    print(result)
except Exception as e:
  print(f"Request failed: {e}")
```

```swift
import Foundation

func sendRequest() {
  guard let url = URL(string: "https://techsz.aoscdn.com/api/tasks/media/subtitle") else {
    print("Invalid URL")
    return
  }

  var request = URLRequest(url: url)
  request.httpMethod = "POST"
  request.addValue("ApiKey", forHTTPHeaderField: "TaskSource")
  request.addValue("application/json", forHTTPHeaderField: "Content-Type")
  request.addValue("{YOUR-API-KEY}", forHTTPHeaderField: "x-api-key")

  let subtitleStyle: [String: Any] = ["alignment": 2, "backcolor": "0,0,0,0.8", "font": 9,
    "font_bold": false, "font_color": "255,255,255,1", "font_italic": false,
    "font_size": 29, "font_underline": false, "position": 48]
    
  let subtitles: [[String: Any]] = [
    ["start": 0, "end": 2880, "text": "如何在极短的时间了解5000年历史"],
    ["start": 2880, "end": 5360, "text": "40分钟浓缩精华版的中国历史"]
  ]

  let requestData: [String: Any] = [
    "url": "oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4",
    "filename": "demo",
    "primary_subtitles": subtitles,
    "subtitle_style": subtitleStyle
  ]

  do {
    request.httpBody = try JSONSerialization.data(withJSONObject: requestData)
    URLSession.shared.dataTask(with: request) { data, response, error in
      if let error = error { print("Request failed: \(error)"); return; }
      guard let data = data else { print("No data returned"); return; }
      do {
        if let result = try JSONSerialization.jsonObject(with: data) as? [String: Any] { print(result) }
      } catch { print("JSON parsing error: \(error)") }
    }.resume()
  } catch { print("JSON encoding error: \(error)") }
}

sendRequest();
```

```go
package main

import (
  "bytes" "encoding/json" "fmt" "io/ioutil" "net/http"
)

func main() {
  url := "https://techsz.aoscdn.com/api/tasks/media/subtitle"
  subtitleStyle := map[string]interface{}{
    "alignment": 2, "backcolor": "0,0,0,0.8", "font": 9, "font_bold": false,
    "font_color": "255,255,255,1", "font_italic": false, "font_size": 29,
    "font_underline": false, "position": 48,
  }
  
  subtitles := []map[string]interface{}{
    {
      "start": 0,
      "end": 2880,
      "text": "如何在极短的时间了解5000年历史",
    },
    {
      "start": 2880,
      "end": 5360,
      "text": "40分钟浓缩精华版的中国历史",
    },
  }
  
  requestData := map[string]interface{}{
    "url": "oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4",
    "filename": "demo",
    "primary_subtitles": subtitles,
    "subtitle_style": subtitleStyle,
  }

  jsonData, err := json.Marshal(requestData)
  if err != nil { fmt.Println("JSON encoding error:", err); return; }

  req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
  if err != nil { fmt.Println("Error creating request:", err); return; }
  req.Header.Set("TaskSource", "ApiKey")
  req.Header.Set("Content-Type", "application/json")
  req.Header.Set("x-api-key", "{YOUR-API-KEY}")

  client := &http.Client{}
  resp, err := client.Do(req)
  if err != nil { fmt.Println("Error sending request:", err); return; }
  defer resp.Body.Close()

  body, err := ioutil.ReadAll(resp.Body)
  if err != nil { fmt.Println("Error reading response:", err); return; }

  var result interface{}
  err = json.Unmarshal(body, &result)
  if err != nil { fmt.Println("JSON parsing error:", err); return; }
  fmt.Println(result)
}
```

```dart
import 'dart:convert'; import 'dart:io';

Future<void> main() async {
  final url = Uri.parse("https://techsz.aoscdn.com/api/tasks/media/subtitle");
  final headers = {
    'TaskSource': 'ApiKey', 'Content-Type': 'application/json', 'x-api-key': '{YOUR-API-KEY}'
  };

  final subtitleStyle = {
    'alignment': 2, 'backcolor': '0,0,0,0.8', 'font': 9, 'font_bold': false,
    'font_color': '255,255,255,1', 'font_italic': false, 'font_size': 29,
    'font_underline': false, 'position': 48
  };
  
  final subtitles = [
    {
      'start': 0,
      'end': 2880,
      'text': '如何在极短的时间了解5000年历史'
    },
    {
      'start': 2880,
      'end': 5360,
      'text': '40分钟浓缩精华版的中国历史'
    }
  ];
  
  final requestData = {
    'url': 'oss://oss-cn-shenzhen.aliyuncs.com/recclouddev/ba4/ba48e8cc-2d35-4286-94f8-3917e96c1161.mp4',
    'filename': 'demo',
    'primary_subtitles': subtitles,
    'subtitle_style': subtitleStyle
  };

  try {
    final httpClient = HttpClient();
    final request = await httpClient.postUrl(url);
    headers.forEach((key, value) => request.headers.set(key, value));
    request.write(jsonEncode(requestData));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();

    if (response.statusCode == 200) { print(jsonDecode(responseBody)); }
    else {
      print('Request failed, status code: ${response.statusCode}');
      print('Response content: ${responseBody}');
    }
  } catch (e) { print('An error occurred: $e'); }
}
```


---
import EasyLayout from "../../layouts/EasyLayout.astro"
import Header from "@/component/header-v2/Header.vue"
import Footer from "@/component/footer-v2/Footer.vue";
import {useI18n} from "../../locales"
import MainScreen from "../../component/online-edit/speech-to-text/product/MainScreen.astro"
import AnimationBanner from "../../component/online-edit/speech-to-text/product/AnimationBanner.astro"
import TextUseScene from "../../component/online-edit/speech-to-text/product/TextUseScene.astro"
import CommentsList from "../../component/home/<USER>"
import logoIcon from "@images/favicon.png"
import {getLang} from "@central/i18n"
import MobileDownloadAppBtn from '@/component/online-edit/MobileDownloadAppBtn.astro';
//@ts-ignore
import {Picture} from '@central/assets/imagetools'
import StartLogo from '~icons/speech-to-text/star.svg'

const {t} = useI18n()
---
<EasyLayout>
  <Header client:load/>
  <MainScreen/>

  <AnimationBanner/>
  <section
    class="hidden lg:block py-40px lg:pt-[120px] lg:pb-[60px] px-6 text-center relative overflow-hidden bg-[url(@images/speech-to-text/text-use-scene-bg.svg)] bg-[length:100%_100%] bg-no-repeat bg-center">
    <h2 class="lg:block hidden text-#1E3D24 text-[40px] lg:text-10 leading-[4px] lg:text-center font-bold mb-[60px]">
      {t("SpeechTextAnywhere")}
    </h2>
    <TextUseScene/>
  </section>

  <section class="relative py-70px lg:py-[160px] bg-[url(@/assets/images/home/<USER>/bg-big.svg)] bg-cover">
    <div class="relative z-10">
      <h2
        class="font-bold flex-center flex-wrap gap-[4px] text-24px lg:text-[40px] text-center px-[24px] user-reccloud pb-30px lg:pb-[64px]"
      >
        <span class:list={[['de', 'en'].includes(getLang()) ? 'order-1' : '']}>{t("speechReccloudMillons")}</span>
        <span class="text-primary-color mx-[4px] relative" set:html={t('speechReccloudLove')}></span>
      </h2>
      <CommentsList type={"speechToText"} client:idle/>
    </div>
  </section>

  <section class="relative py-70px lg:py-[160px]">
    <div class="absolute inset-0 object-cover">
      <Picture
        src='src/assets/images/home/<USER>'
        alt="background"
        layout="fill"
        width={1920}
      />
    </div>

    <div class="relative z-10 flex flex-col justify-center items-center">
      <div class="flex justify-center items-center gap-[16px]">
        <img class="size-[46px] hidden lg:block" src={logoIcon} alt="" loading="lazy"/>
        <span class="text-[24px] lg:text-[40px] font-bold text-center">{t("readyForSpeech")}</span>
      </div>
      <p
        class="mt-[22px] my-[40px] lg:mt-[40px] mb-30px lg:mb-[64px] lg:text-[20px] text-16px px-[24px] text-center"
      >
        {t("readyForSpeechDesc")}
      </p>
      <button
        class="try-now-btn hidden lg:flex-center min-w-[254px] h-[60px] gradient-button text-[22px] font-bold px-[20px]">
        <span class="flex whitespace-nowrap">
          {t("mainStartForFree")}
          <i class="self-start">
            <StartLogo class="!block"/>
          </i>
        </span>
      </button>
      <MobileDownloadAppBtn class="mx-auto w-fit"/>
    </div>
  </section>

  <Footer id="footer-id" client:idle/>

</EasyLayout>

<style lang="scss">
  @import "../../style/common.scss";
</style>

<style is:global>
  header .header-login-status.logined {
    display: flex;
  }

  @media screen and (max-width: 1024px) {
    header .header-login-status.logined .header-login-avatar {
      width: 30px;
      height: 30px;
    }
  }
</style>

<script>
  import SpeechToTextTrack from '../../track/SpeechToTextTrack';
  import useLoginService from '@/services/useLoginService';
  import {adsHeader} from '../../component/ads/ads';
  import {getRegionPath} from '../../utils/util';
  import {PreviewStatus} from '../../interface/SpeechToText';

  const {isLoginRef, doLogin} = useLoginService()

  registerEvent()
  adsHeader()

  function registerEvent() {
    const elList = document.querySelectorAll<HTMLElement>('.try-now-btn')
    elList.forEach(i => {
      i.onclick = () => {
        SpeechToTextTrack.clickStartNow()
        window.open(getRegionPath() + '/speech-to-text-online-start', '_self')
      }
    })
    const productToHistoryElList = document.querySelectorAll<HTMLElement>('.speech-to-history-btn')
    productToHistoryElList.forEach(i => {
      i.onclick = async () => {
        if (isLoginRef.value === false) {
          await doLogin()
        }
        window.open(getRegionPath() + `/speech-to-text-online-start?v=${PreviewStatus.historyList}`, '_self')
      }
    })
  }

</script>

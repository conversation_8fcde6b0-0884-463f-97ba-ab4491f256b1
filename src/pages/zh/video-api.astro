---
import {useI18n} from '../../locales';
import Layout from "../../layouts/ChinaLayout.astro";
import Header from "../../component/header-v2/Header.vue"
import Footer from "../../component/footer-v2/Footer.vue";
import Banner from "@/component/api/Banner.vue";
import textToSpeechIcon from '@images/header-nav/FeatList/text-to-speech.svg';
import bgGreen from '../../assets/images/online-edit/bg-green.svg';
import bgLine from '../../assets/images/online-edit/bg-line.svg';
import middle from '../../assets/images/online-edit/middle.svg';
import rightArrow from '../../assets/images/api/right-arrow.svg';
import TitleItem from '../../component/api/TitleItem.astro';
import starIcon from '../../assets/images/online-edit/star.svg';
import OnlineItem from '../../component/OnlineItem.astro';
import {getRegionPath} from '../../utils/util';
import aiSubtitle from '../../assets/images/online-edit/ai-subtitle.svg';
import sceneBg from '../../assets/images/api/scene-bg.svg'
import videoEdit from '../../assets/images/api/edit.png'
import audioSceneIcon from '../../assets/images/api/audio.png'
import videoIcon from '../../assets/images/api/video.png'
import aiIcon from '../../assets/images/api/ai-audio.png'
import iconOne from '../../assets/images/api/icon-one.svg'
import iconTwo from '../../assets/images/api/icon-two.svg'
import iconThree from '../../assets/images/api/icon-three.svg'
import contact1 from '../../assets/images/api/contact-us-icon.svg'
import contact2 from '../../assets/images/api/contact-us-icon2.svg'
import contact3 from '../../assets/images/api/constact-us-icon3.svg'
import contact4 from '../../assets/images/api/contact-us-icon4.svg'
import contact5 from '../../assets/images/api/contact-us-icon5.svg'
import contact6 from '../../assets/images/api/contact-us-icon6.svg'
import ApiLogo from '@/component/api/ApiLogo.vue'
import voiceText from '../../assets/images/header-nav/FeatList/voiceText.svg';
import AiVideoGenerate from '@/assets/images/header-nav/FeatList/aiTextToVideo.svg'

const {t} = useI18n()
const list = [
  {
    icon: voiceText,
    title: t('onlineSpeechToText'),
    desc: t('speechToTextAi'),
    href: getRegionPath() + '/speech-to-text-online',
  },
  {
    href: getRegionPath() + '/ai-subtitle',
    icon: aiSubtitle,
    title: t('aiSubtitle'),
    desc: t('itemDesc7'),
  },
  {
    icon: textToSpeechIcon,
    title: t('aiTextToSpeech'),
    desc: t('aiTextToSpeechDesc'),
    href: getRegionPath() + '/text-to-speech-online',
  },
  {
    icon: AiVideoGenerate,
    title: t('headerToolsVideoTitle'),
    desc: t('headerToolsVideoDesc'),
    href: getRegionPath() + '/text-to-video',
  },

]

const sceneList = [
  {
    img: videoEdit,
    title: t('videoEdit')
  },
  {
    img: audioSceneIcon,
    title: t('audioEdit')
  },
  {
    img: videoIcon,
    title: t('videoConversion')
  },
  {
    img: aiIcon,
    title: t('aiDubbing')
  }
]

const advanceList = [
  {
    bg: 'bg-[rgba(0,206,255,0.06)]',
    icon: iconOne,
    title: t('aIRecognition'),
    subtitle: t('aIRecognitionDesc'),
    blurColor: 'bg-[rgba(0,206,255,0.12)]',
  },
  {
    bg: 'bg-[rgba(24,173,37,0.06)]',
    icon: iconTwo,
    title: t('SimpleFuncs'),
    subtitle: t('simpleFuncsDesc'),
    blurColor: 'bg-[rgba(24,173,37,0.12)]',
  },
  {
    bg: 'bg-[rgba(255,230,0,0.06)]',
    icon: iconThree,
    title: t('goodExpandability'),
    subtitle: t('goodExpandabilityDesc'),
    blurColor: 'bg-[rgba(255,230,0,0.12)]',
  }
]

const contactUsList = [
  {
    icon: contact1,
    title: t('APIInterface'),
    subtitle: t('APIInterfaceDesc'),
    btnText: t('freeUse'),
    extra: t('freeUseNum'),
  },
  {
    icon: contact2,
    title: t('businessUse'),
    subtitle: t('businessUseDesc'),
    btnText: t('contactBusiness'),
    extra: '',
  }
]
---

<Layout>
  <Header client:load/>
  <div class="online-edit-container w-full relative h-1/2 pt-[72px] bg-[#faf9f9] overflow-hidden pb-[20vw]">
    <div class="w-365px h-421px absolute scale-70 right--60px top-0">
      <img class="h-full object-cover" src={bgGreen} alt=""/>
    </div>

    <div class="top--20px w-693px h-600px mx-auto select-none" id="container">
      <img src={bgLine} alt=""/>
    </div>


    <div class="absolute top-[160px] left-50% min-w-[67.7vw] h-auto mx-auto -translate-x-1/2 scale-top mt-[60px] ">
      <div class="flex justify-center gap-[100px]">
        <div class="flex flex-col items-start text-deep-color">
          <h1 class="text-[56px]/[80px] font-bold mb-[2.29vw]">{t('apiTitle')}</h1>
          <p class="w-[610px] text-[20px]/[40px] text-[rgba(30,61,36,0.6)] bg-full" set:html={t('apiDesc')}></p>
          <p
            class="text-[20px]/[42px] text-primary-color flex justify-end items-center gap-[4px] ml-auto transition duration-500 hover:translate-y-[4px]">
            <a class="font-semibold underline-offset-2 underline decoration-1"
               href={getRegionPath() + "/start"}>{t('learn_more')}</a>
            <svg width="9" height="14" viewBox="0 0 9 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1.65674 1L7.31359 6.65685L1.65674 12.3137" stroke="#18AD25" stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"/>
            </svg>
          </p>
          <div class="flex mt-[71px] gap-[80px]">
            <div class="flex online-btn-primary online-btn free-interface">
              <div>{t('apiDocBtn')}</div>
              <img src={rightArrow} alt="">
            </div>
            <div class="flex online-btn-secondary online-btn btn-next api-pricing">
              <div>{t('apiPricingKey1')}</div>
            </div>
          </div>
        </div>

        <div>
          <ApiLogo client:load></ApiLogo>
        </div>

      </div>

    </div>

    <img class="" src={middle} alt=""/>

    <!--合作伙伴-->
    <section class="mt-[90px]">
      <TitleItem left={t('key11')} right={t('key12')}/>
      <Banner client:visible class="mt-[70px]"/>
    </section>
    <!--功能列表-->
    <section class="mt-[180px] pt-[60px]" id="func">
      <TitleItem left={t('key21')} right={t('key22')}/>
      <div class="relative pt-[70px]">
        <div class="func-bg absolute left-0 top-0 z-0"></div>
        <ul
          class="grid justify-center items-center gap-x-[7vw] gap-y-[40px]"
          style="grid-template-columns: repeat(4, 196px);"
        >
          {
            list.map(item => {
              return (
                <li
                  class="w-[200px] h-[200px] group shadow-sm hover:shadow-online-edit border-transparent
                  rounded-[30px] overflow-hidden transition border-3 hover:border-primary-color"
                >
                  <a
                    class="link w-full h-full bg-white cursor-pointer relative flex flex-col text-center"
                    href={item.href}
                    target='_blank'
                  >
                    <div class="relative flex-1 flex justify-center items-center w-full">
                      <div class="scale-100 group-hover:scale-0 transition w-full translate-y-[16px]">
                        <OnlineItem icon={item.icon}></OnlineItem>
                      </div>
                      <div
                        class="w-[calc(100%-60px)] flex justify-center items-center text-[18px]/[1.5] text-[rgba(30,61,36,0.6)] h-full scale-0 group-hover:scale-100 box-content absolute top-0 left-1/2 -translate-x-1/2 transition px-[30px]">
                        {item.desc}
                      </div>
                    </div>

                    <div class="relative h-[54px]">
                      <div
                        class="transition group-hover:opacity-0 group-hover:-translate-y-[10px] mt-5px">{item.title}</div>
                      <div
                        class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-[4px] opacity-0 transition group-hover:opacity-100 w-max mx-auto text-primary-color rounded-[20px] text-[20px]/[27px] font-semibold  group-hover:-translate-y-[0] px-[28px] py-[6px] bg-primary-color/[0.2]">
                        {t('tryNow')}
                      </div>
                    </div>
                  </a>
                </li>
              )
            })
          }
        </ul>
      </div>
      <div class="flex mt-[71px] gap-[80px] justify-center">
        <div class="flex online-btn-primary online-btn free-interface">
          <div>{t('apiDocBtn')}</div>
          <img src={rightArrow} alt="">
        </div>
        <div class="flex online-btn-secondary online-btn btn-next api-pricing">
          <div>{t('apiPricingKey1')}</div>
        </div>
      </div>
    </section>
    <!--使用场景-->
    <section class="mt-[240px] relative">
      <div class="absolute -top-[70px] left-0">
        <img class="w-screen" src={sceneBg} alt="">
      </div>
      <div class="relative">
        <TitleItem left={t('key41')} right={t('key42')}/>
        <div class="mt-[74px]">
          <ul class="flex justify-center items-center gap-[1.7vw]">
            {
              sceneList.map(item => {
                return (
                  <li
                    class="w-[14.4vw] bg-white rounded-[20px] overflow-hidden
                    shadow-md cursor-pointer transition duration-500
                    group hover:translate-y-[4px] hover:shadow-xl"
                  >
                    <div class="h-[8.9vw] overflow-hidden">
                      <img class="transition duration-500 group-hover:scale-125" src={item.img} alt={item.title}>
                    </div>

                    <div class="py-[12px] text-center">{item.title}</div>
                  </li>
                )
              })
            }
          </ul>
        </div>
      </div>
    </section>

    <!--我们的优势-->
    <section class="mt-[240px]">
      <TitleItem left={t('key51')} right={t('key52')}/>

      <div class="mt-[100px]">
        <ul class="flex justify-center items-center gap-[3.7vw]">
          {
            advanceList.map((item, index) => {
              return (
                <li
                  class=`w-[18.3vw] h-[26vw] relative advance-shadow rounded-[1.45vw] group ${item.bg} border-white border-4`>
                  <!--<img class="absolute" src={item.bg} alt="">-->
                  <div class="-translate-y-[1.5vw]">
                    <img class="w-[5.7vw] h-[5.6vw] ml-[2.8vw] move" src={item.icon} alt={item.title}>
                    <div class="px-[2.5vw] mt-[1.2vw] mb-[1.875vw] text-[1.46vw]/[1.875vw] font-bold">{item.title}</div>
                    <div class="px-[2.5vw] text-[1vw]/[1.875vw]">{item.subtitle}</div>
                  </div>
                  <div class=`absolute right-0 bottom-0 w-[10vw] h-[10vw] blur-[50px] ${item.blurColor}`></div>
                </li>
              )
            })
          }
        </ul>
      </div>
    </section>


    <!--联系我们-->
    <section class="mt-[240px]">
      <div class="relative">
        <TitleItem left={t('key61')} right={t('key62')}/>
        <div class="absolute right-[6.35vw] -top-[1.5vw]">
          <svg width="55" height="82" viewBox="0 0 55 82" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path opacity="0.3"
                  d="M5.94501 55.8328C0.510495 73.972 13.2822 78.8455 20.3474 79.0149C52.4223 79.8873 47.4213 60.0636 36.6487 45.1246C25.8761 30.1855 12.7382 33.1589 5.94501 55.8328Z"
                  stroke="#E4E4E4" stroke-width="4" stroke-linecap="round"/>
            <path
              d="M6.22002 35.1666C13.5789 56.0184 28.9693 51.7599 35.7447 47.0242C66.5752 25.6222 48.2219 10.414 27.7899 3.8061C7.35788 -2.80184 -2.97857 9.10183 6.22002 35.1666Z"
              fill="white" stroke="#18AD25" stroke-opacity="0.2" stroke-width="4" stroke-linecap="round"/>
          </svg>
        </div>
      </div>

      <div class="mt-[74px]">
        <ul class="flex justify-center items-center gap-[4.166vw] relative h-max">
          <li class="absolute left-0 -top-[10vw] w-[23.4vw] -z-0">
            <img src={contact5} alt="">
          </li>

          {
            contactUsList.map((item, index) => {
              return (
                <li
                  class="w-[29.1vw] h-[24vw] card-shadow bg-white px-[4vw] flex flex-col rounded-[20px] relative overflow-hidden z-20">
                  <img class="scale-75 origin-top-right absolute right-0 top-0" src={contact4} alt="">
                  <img class="w-[3.2vw] mt-[2.8vw] mb-[1.35vw]" src={item.icon} alt={item.title}>
                  <div class="flex gap-[8px] mb-[0.88vw]">
                    <span class="text-[1.45vw]/[1.875vw] font-bold">{item.title}</span>
                    <img class="w-[1.1vw] h-[1vw]" src={contact3} alt="">
                  </div>

                  <div class="flex-1 mb-[0.88vw]">{item.subtitle}</div>

                  <div class="w-max text-center">
                    <div
                      class=`flex online-btn-primary online-btn ${index === 0 ? 'free-interface' : 'contact-business'}`>
                      <div class='flex'>
                        <div>{item.btnText}</div>
                        <img class="self-start" src={starIcon} alt="">
                      </div>
                    </div>
                    <div
                      class=`h-[4.1vw] flex justify-center items-center
                       text-[1vw]/[1.35vw] text-[rgba(30,61,36,0.7)]
                       ${item.extra ? 'visible' : 'invisible'}`
                    >
                      {item.extra}
                    </div>
                  </div>
                </li>
              )
            })
          }

          <li class="absolute bottom-0 left-[calc(50%+29.1vw-10vw)] translate-y-1/2 w-[13.8vw] h-[13.8vw]">
            <img src={contact6} alt="">
          </li>

          <li class="absolute left-[calc(50%+29.1vw+4vw)] top-0 translate-x-1/2">
            <svg width="58" height="46" viewBox="0 0 58 46" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path opacity="0.3"
                    d="M49.4353 19.626C40.9671 7.33041 31.6662 12.7759 28.0742 17.0356C11.7035 36.3229 26.3589 43.0349 40.8608 43.7874C55.3627 44.5399 60.0206 34.9955 49.4353 19.626Z"
                    stroke="#E4E4E4" stroke-width="4" stroke-linecap="round"/>
              <path
                d="M30.7855 7.90294C17.638 -3.57407 8.87302 5.51189 6.13396 11.4895C-6.38743 38.588 12.0034 41.776 28.4377 38.1895C44.872 34.6029 47.2198 22.2492 30.7855 7.90294Z"
                fill="white" stroke="#E4E4E4" stroke-width="4" stroke-linecap="round"/>
            </svg>
          </li>

        </ul>
      </div>
    </section>
  </div>

  <Footer client:idle/>
</Layout>

<style lang="scss">
  @import "../../style/common.scss";

  .group:hover .move {
    animation: move 1s 1 alternate;
  }

  @keyframes move {
    0% {
      transform: translateY(0%);
    }

    50% {
      transform: translateY(-25%);
    }

    100% {
      transform: translateY(0%);
    }
  }


  @media screen and (max-height: 700px) {
    .scale-top {
      margin-top: 0;
      top: 8.3vw;
    }
  }

  .online-edit-container {
    background: #FAFAFA;
  }

  .online-edit-container .online-btn {
    width: 260px;
    height: 68px;
    font-weight: 600;
    font-size: 28px;
    line-height: 37px;
  }

  .online-edit-container .btn-next {
    border-width: 4px;
  }

  .online-edit-container .func-bg {
    width: 100%;
    height: 550px;
    background: url("../../assets/images/api/func-bg.svg") no-repeat center;
    background-size: 100% 100%;
    scale: 1.5;
  }

  .advance-shadow {
    box-shadow: 0 4px 22px 12px rgba(30, 61, 36, 0.04);
  }

  .card-shadow {
    box-shadow: 0 4px 22px 12px rgba(30, 61, 36, 0.04);
  }

  :global(.online-edit-container .bg-full span) {
    display: inline-block;
    background-color: pink;
    padding: 0 6px;
    font-weight: bold;
    color: white;
    border-radius: 10px;
  }

  :global(.online-edit-container .bg-full span:nth-child(1)) {
    background-color: #FF967E;
    margin-left: 4px;
  }

  :global(.online-edit-container .bg-full span:nth-child(2)) {
    background-color: #F6C134;
  }

  :global(.online-edit-container .bg-full span:nth-child(3)) {
    background-color: #17AD26;
    margin-right: 4px;
  }

  :global(.online-edit-container .bg-full strong) {
    font-weight: bold;
    color: #1e3d24;
    margin: 0 4px;
  }
</style>


<script>
  import {getLang} from '@central/i18n'
  import {getRegionPath} from '../../utils/util';
  import showContactUsDialog from '../../render/showContactUsDialog';

  const contactUs = document.querySelectorAll<HTMLElement>('.contact-business')
  contactUs.forEach(item => {
    item.onclick = () => {
      showContactUsDialog()
    }
  })
  const apiPricing = document.querySelectorAll<HTMLElement>('.api-pricing')
  apiPricing.forEach(i => {
    i.onclick = () => {
      window.open(getRegionPath() + '/api-pricing', '_self')
    }
  })
  const lang = getLang()
  const freeInterfaces = document.querySelectorAll<HTMLElement>('.free-interface')
  freeInterfaces.forEach(item => {
    item.onclick = () => {
      window.open(`/${lang}/video-api-doc`)
    }
  })
</script>

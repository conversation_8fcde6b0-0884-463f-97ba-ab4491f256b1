---
import Layout from '@/layouts/EasyLayout.astro'
import Header from '@/component/header-v2/Header.vue'
import TextToSpeech from '@/component/online-edit/text-to-speech-v2/TextToSpeech.vue'
import LoadingIcon from '~icons/text-to-speech/loading.svg'
---
<Layout>
  <Header client:load/>
  <main class="w-full h-screen">
    <TextToSpeech client:only="vue">
      <div
        slot="fallback"
        class="size-full absolute inset-0 bg-white rounded-5 flex-center"
      >
        <LoadingIcon class="animate-spin"/>
      </div>
    </TextToSpeech>
  </main>
</Layout>

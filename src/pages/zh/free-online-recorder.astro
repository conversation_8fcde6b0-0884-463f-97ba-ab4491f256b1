---
import Layout from '../../layouts/EasyLayout.astro'
import Header from "../../component/header-v2/Header.vue"
import OnlineRecorder from '@/component/online-edit/online-recorder/OnlineRecorder.vue'
---
<Layout>
  <Header client:load/>
  <main class="h-screen">
    <OnlineRecorder client:load/>
  </main>
</Layout>

<script>
  import {useI18n} from '../../locales';
  import ClientInfo from '../../utils/ClientInfo';

  if (ClientInfo.isSafari) {
    const {t} = useI18n()
    const div = document.createElement('div')
    div.className = 'w-full h-screen flex justify-center items-center text-2xl'
    div.textContent = t('safariNotSupport')
    const content = document.querySelector('main')
    content.textContent = ''
    content.appendChild(div)
  }
</script>


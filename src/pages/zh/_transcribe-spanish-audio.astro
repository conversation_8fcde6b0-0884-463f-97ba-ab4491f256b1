---
import Footer from '@/component/footer-v2/Footer.vue'
import Layout from '@/layouts/EasyLayout.astro'
import Header from "@/component/header-v2/Header.vue"
import MainScreen from '@/component/youtube/product/MainScreen.astro';
import CommentsSection from '@/component/youtube/product/CommentsSection.astro';
import FAQSection from '@/component/youtube/product/FAQSection.astro';
import LastScreenSection from '@/component/youtube/product/LastScreenSection.astro';
import {useI18n} from "@/locales";
import avatarIcon1 from '@/assets/images/ai-subtitle-translator/userAvatar1.webp'
import avatarIcon2 from '@/assets/images/ai-subtitle-translator/userAvatar2.webp'
import avatarIcon3 from '@/assets/images/ai-subtitle-translator/userAvatar3.webp'
import avatarIcon4 from '@/assets/images/ai-subtitle-translator/userAvatar4.webp'
import avatarIcon5 from '@/assets/images/ai-translate/translate-cn-user1.png'
import avatarIcon6 from '@/assets/images/ai-translate/translate-cn-user2.png'
import avatarIcon7 from '@/assets/images/ai-translate/translate-cn-user3.png'
import avatarIcon8 from '@/assets/images/ai-translate/translate-cn-user4.png'
import {getLang} from '@central/i18n';
import step1Icon from '../../assets/images/audio-to-text-online/steps/step1.svg';
import step2Icon from '../../assets/images/audio-to-text-online/steps/step2.svg';
import step3Icon from '../../assets/images/audio-to-text-online/steps/step3.svg';
import StarIcon from '~icons/ai-youtube/star-icon.svg'
import Advanced1Icon from '~icons/ai-youtube/advanced-1.svg';
import Advanced2Icon from '~icons/ai-youtube/advanced-2.svg';
import Advanced3Icon from '~icons/ai-youtube/advanced-3.svg';
import Advanced4Icon from '~icons/ai-youtube/advanced-4.svg';
import AudioToText from '../../component/single-page/audio-to-text-online/AudioToTextOnline.vue'


const {t} = useI18n();
const lang = getLang()
const isZhTwJp = lang === 'zh' || lang === 'tw' || lang === 'jp'

const stepList = [
  {
    icon: step1Icon,
    title: t('transcribeSpanishAudioKey4'),
    description: t('transcribeSpanishAudioKey5')
  },
  {
    icon: step2Icon,
    title: t('transcribeSpanishAudioKey6'),
    description: t('transcribeSpanishAudioKey7')
  },
  {
    icon: step3Icon,
    title: t('transcribeSpanishAudioKey8'),
    description: t('transcribeSpanishAudioKey9')
  }
];

const advancedList = [
  {

    icon: Advanced3Icon,
    title: t('transcribeSpanishAudioKey11'),
    description: t('transcribeSpanishAudioKey12')
  },
  {
    icon: Advanced4Icon,
    title: t('transcribeSpanishAudioKey13'),
    description: t('transcribeSpanishAudioKey14')
  },
  {
    icon: Advanced1Icon,
    title: t('transcribeSpanishAudioKey15'),
    description: t('transcribeSpanishAudioKey16')
  },
  {
    icon: Advanced2Icon,
    title: t('transcribeSpanishAudioKey17'),
    description: t('transcribeSpanishAudioKey18')
  }
];

const commentsList = [
  {
    comment: t('transcribeSpanishAudioKey20'),
    name: t('transcribeSpanishAudioKey21'),
    avatar: isZhTwJp ? avatarIcon6 : avatarIcon1
  },
  {
    comment: t('transcribeSpanishAudioKey22'),
    name: t('transcribeSpanishAudioKey23'),
    avatar: isZhTwJp ? avatarIcon8 : avatarIcon2
  },
  {
    comment: t('transcribeSpanishAudioKey24'),
    name: t('transcribeSpanishAudioKey25'),
    avatar: isZhTwJp ? avatarIcon5 : avatarIcon4
  },
  {
    comment: t('transcribeSpanishAudioKey26'),
    name: t('transcribeSpanishAudioKey27'),
    avatar: isZhTwJp ? avatarIcon7 : avatarIcon3
  },
]

const QAList = [
  {
    question: t('transcribeSpanishAudioKey29'),
    answer: t('transcribeSpanishAudioKey30')
  },
  {
    question: t('transcribeSpanishAudioKey31'),
    answer: t('transcribeSpanishAudioKey32')
  },
  {
    question: t('transcribeSpanishAudioKey33'),
    answer: t('transcribeSpanishAudioKey34')
  },
  {
    question: t('transcribeSpanishAudioKey35'),
    answer: t('transcribeSpanishAudioKey36')
  },
  {
    question: t('transcribeSpanishAudioKey37'),
    answer: t('transcribeSpanishAudioKey38')
  },
]
---

<Layout theme="dark">
  <Header client:load/>

  <main class="w-full font-['Poppins',var(--body-font)]">
    <MainScreen
      title={t('transcribeSpanishAudioKey1')}
      titleReplaceClass={'gradient-text'}
      description={t('transcribeSpanishAudioKey2')}
      stepTitle={t('transcribeSpanishAudioKey3')}
      stepList={stepList}
      advancedTitle={t('transcribeSpanishAudioKey10')}
      advancedList={advancedList}
      btnTitle={t('aiAudioToTextOnlineChooseStartBtnTitle')}
    >
      <div class="mt-[40px] lg:mt-[70px]">
        <AudioToText client:load/>
      </div>
    </MainScreen>

    <CommentsSection
      title={t('transcribeSpanishAudioKey19').replace('<i>', '<div class="section-title-div inline relative"><i class="section-title-light gradient-text not-italic relative">').replace('</i>', '</i></div>')}
      commentsList={commentsList}
    />
    <FAQSection
      title={t('transcribeSpanishAudioKey28')}
      QAList={QAList}
    />
    <LastScreenSection
      title={t('transcribeSpanishAudioKey39')}
      desc={t('transcribeSpanishAudioKey40')}
      btnTitle={t('aiAudioToTextOnlineChooseStartBtnTitle')}
    />
  </main>

  <Footer client:idle/>
</Layout>


<style is:global>

  header .header-login-status.logined {
    display: flex;
  }

  @media screen and (max-width: 1024px) {
    header .header-login-status.logined .header-login-avatar {
      width: 30px;
      height: 30px;
    }
  }

  .gradient-text {
    color: white;
    background: linear-gradient(to right, #D6FB72, #76F9B1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
  }

  .line-tilde::after {
    content: '';
    width: 100%;
    height: 16px;
    position: absolute;
    left: 100%;
    top: 100%;
    transform: translate(-100%, -50%);
    background: url("@/assets/images/ai-translate/line.png") no-repeat center;
  }

  @media screen and (max-width: 1024px) {
    .line-tilde::after {
      display: none;
    }
  }

  @keyframes like {
    0% {
      transform: translate(-100%, -100%) rotate(25deg);
    }

    100% {
      transform: translate(-100%, -100%) rotate(0deg);
    }
  }

  @keyframes breathe {
    0% {
      transform: translate(-100%, -100%) scale(1);
    }

    100% {
      transform: translate(-100%, -100%) scale(0.2);
    }
  }

  @keyframes userBreath {
    0% {
      transform: translate(100%, -100%) scale(1);
    }

    100% {
      transform: translate(100%, -100%) scale(0.2);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .section-title-light::before {
    content: '';
    position: absolute;
    left: unset;
    right: 0;
    top: 0;
    width: 14px;
    height: 14px;
    background-color: #56E8B3;
    animation: breathe 2s infinite alternate;
    border-radius: 14px 14px 14px 0;
    transform-origin: left bottom;
    transform: translate(100%, -100%);
    animation-name: userBreath;
  }

  .section-title-light::after {
    content: '';
    width: 28px;
    height: 40px;
    position: absolute;
    left: 0;
    top: 0;
    transform: translate(-100%, -100%);
    background: url("@/assets/icons/ai-translate/user-good.svg") no-repeat center;
    animation: like 1s infinite alternate;
    transform-origin: left bottom;
  }

  .section-title-div::before {
    content: '';
    width: 22px;
    height: 22px;
    position: absolute;
    left: 40px;
    top: -80%;
    background: url("@/assets/icons/ai-translate/flower.svg") no-repeat center;
    animation: rotate 8s linear infinite;
  }

  .user-step-section-img {
    width: 90px;
    height: 90px;
  }

  @media screen and (max-width: 1024px) {
    .user-step-section-img {
      width: 72px;
      height: 72px;
    }
  }

</style>

<script>
  import AudioToTextTrack from '../../track/AudioToTextTrack';

  const startButtonElList = document.querySelectorAll<HTMLAnchorElement>('.try-now-btn')
  startButtonElList.forEach(i => {
    i.onclick = () => {
      AudioToTextTrack.clickStartNow().then()
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  })
</script>

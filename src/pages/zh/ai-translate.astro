---
import Layout from '@/layouts/EasyLayout.astro'
import Header from "@/component/header-v2/Header.vue"
import Footer from "@/component/footer-v2/Footer.vue";
import MainScreen from '@/component/online-edit/ai-translate/product/ai-translate/MainScreen.astro';
import ContentPage from '@/component/online-edit/ai-translate/product/ai-translate/ContentPage.astro';
---

<Layout theme="dark">
  <Header client:load/>
  <MainScreen/>
  <ContentPage/>
  <Footer client:idle/>
</Layout>

<style is:global>
  .gradient-text {
    color: white;
    background: linear-gradient(to right, #D6FB72, #76F9B1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
  }

  .line-tilde::after {
    content: '';
    width: 100%;
    height: 16px;
    position: absolute;
    left: 100%;
    top: 100%;
    transform: translate(-100%, -50%);
    background: url("@/assets/images/ai-translate/line.png") no-repeat center;
  }

  @media screen and (max-width: 1024px) {
    .line-tilde::after {
      display: none;
    }
  }

  @keyframes like {
    0% {
      transform: translate(-100%, -100%) rotate(25deg);
    }

    100% {
      transform: translate(-100%, -100%) rotate(0deg);
    }
  }

  @keyframes breathe {
    0% {
      transform: translate(-100%, -100%) scale(1);
    }

    100% {
      transform: translate(-100%, -100%) scale(0.2);
    }
  }

  @keyframes userBreath {
    0% {
      transform: translate(100%, -100%) scale(1);
    }

    100% {
      transform: translate(100%, -100%) scale(0.2);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .section-title-light {
    &::before {
      content: '';
      position: absolute;
      left: unset;
      right: 0;
      top: 0;
      width: 14px;
      height: 14px;
      background-color: #FFF;
      animation: breathe 2s infinite alternate;
      border-radius: 14px 14px 14px 0;
      transform-origin: left bottom;
      transform: translate(100%, -100%);
      animation-name: userBreath;
    }

    &::after {
      content: '';
      width: 28px;
      height: 40px;
      position: absolute;
      left: 0;
      top: 0;
      transform: translate(-100%, -100%);
      background: url("@/assets/icons/ai-translate/user-good.svg") no-repeat center;
      animation: like 1s infinite alternate;
      transform-origin: left bottom;

    }

  }

  .section-title-div::before {
    content: '';
    width: 22px;
    height: 22px;
    position: absolute;
    left: 40px;
    top: -80%;
    background: url("@/assets/icons/ai-translate/flower.svg") no-repeat center;
    animation: rotate 8s linear infinite;
  }

</style>

<script>
  import AiTranslateTrack from '@/track/AiTranslateTrack';
  import {getRegionPath} from '../../utils/util';

  registerEvent()

  function registerEvent() {
    const elList = document.querySelectorAll<HTMLElement>('.try-now-btn')
    elList.forEach(i => {
      i.onclick = () => {
        AiTranslateTrack.clickStartNow()
        window.open(getRegionPath() + '/ai-translate-start', '_self')
      }
    })
  }
</script>

---
import Footer from '@/component/footer-v2/Footer.vue'
import Layout from '@/layouts/EasyLayout.astro'
import Header from "@/component/header-v2/Header.vue"
import CommentsSection from '@/component/youtube/product/CommentsSection.astro';
import FAQSection from '@/component/youtube/product/FAQSection.astro';
import LastScreenSection from '@/component/youtube/product/LastScreenSection.astro';
import {useI18n} from "@/locales";
import avatarIcon1 from '@/assets/images/ai-subtitle-translator/userAvatar1.webp'
import avatarIcon2 from '@/assets/images/ai-subtitle-translator/userAvatar2.webp'
import avatarIcon3 from '@/assets/images/ai-subtitle-translator/userAvatar3.webp'
import avatarIcon4 from '@/assets/images/ai-subtitle-translator/userAvatar4.webp'
import avatarIcon5 from '@/assets/images/ai-translate/translate-cn-user1.png'
import avatarIcon6 from '@/assets/images/ai-translate/translate-cn-user2.png'
import avatarIcon7 from '@/assets/images/ai-translate/translate-cn-user3.png'
import avatarIcon8 from '@/assets/images/ai-translate/translate-cn-user4.png'
import step1Icon from '../../assets/images/audio-to-text-online/steps/step1.svg';
import step2Icon from '../../assets/images/audio-to-text-online/steps/step2.svg';
import step3Icon from '../../assets/images/audio-to-text-online/steps/step3.svg';
import StarIcon from '~icons/ai-youtube/star-icon.svg'
import Advanced1Icon from '~icons/ai-clip-maker/advanced-1.svg';
import Advanced2Icon from '~icons/ai-clip-maker/advanced-2.svg';
import Advanced3Icon from '~icons/ai-clip-maker/advanced-3.svg';
import {getLang} from '@central/i18n';
import UseStepSection from '../../component/youtube/product/UseStepSection.astro';
import AiClipMakerV from '../../component/ai-clip-maker/AiClipMaker.vue';
import itemBgIcon from '../../assets/images/ai-clip-maker/bg-icon.png';
import itemBgText from '../../assets/images/ai-clip-maker/bg-text.png';
import caseBg1 from '../../assets/images/ai-clip-maker/user-case/img1.webp';
import caseBg2 from '../../assets/images/ai-clip-maker/user-case/img2.webp';
import caseBg3 from '../../assets/images/ai-clip-maker/user-case/img3.webp';
import caseBg4 from '../../assets/images/ai-clip-maker/user-case/img4.webp';
import caseBg5 from '../../assets/images/ai-clip-maker/user-case/img5.webp';
import caseBg6 from '../../assets/images/ai-clip-maker/user-case/img6.webp';
import {Picture} from '@central/assets/imagetools';
import startIcon1 from '../../assets/images/ai-clip-maker/main/star1.svg'
import startIcon2 from '../../assets/images/ai-clip-maker/main/star2.svg'
import startIcon3 from '../../assets/images/ai-clip-maker/main/star3.svg'
import startIcon4 from '../../assets/images/ai-clip-maker/main/star4.svg'
import bgVideo from '../../assets/videos/ai-clip-bg1.mp4'
import CacheVideo from '@/component/global/CacheVideo.vue';

const {t} = useI18n();
const lang = getLang()
const isZhTwJp = lang === 'zh' || lang === 'tw' || lang === 'jp'

const userCaseList = [
  {
    title: t('aiClipUseCasesContent1'),
    desc: t('aiClipUseCasesContent2'),
    img: caseBg1
  },
  {
    title: t('aiClipUseCasesContent3'),
    desc: t('aiClipUseCasesContent4'),
    img: caseBg2
  },
  {
    title: t('aiClipUseCasesContent5'),
    desc: t('aiClipUseCasesContent6'),
    img: caseBg3
  },
  {
    title: t('aiClipUseCasesContent7'),
    desc: t('aiClipUseCasesContent8'),
    img: caseBg4
  },
  {
    title: t('aiClipUseCasesContent9'),
    desc: t('aiClipUseCasesContent10'),
    img: caseBg5
  },
  {
    title: t('aiClipUseCasesContent11'),
    desc: t('aiClipUseCasesContent12'),
    img: caseBg6
  }
]

const stepList = [
  {
    icon: step1Icon,
    title: t('aiClipStepsContent1'),
    description: t('aiClipStepsContent2')
  },
  {
    icon: step2Icon,
    title: t('aiClipStepsContent3'),
    description: t('aiClipStepsContent4')
  },
  {
    icon: step3Icon,
    title: t('aiClipStepsContent5'),
    description: t('aiClipStepsContent6')
  }
];

const advancedList = [
  {

    icon: Advanced1Icon,
    title: t('aiClipWhyContent1'),
    description: t('aiClipWhyContent2')
  },
  {
    icon: Advanced2Icon,
    title: t('aiClipWhyContent3'),
    description: t('aiClipWhyContent4')
  },
  {
    icon: Advanced3Icon,
    title: t('aiClipWhyContent5'),
    description: t('aiClipWhyContent6')
  },
];

const commentsList = [
  {
    comment: t('aiClipUserDesc'),
    name: t('aiClipUserContent1'),
    avatar: isZhTwJp ? avatarIcon6 : avatarIcon1
  },
  {
    comment: t('aiClipUserContent2'),
    name: t('aiClipUserContent3'),
    avatar: isZhTwJp ? avatarIcon8 : avatarIcon2
  },
  {
    comment: t('aiClipUserContent4'),
    name: t('aiClipUserContent5'),
    avatar: isZhTwJp ? avatarIcon7 : avatarIcon3
  },
  {
    comment: t('aiClipUserContent6'),
    name: t('aiClipUserContent7'),
    avatar: isZhTwJp ? avatarIcon5 : avatarIcon4
  },
]

const QAList = [
  {
    question: t('aiClipQADesc'),
    answer: t('aiClipQAContent1')
  },
  {
    question: t('aiClipQAContent2'),
    answer: t('aiClipQAContent3')
  },
  {
    question: t('aiClipQAContent4'),
    answer: t('aiClipQAContent5')
  },
  {
    question: t('aiClipQAContent6'),
    answer: t('aiClipQAContent7')
  },
  {
    question: t('aiClipQAContent8'),
    answer: t('aiClipQAContent9')
  },
]
---

<Layout theme="dark">
  <Header client:load/>

  <main class="w-full font-['Poppins',var(--body-font)]">

    <section class="relative bg-black pt-header-offset">
      <!-- 背景图片 -->
      <div class="absolute inset-0 z-0">
        <Picture
          src='src/assets/images/ai-clip-maker/main/main-bg.png'
          alt="background"
          width={1920}
        />
      </div>

      <div class="relative size-full z-10 pt-[50px] lg:pt-[120px]">
        <div class="flex-center size-full flex-col items-center">
          <!--标题和说明-->
          <div class="w-90% max-w-[1200px] z-10 relative">
            <div class="relative">
              <h1
                class="text-[32px] lg:text-[54px] text-center text-white font-bold gap-[24px] leading-[2] lg:leading-normal"
                set:html={t('aiClipTitle').replace('<i>', `<i class="gradient-text not-italic">`)}
              >
              </h1>
              <img class="star1 size-[24px] absolute top-0 left-0 select-none" src={startIcon1} alt="star icon" draggable="false">
              <img class="star2 size-[20px] absolute -top-[60px] left-[10vw] select-none" src={startIcon2} alt="star icon" draggable="false">
              <img class="star4 size-[24px] absolute top-1/2 -right-[4vw] select-none" src={startIcon4} alt="star icon" draggable="false">
            </div>


            <p
              class="text-[14px] lg:text-[24px] text-center mt-[14px] lg:mt-[30px] lg:text-white text-white/[0.8] relative"
            >
              {t('aiClipDesc')}
              <img class="star3 size-[24px] absolute right-0 bottom-0 select-none" src={startIcon3} alt="star icon" draggable="false">
            </p>
          </div>

          <div class="mt-[30px] lg:mt-[60px]">
            <AiClipMakerV client:load/>
          </div>

          <section class="w-90% max-w-[1500px] relative z-10 mt-[60px] lg:mt-[180px]">
            <CacheVideo
              client:visible
              class="w-full object-cover"
              src={bgVideo} autoplay loop muted playsinline
            />
          </section>

          <section class="w-90% max-w-[1500px] relative z-10 mt-[60px] lg:mt-[180px]">
            <h2 class="text-[28px] lg:text-[48px] font-semibold text-white text-center">{t('aiClipUseCasesTitle')}</h2>
            <p class="text-center text-[20px] text-white/[0.4] mt-[24px]">{t('aiClipUseCasesDesc')}</p>
            <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[16px] lg:gap-[30px] mt-[30px] lg:mt-[60px]">
              {
                userCaseList.map((item) => (
                  <li
                    class="bg-[#B9ECFF]/[0.12] rounded-[16px] backdrop-blur-[40px] text-white relative overflow-hidden group hover:scale-[1.02] transition-all duration-300 ease-in-out"
                  >
                    <img class="w-[478px] aspect-video" src={item.img} alt={item.title} loading="lazy"/>
                    <div class="p-[30px]">
                      <p class="text-[16px] lg:text-[20px] font-bold">{item.title}</p>
                      <p class="text-[14px] lg:text-[16px] mt-[16px] opacity-60">{item.desc}</p>
                    </div>
                  </li>
                ))
              }
            </ul>

            <button
              class="gradient-button-auto-theme try-now-btn min-w-[230px] h-[64px] px-[58px] py-[18px] text-[22px] font-semibold flex gap-[8px] mt-[70px] mx-auto"
            >
              <span>{t('aiClipStartMakeNow')}</span>
              <StarIcon/>
            </button>
          </section>


          <section class="w-90% max-w-[1200px] relative z-10 mt-[60px] lg:mt-[180px]">
            <h2 class="text-[28px] lg:text-[48px] font-semibold text-white text-center">{t('aiClipWhyTitle')}</h2>
            <p class="text-center text-[20px] text-white/[0.4] mt-[24px]">{t('aiClipWhyDesc')}</p>
            <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[16px] lg:gap-[30px] mt-[30px] lg:mt-[60px]">
              {
                advancedList.map((item) => (
                  <li
                    class="bg-[#B9ECFF]/[0.12] rounded-[16px] backdrop-blur-[40px] p-[30px] text-white relative overflow-hidden group hover:scale-[1.02] transition-all duration-300 ease-in-out"
                  >
                    <div class="flex items-center">
                      <div class="mr-[8px]">
                        <item.icon/>
                      </div>
                      <p class="text-[16px] lg:text-[20px] font-semibold">{item.title}</p>
                    </div>

                    <p class="text-[14px] lg:text-[16px] mt-[18px] opacity-60">{item.description}</p>

                    <img
                      class="hidden group-hover:block absolute top-0 -right-[20px] z-0 select-none"
                      src={itemBgIcon}
                      alt="bg img"
                      draggable="false"
                    />
                    <img
                      class="hidden group-hover:block absolute bottom-0 left-0 translate-y-1/2 z-0 select-none"
                      src={itemBgText}
                      alt="bg img"
                      draggable="false"
                    />
                  </li>
                ))
              }
            </ul>
          </section>

          <!-- 使用步骤 -->
          <UseStepSection
            title={t('aiClipStepsTitle')}
            stepList={stepList}
          >
            <p class="text-center text-[20px] text-white/[0.4] mt-[24px]">{t('aiClipStepsDesc')}</p>
          </UseStepSection>
          <button
            class="gradient-button-auto-theme try-now-btn min-w-[230px] h-[64px] px-[58px] py-[18px] text-[22px] font-semibold flex gap-[8px] mt-[70px]"
          >
            <span>{t('aiClipStartMakeNow2')}</span>
            <StarIcon/>
          </button>
        </div>
      </div>
    </section>


    <CommentsSection
      title={t('aiClipUserTitle').replace('<i>', '<div class="section-title-div inline relative"><i class="section-title-light gradient-text not-italic relative">').replace('</i>', '</i></div>')}
      commentsList={commentsList}
    />
    <FAQSection
      title={t('aiClipQATitle')}
      QAList={QAList}
    />
    <LastScreenSection
      title={t('aiClipLastStartTitle')}
      desc={t('aiClipLastStartDesc')}
      btnTitle={t('aiClipStartMakeNow3')}
    />
  </main>

  <Footer client:idle/>
</Layout>


<script>
  import AiClipMakerTrack from '../../track/AiClipMakerTrack';

  const startButtonElList = document.querySelectorAll<HTMLAnchorElement>('.try-now-btn')
  startButtonElList.forEach(i => {
    i.onclick = () => {
      AiClipMakerTrack.clickStartNow().then()
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  })
</script>


<style>
  .star1 {
    animation: breathe 2s infinite alternate ease-out;
  }

  .star2, .star4 {
    animation: float-move 2s infinite alternate ease-in-out;
  }

  .star3 {
    animation: star-rotate 20s infinite linear;
    transform: translateY(100%);
  }

  @keyframes float-move {
    0% {
      translate: 0 10px;
    }
    100% {
      translate: 0 -10px;
    }
  }

  @keyframes star-rotate {
    0% {
      transform: translateY(100%) rotate(0deg);
    }
    100% {
      transform: translateY(100%) rotate(360deg);
    }
  }

  @keyframes breathe {
    0% {
      scale: 1.2;
    }
    100% {
      scale: 0.4;
    }
  }
</style>

<style is:global>

  header .header-login-status.logined {
    display: flex;
  }

  @media screen and (max-width: 1024px) {
    header .header-login-status.logined .header-login-avatar {
      width: 30px;
      height: 30px;
    }
  }

  .gradient-text {
    color: white;
    background: linear-gradient(to right, #D6FB72, #76F9B1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
  }

  .line-tilde::after {
    content: '';
    width: 100%;
    height: 16px;
    position: absolute;
    left: 100%;
    top: 100%;
    transform: translate(-100%, -50%);
    background: url("@/assets/images/ai-translate/line.png") no-repeat center;
  }

  @media screen and (max-width: 1024px) {
    .line-tilde::after {
      display: none;
    }
  }

  @keyframes like {
    0% {
      transform: translate(-100%, -100%) rotate(25deg);
    }

    100% {
      transform: translate(-100%, -100%) rotate(0deg);
    }
  }

  @keyframes breathe {
    0% {
      transform: translate(-100%, -100%) scale(1);
    }

    100% {
      transform: translate(-100%, -100%) scale(0.2);
    }
  }

  @keyframes userBreath {
    0% {
      transform: translate(100%, -100%) scale(1);
    }

    100% {
      transform: translate(100%, -100%) scale(0.2);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .section-title-light::before {
    content: '';
    position: absolute;
    left: unset;
    right: 0;
    top: 0;
    width: 14px;
    height: 14px;
    background-color: #56E8B3;
    animation: breathe 2s infinite alternate;
    border-radius: 14px 14px 14px 0;
    transform-origin: left bottom;
    transform: translate(100%, -100%);
    animation-name: userBreath;
  }

  .section-title-light::after {
    content: '';
    width: 28px;
    height: 40px;
    position: absolute;
    left: 0;
    top: 0;
    transform: translate(-100%, -100%);
    background: url("@/assets/icons/ai-translate/user-good.svg") no-repeat center;
    animation: like 1s infinite alternate;
    transform-origin: left bottom;
  }

  .section-title-div::before {
    content: '';
    width: 22px;
    height: 22px;
    position: absolute;
    left: 40px;
    top: -80%;
    background: url("@/assets/icons/ai-translate/flower.svg") no-repeat center;
    animation: rotate 8s linear infinite;
  }

  .user-step-section-img {
    width: 90px;
    height: 90px;
  }

  @media screen and (max-width: 1024px) {
    .user-step-section-img {
      width: 72px;
      height: 72px;
    }
  }

</style>


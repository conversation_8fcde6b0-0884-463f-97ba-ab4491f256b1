---
import EasyLayout from '../../layouts/EasyLayout.astro'
import Main from '../../component/chat-video/Main.astro'
import Second from '../../component/chat-video/Second.astro'
import Header from "@/component/header-v2/Header.vue"
import Footer from "@/component/footer-v2/Footer.vue";
import FeatChatVideoList from "@/component/chat-video/FeatChatVideoList.astro"
import FeatCommonQuestion from "../../component/chat-video/FeatCommonQuestion.vue"
import FeatSToolScene from "../../component/chat-video/FeatToolScene.astro"
import {useI18n} from '../../locales'
const {t} = useI18n()
---

<EasyLayout>
  <Header client:load/>
  <Main/>
  <!--开始和您的视频对话吧!-->
  <Second/>
  <!--AI视频对话工具适用场景-->
  <section
    class='relative py-160px bg-[url(@/assets/images/chat-video/chat-video-feat/feat-chatvideo-third.svg)] bg-cover'>
    <div class="flex flex-col items-center">
      <h2
        class="mx-[auto] font-bold text-[40px] text-deep-color mb-[90px] relative"
        set:html={t('aiChatNeeds')}
      >

      </h2>
      <FeatSToolScene/>
      <button class="mt-80px toggleButton gradient-button min-w-[260px] h-[70px] flex-center">
        <span class="flex">
          <span class="text-28px font-bold">{t("mainStartForFree")}</span>
        </span>
      </button>
    </div>
  </section>
  <!--常见问题-->
  <section
    class='relative py-160px bg-[url(@/assets/images/chat-video/chat-video-feat/feat-chatvideo-four.svg)] bg-cover'>
    <div class='flex flex-col items-center'>
      <h2 class='mx-[auto] text-[40px] font-bold text-deep-color'>
        {t('frequentlyQuestions')}
      </h2>
      <FeatCommonQuestion client:visible class='mt-86px'/>
      <button class="mt-80px toggleButton gradient-button min-w-[260px] h-[70px] flex-center">
        <span class="flex">
          <span class="text-28px font-bold">{t("mainStartForFree")}</span>
        </span>
      </button>
    </div>
  </section>
  <!--录咖 — 一站式音视频处理平台-->
  <section
    class='relative py-160px bg-[url(@/assets/images/chat-video/chat-video-feat/feat-chatvideo-last.svg)] bg-cover'>
    <div class="relative z-1">
      <h2 class="section-title small-lang-size">
        <span class='main-title'>{t('RecCloud')}</span>
        <span class='text-deep-color'>— </span>
        <span class='text-deep-color'>{t('featOnePlatLeft')}</span>
        <span class='bg-light-color bg-opacity-10 pl-4px ml-[-6px]'>
          <span class='main-title'>{t('featOnePlatCenter')}</span>
        </span>
        <span class='text-deep-color'>{t('featOnePlatRight')}</span>
      </h2>

      <FeatChatVideoList class='mt-90px'/>
    </div>
  </section>
  <Footer client:idle/>
</EasyLayout>

<style is:global>
  .start-chatVideo {
    @apply relative text-primary-color px-[5px]
  }

  .aiChat-needs {
    @apply text-primary-color bg-light-color bg-opacity-10 pl-[5px] relative
  }

  .chatVideo-feat-title-light::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 14px;
    height: 14px;
    background-color: #18AD25;
    transform-origin: right bottom;
    transform: translate(-100%, -100%);
    border-radius: 14px 14px 0 14px;
    animation: feat-breathe 2s infinite alternate;
  }

  @keyframes feat-breathe {
    0% {
      transform: translate(-100%, -100%) scale(1);
    }
    100% {
      transform: translate(-100%, -100%) scale(0.2);
    }
  }
</style>

<style lang="scss">
  @import "../../style/common.scss";

  .the-header {
    background-color: transparent !important;
    border-bottom-color: transparent !important;
  }

  :global(.the-header:not(.active) .header-bg-container) {
    background-color: transparent;
    border-bottom-color: transparent;
  }

  @media screen and (max-height: 750px) {
    :global(.flex.scale) {
      scale: 0.7;
      transform-origin: top;
      margin-bottom: 0;
    }
  }

  .main-title {
    background: linear-gradient(92deg, #56E8B3 0.54%, #18AD25 14.89%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .section-title {
    @apply font-bold text-[40px] text-center px-[24px];
  }

  .small-lang-size {
    &:lang(es) {
      @media screen and (max-width: 1366px) {
        font-size: 36px;
      }
    }

    &:lang(de) {
      @media screen and (max-width: 1366px) {
        font-size: 28px;
      }
    }

    &:lang(pt) {
      @media screen and (max-width: 1366px) {
        font-size: 36px;
      }
    }
  }
</style>

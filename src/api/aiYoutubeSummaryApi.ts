import http from '@/services/http';
import {BaseTaskQueryType, SrtResultModel} from '@/interface/OnlineEdit';
import CreateTaskError from '@/services/exception/CreateTaskError';
import useLoginService from '@/services/useLoginService';
import {getLang} from '@central/i18n';
import {AiSummaryHasSubtitle, SummaryTaskItem} from '@/interface/AiYoutube';

const {isLoginRef} = useLoginService()

export async function createSummaryTaskApi(url: string, deviceId: string, freeTime?: number) {

  const _baseUrl = isLoginRef.value ? '/ai/av/chat-video/youtube' : '/open/ai/av/chat-video/youtube'

  const result = await http.post<{ task_id: string }>(`${_baseUrl}`, {
    url: url,
    device_id: deviceId,
    source: 'web',
    truncation_at: freeTime ?? 0,
    app_lang: getLang()
  }, {
    timeout: 60000,
  })

  if (result.status === 200) {
    return result.data.task_id
  }
  throw new CreateTaskError(result.message, result)
}

export async function querySummaryTaskApi(task_id: string) {
  interface QuerySummaryTaskResult extends BaseTaskQueryType {
    task_id: string,
    summary: string,
    duration: number,
    subtitles: Array<SrtResultModel>
    error: {
      status: number,
      message: string
    },
    has_subtitle: AiSummaryHasSubtitle,
    title: string,
  }

  const _baseUrl = isLoginRef.value ? '/ai/av/chat-video/youtube' : '/open/ai/av/chat-video/youtube'

  const result = await http.get<QuerySummaryTaskResult>(`${_baseUrl}/${task_id}`)
  return result.data
}

export async function getYoutubeSummarizerEquityApi(deviceId: string) {
  const _baseUrl = isLoginRef.value ? '/ai/av/chat-video/equity' : '/open/ai/av/chat-video/equity'

  interface EquityResponse {
    youtube: {
      limit: number,
      used: number,
      price: number
    }
  }

  const result = await http.get<EquityResponse>(`${_baseUrl}`, {
    device_id: deviceId,
    source: 'web',
  })

  return result.data
}


export async function getSummaryTaskListApi(page: number, deviceId: string) {
  const baseUrl = isLoginRef.value ? '/ai/av/chat-video/youtube' : '/open/ai/av/chat-video/youtube'
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: '20',
    order_column: 'id',
    device_id: deviceId,
    source: 'web',
  })

  interface SummaryTaskListModel {
    items: SummaryTaskItem[],
    total: number,
  }

  const result = await http.get<SummaryTaskListModel>(`${baseUrl}?${params}`)
  if (result.status === 200) {
    return result.data
  }
}

export async function deleteSummaryTaskApi(taskId: string, deviceId: string) {
  const url = isLoginRef.value ? `/ai/av/chat-video/youtube/${taskId}` : `/open/ai/av/chat-video/youtube/${taskId}`
  const result = await http.delete(url, {
    device_id: deviceId,
  })
  return result.status === 200
}

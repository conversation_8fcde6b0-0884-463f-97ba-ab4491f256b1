import http from "@/services/http";
import { baseUrl } from '@/services/constants';
import useLoginService from "@/services/useLoginService";
import { useI18n } from "@/locales";
import { GroupDetailData, GroupMemberData, GroupVideoData } from '@/interface/group';
import { getRegionPath } from "@/utils/util";

const { t } = useI18n();
const { isLoginRef, userIdRef} = useLoginService()

/**
 * 移除群成员
 */
export async function removeMember(uniqid: string, tip:any, removeId:number|null): Promise<void>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    const result = await http.delete<null>(baseUrl() + '/group/' + uniqid +'/members', {
        user_id : removeId ? removeId :userIdRef.value
    })
     if (result.status == 200) {
        if (removeId) {
            tip.value.success(t("Remove successfully"));
        } else {
            tip.value.success(t("Exit successfully"));
            if (location.href.includes('manage')) {
                location.href = getRegionPath() + '/group';
            }
        }
    }else {
        throw new Error(result.message.toString())
    }
}

/**
 * 群组设置
 */
export async function saveGroupSetting(uniqid: string | undefined, name:string| undefined, tip:any, desc:string | undefined, avatorId: string, close: Function): Promise<void>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    const params = {
        name,
    }
    if (desc) {
        Object.assign(params, {
            desc
        })
    }
    if (avatorId) {
        Object.assign(params, {
            avatar_id:avatorId
        })
    }
    await http.put<null>(baseUrl() + '/group/' + uniqid , params).then(() => {
        tip();
        close();
    })
}

/**
 * 群组详情
 */
export async function getGroupDetail(uniqid:string): Promise<GroupDetailData>{
    return await http.get<GroupDetailData>(baseUrl() + '/group/' + uniqid).then((res) => {
        return res.data;
    })
}

/**
 * 群组分享链接信息
 */
export async function getGroupSharedDetail(uniqid:string, user_id:number | undefined): Promise<GroupDetailData>{
    return await http.get<GroupDetailData>(baseUrl() + '/open/group/' + uniqid, { user_id }).then((res) => {
        return res.data;
    })
}

/**
 * 申请进群
 */
export async function applyJoinGroup(uniqid:string): Promise<number>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    return await http.post(baseUrl() + '/group/' + uniqid + '/apply-join').then((res) => {
        return res.status;
    }).catch(() => {
        throw new Error();
    }
    )
}

/**
 * 获取成员列表
 */
export async function getMemberList(uniqid:string): Promise<GroupMemberData>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    return await http.get<GroupMemberData>(baseUrl() + '/group/' + uniqid + '/members').then((res) => {
        return res.data;
    })
}

/**
 * 入群申请审核
 */
export async function groupInviteApproval(state:number,uniqid:string, tip:any, userId:Number): Promise<void>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    //TODO:
    const params = {
        joined_state: state,
        approval_user_id: userId,
        approval_type: 0,
    }
    return await http.post(baseUrl() + '/group/' + uniqid + '/approval', params).then((res) => {
        if (state === 1) {
            tip.value.success(t("Add member successfully"));
        } else {
            tip.value.success(t("Rejected to join"));
        }
    })
}

/**
 * 分享至群组的播放列表|视频 列表数据
 */
export async function groupVideoList(uniqid:string, keywords:string | null): Promise<GroupVideoData>{
    let params={}
    if (keywords) {
        Object.assign(params, {
            keywords,
        })
    }
    return await http.get<GroupVideoData>(baseUrl() + '/group/' + uniqid + '/playlist-videos',params).then((res) => {
        return res.data;
    })
}

/**
 * 播放列表|视频 取消分享到群组
 */
export async function cancelShareGroup(uniqid:string, type:number, groupId:string, tip:any): Promise<void>{
    if(!isLoginRef.value){
        throw new Error('未登录')
    }
    const params = {
        type,
        target_uniqid:uniqid
    }
    return await http.put(baseUrl() + '/group/' + groupId + '/close-share', params).then((res) => {
       tip.value.success(t('Cancel succeeded'))
    })
}

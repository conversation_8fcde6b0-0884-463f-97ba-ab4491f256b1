// @ts-ignore
import * as OSS from 'ali-oss/dist/aliyun-oss-sdk.js';
import http from "@/services/http";
import {apBaseUrl} from '@/services/constants';
import {OssAuthResult, ProgressType} from '@/interface/OSS';

export interface UploadResultType {
  filename?: string,
  resource_id?: string
  size?: number
  type?: string
  url?: string
  uniqid?: string//上传录咖和云端选择才有id,
  uri?: string,
  duration?: number,
}

interface UploadOptions {
  progress?: ProgressType,
  uploadEnd?: (result: UploadResultType) => void
  apiKey: string
}

/**
 * 上传文件
 */
export async function uploadFileToAp(file: File, options: UploadOptions) {
  const authParams = await getApOssAccessKey(file.name, options.apiKey);
  const store = new OSS.default(authParams);
  let callbackUrl = authParams.callback.url;
  let callbackBody = authParams.callback.body.replace(
    "${filename}",
    encodeURI(file?.name.replace(/\s*/g, ""))
  );
  let uploadCallback = {
    url: callbackUrl,
    body: callbackBody
  };
  let uploadOptions = {
    cancelFlag: true,
    progress: options?.progress,
    callback: uploadCallback,
    partSize: 1000 * 128, //设置分片大小
    timeout: 120000, //设置超时时间
  };
  const res = await store.multipartUpload(authParams.objects[file.name], file, uploadOptions)
  options?.uploadEnd?.(res.data.data)
}

/**
 * 获取oss授权
 * 未登录时直接使用录咖上传会出现400 后台要求使用AP站上传模式
 */
async function getApOssAccessKey(name: string, apiKey: string): Promise<OssAuthResult> {
  const formData = new FormData();
  formData.append('task_type', '201');
  formData.append('filenames[]', name);

  return await http.post<any>(apBaseUrl() + '/authorizations/oss', formData, {
      header: {"X-API-KEY": apiKey}
    }
  ).then((res) => {
    const {credential, bucket, endpoint, callback, objects, accelerate} = res.data;
    return {
      accessKeyId: credential.access_key_id,
      accessKeySecret: credential.access_key_secret,
      stsToken: credential.security_token,
      bucket,
      endpoint: endpoint,
      callback,
      objects
    };
  })
}

import {OssAuthResult, ProgressType} from '@/interface/OSS';
import http from '@/services/http';
import {getLang} from '@central/i18n';
//@ts-ignore
import * as OSS from 'ali-oss/dist/aliyun-oss-sdk.js';

import {ResponseType} from '@/interface/ResponseType';

interface UploadOptions {
  onProgress?: ProgressType,
  customHeader?: Record<string, any>,
  signal?: AbortSignal,
}

export interface UploadResultType {
  duration: number,
  filename: string,
  resource_id: string,
  size: number,
  uniqid: string,
  uri: string,
  url: string,
  cover_url: string,
}

export default class UploadApi {

  //获取oss授权
  private static async _getOssAccessKey(filename: string, header?: Record<string, any>) {
    const res = await http.post<any>('/authorizations/oss', {
        region: getLang() !== "zh" ? "hk" : "",
        filenames: filename,
      }, {
        header: header
      }
    )
    const {credential, bucket, endpoint, callback, objects, accelerate} = res.data;
    return {
      accessKeyId: credential.access_key_id,
      accessKeySecret: credential.access_key_secret,
      stsToken: credential.security_token,
      bucket,
      endpoint: endpoint,
      callback,
      objects
    } as OssAuthResult;
  }

  private static async _baseUpload() {

  }

  /**
   * 上传不保存录咖
   * @param file
   * @param options
   */
  public static async uploadFileWithNoToken(file: File, options?: Omit<UploadOptions, 'customHeader'>) {
    return this.uploadFileToRecCloud(file, {
      ...options,
      customHeader: {
        authorization: null
      }
    })
  }

  /**
   * @desc 上传到录咖
   * 如果需要在我的空间列表显示需要调用[putVideoInfo]方法
   * @see putVideoInfo
   */
  public static async uploadFileToRecCloud(file: File, options?: UploadOptions): Promise<UploadResultType> {
    const authParams = await this._getOssAccessKey(file.name, options?.customHeader);
    const store = new OSS.default(authParams);
    const callbackUrl = authParams.callback.url;
    const callbackBody = authParams.callback.body.replace("${filename}", encodeURI(file?.name.replace(/\s*/g, "")));
    let abortCheckpoint: Record<string, any>;
    const uploadOptions = {
      cancelFlag: true,
      callback: {
        url: callbackUrl,
        body: callbackBody
      },
      partSize: 1024 * 1024 * 2, //设置分片大小 2MB
      timeout: 120000, //设置超时时间
      progress: (progress: number, cpt: any, res: any) => {
        options?.onProgress?.(progress, cpt, res)
        if (cpt) {
          abortCheckpoint = cpt
        }
      },
    };
    if (options?.signal) {
      options.signal.addEventListener('abort', () => {
        if (abortCheckpoint) {
          store.abortMultipartUpload(abortCheckpoint.name, abortCheckpoint.uploadId);
        }
      })
    }
    const res = await store.multipartUpload(authParams.objects[file.name], file, uploadOptions)
    const response = res.data as ResponseType<UploadResultType>
    if (response.status === 200) {
      return response.data
    } else {
      throw response
    }
  }
}

/// <reference path="../.astro/types.d.ts" />
/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 重写 @central/i18n 的类型定义，使其更宽松
declare module '@central/i18n' {
  export function createUsei18n<T>(): () => {
    t: (key: keyof T, params?: any) => string;
    scopedT: any;
    trans: any;
  };

  export function getLang(): string;

  export function isClient(): boolean;
}

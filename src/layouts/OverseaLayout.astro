---
import type {ThemeModeType} from '../interface/Global';
import {SEO} from 'astro-seo'
import {Tracking} from "@central/tracking/components";
import {createMetaRecord} from '@central/metadata'
import EnterPageUpload from '../component/global/EnterPageUpload.astro'
import {getLang, getPageUrl, getSiteHost} from "@central/i18n/utils";
import {getCdnUrl} from '../utils/util';
import LinkingData from '@/component/LinkingData.astro';
import RightBar from '@/component/RightBar.astro';
import GoogleCookie from '@/component/global/GoogleCookie.astro';
import CookieTips from '../component/global/CookieTips.astro';
import MobileDownloadTips from '../component/global/MobileDownloadTips.vue';
import process from "process";
const faviconUrl = getCdnUrl('/local/reccloud.cn/img/reccloud-favicon.png')
const getMetadata = await createMetaRecord(Astro)
const hostname = "reccloud";
const site = getSiteHost(hostname, Astro);

const pathname = getPathname();
const seoCanonical = `https://reccloud.com${pathname}`

//mobile目录下的都不需要被索引
const enableIndexFollow = pathname.includes('/mobile/') === false

function getPathname() {
  const pageUrl = getPageUrl(Astro, false);
  const pathname = new URL(pageUrl).pathname
  const lang = getLang(Astro)
  if (lang === 'en') {
    return pathname
  } else {
    return `/${lang}${pathname}`
  }
}

interface Props {
  lang?: string
  isLandingPage?: boolean
  theme?: ThemeModeType
}

const seoTitle = getMetadata('Title')
const seoDescription = getMetadata('Description')
const seoKeywords = getMetadata("Keywords")

const {lang = getLang(Astro), isLandingPage, theme = 'light'} = Astro.props as Props

const seoTags = {
  link: [
    {rel: 'icon', href: faviconUrl},
    {rel: 'apple-touch-icon', href: faviconUrl},
    {rel: 'bookmark', href: faviconUrl},
    {rel: 'Shortcut Icon', href: faviconUrl},
  ],
  meta: [
    seoKeywords && {name: 'keywords', content: seoKeywords},
  ].filter(Boolean),
}
console.log(process.env["npm_lifecycle_event"])
---

<html lang={lang} class:list={[theme === 'light' ? 'light' : 'dark']}>
<head>
  <meta charset='utf-8'/>
  {
    process.env["npm_lifecycle_event"] === "dev" && (
      <meta name="build-time" content={new Date().toLocaleString('zh')}/>
    )
  }
  <meta name="google-site-verification" content="tnzaYmySagBXkPQp6s4GcfPJuwWX_o2kvXue-qhIzLw"/>
  <meta name="referrer" content="no-referrer-when-downgrade"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel='preconnect' href='https://fonts.googleapis.com'/>
  <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin/>
  <link
    href='https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@300;400;600&display=swap'
    rel='stylesheet'
  />
  <SEO
    title={seoTitle}
    description={seoDescription}
    nofollow={!enableIndexFollow}
    noindex={!enableIndexFollow}
    canonical={seoCanonical}
    openGraph={{
      basic: {
        title: seoTitle,
        type: 'website',
        image: faviconUrl,
        url: seoCanonical,
      },
      optional: {
        description: seoDescription,
        siteName: seoTitle,
        locale: lang,
      },
      image: {
        url: faviconUrl,
        width: 121,
        height: 121,
        alt: seoTitle,
      }
    }}
    twitter={{
      card: 'summary_large_image',
      title: seoTitle,
      description: seoDescription,
    }}
    extend={seoTags}
  />

  <LinkingData
    hostname={hostname}
    isLandingPage={isLandingPage}
    logoUrl={faviconUrl}
  />
  <Tracking
    globalGtmId='GTM-MCFLF92'
    gtmId='GTM-NMWVJP6'
    gaId='G-HHDQZ4SPZ1'
  />
  <GoogleCookie/>
  <style>
    :root {
      --header-height: 54px;

      @media screen and (min-width: 1024px) {
        --header-height: 72px;
      }

      --body-font: 'Microsoft YaHei', '微软雅黑', 'Open sans', Arial, sans-serif;
    }

    body {
      font-family: var(--body-font), serif;
    }
  </style>
  <style is:global>
    .text-white {
      color: rgb(255, 255, 255);
    }

    input:focus-visible {
      outline: none;
    }

    textarea:focus-visible {
      outline: none;
    }
  </style>
</head>
<body>
  <slot/>
<EnterPageUpload/>
<RightBar/>
<CookieTips/>
<MobileDownloadTips client:only="vue"/>
</body>
</html>

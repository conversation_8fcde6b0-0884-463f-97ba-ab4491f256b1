---
import type {ThemeModeType} from '../interface/Global';
import {SEO} from 'astro-seo'
import {Tracking} from "@central/tracking/components"
import {createMetaRecord} from '@central/metadata'
import EnterPageUpload from '../component/global/EnterPageUpload.astro'
import {getLang, getPageUrl} from "@central/i18n/utils";
import {getCdnUrl} from '../utils/util';
import LinkingData from '@/component/LinkingData.astro';
import RightBar from '@/component/RightBar.astro';
import GoogleCookie from '@/component/global/GoogleCookie.astro';
import CookieTips from '../component/global/CookieTips.astro';
import MobileDownloadTips from '../component/global/MobileDownloadTips.vue'
const getMetadata = await createMetaRecord(Astro)

interface Props {
  lang?: string
  isLandingPage?: boolean,
  theme?: ThemeModeType
}

const faviconUrl = getCdnUrl('/local/reccloud.cn/img/reccloud-favicon.png')
const {lang = getLang(Astro), isLandingPage, theme = 'light'} = Astro.props as Props

const seoTitle = getMetadata('Title')
const seoDescription = getMetadata('Description')
const seoKeywords = getMetadata('Keywords')


const pageUrl = getPageUrl(Astro);
const pathname = new URL(pageUrl).pathname;
const pathnameWithoutLang = pathname.replace(new RegExp(`^/zh(/?.*)`), "$1");
const seoCanonical = `https://reccloud.cn${pathnameWithoutLang}`
const hostname = "录咖";

//mobile目录下的都不需要被索引
const enableIndexFollow = pathname.includes('/mobile/') === false

const seoTags = {
  link: [
    {rel: 'icon', href: faviconUrl},
    {rel: 'apple-touch-icon', href: faviconUrl},
    {rel: 'bookmark', href: faviconUrl},
    {rel: 'Shortcut Icon', href: faviconUrl},
  ],
  meta: [
    seoKeywords && {name: 'keywords', content: seoKeywords},
  ].filter(Boolean),
}
---

<html lang={lang} class:list={[theme === 'light' ? 'light' : 'dark']}>
<head>
  <meta charset='utf-8'/>
  <meta name="build-time" content={new Date().toLocaleString('zh')}/>
  <meta name="referrer" content="no-referrer-when-downgrade"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="google-site-verification" content="weBqSszB_MEB1baq1NhIkZOOwnPcbvskauSXY6xjaUU"/>
  <SEO
    title={seoTitle}
    description={seoDescription}
    nofollow={!enableIndexFollow}
    noindex={!enableIndexFollow}
    canonical={seoCanonical}
    openGraph={{
      basic: {
        title: seoTitle,
        type: 'website',
        image: faviconUrl,
        url: seoCanonical,
      },
      optional: {
        description: seoDescription,
        siteName: seoTitle,
        locale: 'zh_CN',
      },
      image: {
        url: faviconUrl,
        width: 121,
        height: 121,
        alt: seoTitle,
      }
    }}
    twitter={{
      card: 'summary_large_image',
      title: seoTitle,
      description: seoDescription,
    }}
    extend={seoTags}
  />

  <LinkingData
    hostname={hostname}
    isLandingPage={isLandingPage}
    logoUrl={faviconUrl}
  />
  <Tracking
    globalGtmId='GTM-MCFLF92'
    gaId='G-HR5GGQCPZL'
  />
  <GoogleCookie/>
  <style>
    :root {
      --header-height: 54px;

      @media screen and (min-width: 1024px) {
        --header-height: 72px;
      }

      --body-font: 'Microsoft YaHei', '微软雅黑', 'PingFang SC', YouYuan, SimSun, Arial, sans-serif
    }

    body:lang(zh) {
      font-family: var(--body-font), serif;
    }
  </style>
  <style is:global>
    .text-white {
      color: rgb(255, 255, 255);
    }

    input:focus-visible {
      outline: none;
    }

    textarea:focus-visible {
      outline: none;
    }
  </style>
</head>
<body>
  <slot/>
<EnterPageUpload/>
<RightBar/>
<CookieTips/>
<MobileDownloadTips client:only="vue"/>
</body>
</html>

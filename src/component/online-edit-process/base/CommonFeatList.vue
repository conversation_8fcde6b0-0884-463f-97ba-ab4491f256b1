<script setup lang='ts'>
import {getLang} from '@central/i18n'
import {useI18n} from '@/locales';

const lang = getLang()
const {t} = useI18n()
defineProps<{
  featList: {
    name: string
    icon: string
    hoverIcon: string
    title: string
    description: string
    href: string
  }[]
}>()
</script>

<template>
  <div class="relative">
    <ul
      class="common relative w-1200px h-auto grid sm:justify-center sm:items-center
    sm:gap-[0] lg:gap-[50px] mx-auto sm:grid-cols-3 sm:grid-rows-none"
    >
      <li
        v-for='item in featList'
        :key="item.name"
        class="item hover:scale-[1.1] duration-400 will-change-transform"
      >
        <a :href="item.href" class="item-link" :title="item.description">
          <img class="item-icon normal" draggable="false" :src="item.icon" :alt="item.title"/>
          <img class="item-icon hover" draggable="false" :src="item.hoverIcon" :alt="item.title"/>
          <div class="title">{{ item.title }}</div>
          <div class="desc">{{ item.description }}</div>
        </a>
      </li>
    </ul>
  </div>
</template>

<style scoped lang='scss'>
.common {
  color: #1e3d24;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  .item {
    .item-link {
      position: relative;
      overflow: hidden;
      background-size: cover;
      display: inline-block;
      width: 366px;
      height: 104px;
      background-position: center center;
      background-repeat: no-repeat;
      border-radius: 22px;
      padding-right: 14px;
      border: 1px solid #FFF;
      box-shadow: 0 6px 20px 0 rgba(24, 173, 37, 0.15);

      &::before {
        content: '';
        display: none;
        position: absolute;
        top: 16px;
        right: 2px;
        width: 74px;
        height: 74px;
        transform: translate(37%, 50%);
        background: url("../../../assets/images/header-nav/AiTools/bg-right.svg") no-repeat center;
      }

      &:hover::before {
        display: block;
      }

      &::after {
        content: '';
        display: none;
        position: absolute;
        top: -22px;
        left: -22px;
        width: 90px;
        height: 90px;
        background: url("../../../assets/images/header-nav/AiTools/bg-left.svg") no-repeat center;
      }

      &:hover::after {
        display: block;
      }

      .item-icon {
        float: left;
        width: 60px;
        height: 60px;
        margin: 23px 20px 0 36px;

        &.normal {
          display: block;
        }

        &.hover {
          display: none;
        }
      }

      .title {
        width: 64%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #1e3d24;
        font-weight: 700;
        opacity: 1;
        margin: 28px 0 8px 0;
      }

      .desc {
        color: #1e3d24;
        font-size: 16px;
        font-weight: normal;
        opacity: 0.8;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        border: 1px solid #18AD25;
        background-color: #EAFFEE;

        .title {
          color: #18ad25;
        }

        .desc {
          opacity: 1;
        }

        .item-icon {
          &.normal {
            display: none;
          }

          &.hover {
            display: block;
          }
        }
      }
    }
  }
}
</style>

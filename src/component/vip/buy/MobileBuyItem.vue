<script setup lang="ts">

import TheCheckbox from '@/component/vip/buy/TheCheckBox.vue';

const props = defineProps<{
  isActive: boolean,
  title: string,
  subtitleHTML?: string,
  priceHTML: string,
  saleText?: string,
}>()
</script>

<template>
  <div
    class="px-[16px] flex justify-between items-center relative gradient-border bg-[#fafafa] dark:bg-[#333236]"
    :class="[isActive?'active':'normal']"
  >
    <div class="flex items-center gap-[12px] text-[16px] h-[64px]">
      <TheCheckbox :is-active="isActive"/>
      <div class="flex flex-col">
        <span>{{ title }}</span>
        <span v-html="subtitleHTML"></span>
      </div>

    </div>
    <div class="ml-2" v-html="priceHTML"></div>

    <div
      v-if="saleText&&false"
      class="min-w-[70px] text-center writing-rotate block right-1% -top-[50%]
      rotate-animation absolute right-0 text-sm font-500 z-2 text-white rounded-lg bg-[#F24D6B] shadow-online-edit px-[8px] py-1 rotate-[30deg]"
    >
      {{ saleText }}
      <span
        class="block absolute top-98% left-50% -translate-x-[50%] border-6 border-t-[#F24D6B] border-x-transparent border-b-transparent">
      </span>
    </div>
  </div>
</template>

<style scoped>
.gradient-border {
  --radius: 10px;
  @apply border-[1.5px] border-[#EAEAEB] dark:border-none relative bg-transparent z-[1] rounded-[var(--radius)];
}

.gradient-border.active {
  @apply border-[1.5px] border-theme dark:border-none;
}

.gradient-border.active::before {
  @apply bg-gradient-to-r from-theme to-theme dark:from-[#D6FB72] dark:to-[#76F9B1];
}

html.dark .gradient-border.active::before {
  background: linear-gradient(90deg, #D6FB72, #76F9B1);
}

.gradient-border::before {
  @apply dark:content-[''] absolute -inset-[1px] -z-[1] rounded-[var(--radius)] bg-gradient-to-r from-[#DFDFE0] to-[#DFDFE0] dark:from-white/[0.2] dark:to-white/[0.2];
}

.gradient-border::after {
  @apply bg-[#fafafa] dark:bg-[#333236] z-[-1] dark:content-[''] absolute inset-[0] rounded-[var(--radius)];
}

.rotate-animation {
  animation: writing-rotate 3s ease-in-out infinite
}

@keyframes writing-rotate {
  0%, to {
    transform: scale(1) rotate(30deg)
  }

  10%, 20% {
    transform: scale(.9) rotate(25deg)
  }

  30%, 50%, 70%, 90% {
    transform: scale(1.1) rotate(35deg)
  }

  40%, 60%, 80% {
    transform: scale(1.1) rotate(25deg)
  }
}
</style>

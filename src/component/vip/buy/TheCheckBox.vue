<script setup lang="ts">
defineProps<{
  isActive: boolean
}>()
</script>

<template>
  <template v-if="isActive">
    <div class="block dark:hidden">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z"
          fill="url(#paint0_linear_55242_2344)"/>
        <path
          d="M10.8906 18.6551C10.3864 18.6551 9.88217 18.4534 9.57964 18.0501L5.44519 13.3106C4.73931 12.6047 4.84015 11.3946 5.64687 10.7896C6.35275 10.0837 7.462 10.1845 8.06704 10.9912L10.6889 13.9156L15.7309 6.25174C16.2351 5.44502 17.3444 5.24334 18.2519 5.74754C19.0586 6.25174 19.2603 7.36099 18.7561 8.26855L12.4032 17.8484C12.1007 18.3526 11.5965 18.6551 10.9914 18.6551C10.9914 18.6551 10.9914 18.6551 10.8906 18.6551Z"
          fill="white"/>
        <defs>
          <linearGradient id="paint0_linear_55242_2344" x1="-1.26818e-07" y1="11.1" x2="24" y2="11.1"
                          gradientUnits="userSpaceOnUse">
            <stop stop-color="#18AD25"/>
            <stop offset="1" stop-color="#18AD25"/>
          </linearGradient>
        </defs>
      </svg>
    </div>
    <div class="hidden dark:block">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z"
          fill="url(#paint0_linear_55242_1618)"/>
        <path
          d="M10.8906 18.6551C10.3864 18.6551 9.88217 18.4534 9.57964 18.0501L5.44519 13.3106C4.73931 12.6047 4.84015 11.3946 5.64687 10.7896C6.35275 10.0837 7.462 10.1845 8.06704 10.9912L10.6889 13.9156L15.7309 6.25174C16.2351 5.44502 17.3444 5.24334 18.2519 5.74754C19.0586 6.25174 19.2603 7.36099 18.7561 8.26855L12.4032 17.8484C12.1007 18.3526 11.5965 18.6551 10.9914 18.6551C10.9914 18.6551 10.9914 18.6551 10.8906 18.6551Z"
          fill="#2D2D33"/>
        <defs>
          <linearGradient id="paint0_linear_55242_1618" x1="2.76349e-07" y1="12" x2="24.4377" y2="15.1502"
                          gradientUnits="userSpaceOnUse">
            <stop stop-color="#D6FB72"/>
            <stop offset="1" stop-color="#76F9B1"/>
          </linearGradient>
        </defs>
      </svg>
    </div>
  </template>
  <template v-else>
    <div class="size-[24px]">

    </div>
  </template>
</template>

<script setup lang="ts">
import useProductBuy from '@/component/vip/buy/useProductBuy'
import {useI18n} from '@/locales'
import {ExtendTabType} from '@/interface/Vip'

const {t} = useI18n()
const {extendTabActiveRef} = useProductBuy()

const tabList = [
  {
    type: ExtendTabType.tab1,
    title: t('vipCreditsTab'),
    enableSale: true,
  },
  {
    type: ExtendTabType.tab2,
    title: t('pricingExtendStorage'),
    enableSale: false,
  }
]
</script>

<template>
  <div
    class="flex justify-center items-center rounded-[30px] shadow-[0_6px_20px_0_rgba(24,173,37,0.16)]">
    <ul class="flex">
      <li
        v-for="item in tabList"
        class="small-screen h-[38px] flex justify-center items-center py-[8px] px-[24px] xl:[&:lang(es)]:px-[14px]
        cursor-pointer text-[16px] font-bold rounded-[30px] relative whitespace-nowrap"
        :class="[extendTabActiveRef===item.type?'bg-primary-color text-white':'text-primary-color']"
        @click="extendTabActiveRef=item.type"
      >
        <span>{{ item.title }}</span>
      </li>
    </ul>
  </div>

</template>

<style scoped>
@media screen and (max-width: 1630px) {
  .small-screen {
    padding-left: 12px;
    padding-right: 12px;
  }
}

@media screen and (max-width: 1280px) {
  .small-screen {
    padding-left: 8px;
    padding-right: 8px;
  }
}
</style>

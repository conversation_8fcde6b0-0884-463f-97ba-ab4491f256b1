<script setup lang="ts">

import disableIcon from '@/assets/images/vip/close.svg'
import enableIcon from '@/assets/images/vip/yes.svg'
import {useI18n} from '@/locales'
import {getLang} from '@central/i18n';
import useProductBuy from '@/component/vip/buy/useProductBuy';
import {computed} from 'vue';

const {t} = useI18n()
const lang = getLang()
const {
  productInfo
} = useProductBuy()

const formatGiftPlanDesc = (desc: string) => desc.split('<br/>')[0].replace(/<\/?b>/g, '')

const basicCreditsRef = computed(() => {
  const m = productInfo.vipInfo.find(i => i.sort === 2)

  if (m) {
    return [
      formatGiftPlanDesc(m.gift_plan_desc),
    ].join('\n')
  } else {
    return ''
  }
})

const proCreditsRef = computed(() => {
  const m = productInfo.vipInfo.find(i => i.sort === 3)
  const j = productInfo.vipInfo.find(i => i.sort === 1)

  if (m && j) {
    return [
      formatGiftPlanDesc(m.gift_plan_desc),
      t('pricingKey47'),
      formatGiftPlanDesc(j.gift_plan_desc)
    ].join('\n')
  } else {
    return ''
  }
})
const businessCreditsRef = computed(() => {
  const i = productInfo.vipInfo.find(i => i.sort === 5)
  return i ? formatGiftPlanDesc(i.gift_plan_desc) : ''
})

const tableListRef = computed(() => {
  return [
    {
      type: 'text',
      title: t('pricingKey29'),
      free: t('pricingKey37'),
      month: basicCreditsRef.value,
      year: proCreditsRef.value,
      pro: businessCreditsRef.value,
    },
    {
      type: 'text',
      title: t('pricingKey30'),
      free: '2GB',
      month: '10GB',
      year: '10GB',
      pro: '100GB',
    },
    {
      type: 'text',
      title: t('pricingKey31'),
      free: t('numberFilesUnit').replace('xx', '5'),
      month: t('purchaseUnlimited'),
      year: t('purchaseUnlimited'),
      pro: t('purchaseUnlimited'),
    },
    {
      type: 'text',
      title: t('pricingKey32'),
      free: t('pricingKey38'),
      month: t('purchaseUnlimited'),
      year: t('purchaseUnlimited'),
      pro: t('purchaseUnlimited'),
    },
    {
      type: 'text',
      title: t('pricingKey33'),
      free: t('dataRetentionDesc1').replace('#', lang === 'zh' ? '6' : '1'),
      month: t('dataRetentionDesc2'),
      year: t('dataRetentionDesc2'),
      pro: t('dataRetentionDesc2'),
    },
    {
      type: 'img',
      title: t('pricingKey34'),
      free: 'trial-text',
      month: enableIcon,
      year: enableIcon,
      pro: enableIcon,
    },
    {
      type: 'img',
      title: t('pricingKey35'),
      free: disableIcon,
      month: disableIcon,
      year: disableIcon,
      pro: enableIcon,
    },
  ];
})

</script>

<template>
  <table class="w-full">
    <thead class="bg-primary-color/[0.05]">
    <tr class="text-[20px]">
      <th class="text-start px-[20px]">{{ t('purchaseFeatures') }}</th>
      <th class="w-[210px]">{{ t('pricingKey36') }}</th>
      <th class="w-[210px]">{{ t('vipBasic') }}</th>
      <th class="w-[210px]">{{ t('vipPro') }}</th>
      <th class="w-[210px]">{{ t('vipBusiness') }}</th>
    </tr>
    </thead>

    <tbody>

    <tr v-for="item in tableListRef" class="text-center">
      <th class="text-start px-[20px]">
        <span class="text-start text-[18px]">{{ item.title }}</span>
      </th>

      <td>
        <template v-if="item.type === 'text'">
          {{ item.free }}
        </template>
        <template v-else-if="item.free === 'trial-text'">
          {{ t('pricingKey39') }}
        </template>
        <template v-else>
          <img class="mx-auto" :src="item.free" alt="icon">
        </template>
      </td>

      <td>
        <template v-if="item.type === 'text'">
          <div class="flex-center whitespace-pre-line py-[20px]">{{ item.month }}</div>
        </template>
        <template v-else>
          <img class="mx-auto" :src="item.month" alt="icon"/>
        </template>
      </td>

      <td>
        <template v-if="item.type === 'text'">
          <div class="flex-center whitespace-pre-line py-[20px]">{{ item.year }}</div>
        </template>
        <template v-else>
          <img class="mx-auto" :src="item.year" alt="icon"/>
        </template>
      </td>

      <td>
        <template v-if="item.type === 'text'">
          {{ item.pro }}
        </template>
        <template v-else>
          <img class="mx-auto" :src="item.pro" alt="icon"/>
        </template>
      </td>
    </tr>

    </tbody>
  </table>
</template>

<style scoped>
table, tr, th, td {
  border: 1px solid #e2e8f0;
  border-collapse: collapse;
}

tr {
  @apply h-[64px];
}

th {
  @apply px-[20px]
}

tbody tr {
  @apply bg-white hover:bg-[#E8F7E9];
}

</style>

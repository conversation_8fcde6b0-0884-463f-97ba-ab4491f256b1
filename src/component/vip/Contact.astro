---
import {useI18n} from '@/locales';
const {t} = useI18n()
---
<div
  class="mx-[20px] lg:mx-auto lg:w-[62.5vw] lg:h-[15.6vw] bg-gradient-light-primary rounded-[16px] lg:rounded-[48px] flex justify-between items-center p-[30px] lg:p-[4vw] lg:flex-row flex-col"
>
  <p class="text-white text-[18px] my-[30px] text-center lg:text-start lg:text-[28px] font-bold">
    {t('purchaseContactUs')}
  </p>
  <button
    id="pricing-contactUs-btn"
    class="min-w-[200px] h-[60px] rounded-[48px] bg-white text-primary-color text-[20px] font-bold transition duration-500 hover:translate-y-[5px] px-[10px]"
  >
    {t('002219')}
  </button>
</div>
<script>
  const el = document.querySelector<HTMLElement>('#pricing-contactUs-btn')
  if (el) {
    el.onclick = async () => {
      const {default: showContactUsDialog} = await import('@/render/showContactUsDialog')
      showContactUsDialog()
    }
  }
</script>

<script setup lang="ts">
import downloadIcon from '@/assets/images/header-nav/client-download/reccloud-mobile.png'
import windowsRecIcon from '@/assets/images/header-nav/FeatList/windows-rec.svg'

import {getMobileClientDownloadUrl, downloadWindowsUrl} from '@/services/constants';
import TrackUtil from '@/track/_TrackUtil';
import {useI18n} from '@/locales';
import {getLang} from '@central/i18n';
import appleIcon from '@/assets/images/footer/apple.png'
import googleIcon from '@/assets/images/footer/google.png'
import robotIcon from '@/assets/images/footer/robot.svg'
import {onMounted, ref} from 'vue';

const {t} = useI18n()
const lang = getLang()

const list = [
  {
    href: getMobileClientDownloadUrl() + `&os=ios`,
    url: appleIcon,
    title: t('iosTitle'),
    subtitle: t('iosDownload'),
  },
  {
    href: `${getMobileClientDownloadUrl()}&os=android` + (lang === 'zh' ? '' : '&lang=en'),
    url: getLang() === 'zh' ? robotIcon : googleIcon,
    title: t('androidTitle'),
    subtitle: t('androidDownload'),
  }
]

const isZh = getLang() === 'zh'

const clientList = [
  {
    icon: windowsRecIcon,
    title: t('headerWinDownload'),
    href: downloadWindowsUrl,
    click() {
      TrackUtil.trackMenuClick('func-win-reccloud')
    },
  }
]
const enableDownloadWinRef = ref<boolean>(true)
onMounted(() => {
  const userAgent = navigator.userAgent
  if (/mac/i.test(userAgent) || /iPad|iPhone/.test(userAgent)) {
    enableDownloadWinRef.value = false
  }
})


</script>

<template>
  <div class="absolute top-full left-0 min-w-[238px] w-max cursor-auto">
    <ul
      class="px-[20px] py-[30px] bg-white/[0.9] rounded-[16px] shadow-[0_60px_60px_0_rgba(30,61,36,0.10)] backdrop-blur-[15px]"
    >
      <li v-if="!isZh" class="text-deep-color text-[14px] opacity-70 flex-center relative">
        <span class="flex-1 bg-deep-color h-[1px]"></span>
        <span class="z-10 px-[12px]">Android/iOS</span>
        <span class="flex-1 bg-deep-color h-[1px]"></span>
      </li>
      <li
        v-if="isZh"
        class="flex-center flex-col gap-[8px]"
      >
        <img class="size-[130px]" :src="downloadIcon" alt="download app qrcode" loading="lazy">
        <span class="text-[14px] text-deep-color">{{ t('scanToDownloadApp') }}</span>
      </li>

      <template v-else>
        <li v-for="item in list">
          <a
            :href="item.href"
            class="app-store-btn mt-[12px]"
            target="_blank"
            rel="nofollow"
          >
            <div class="flex-center gap-[8px] h-full">
              <img class="w-[20px]" :src="item.url" alt="logo" loading="lazy">

              <div class="flex flex-col text-white">
                <span
                  class="text-[12px] [&:lang(jp)]:order-1"
                  :class="['zh', 'tw'].includes(getLang()) ? 'hidden' : ''"
                >
                  {{ item.title }}
                </span>
                <span
                  class="text-[16px]"
                  :class="['zh', 'tw'].includes(getLang()) ? '' : 'font-bold'"
                >
                {{ item.subtitle }}
                </span>
              </div>
            </div>
          </a>
        </li>
      </template>


      <li v-if="enableDownloadWinRef" class="text-deep-color text-[14px] opacity-70 flex-center relative mt-[20px]">
        <span class="flex-1 bg-deep-color h-[1px]"></span>
        <span class="z-10 px-[12px]">Windows</span>
        <span class="flex-1 bg-deep-color h-[1px]"></span>
      </li>

      <li v-if="enableDownloadWinRef" class="mt-[16px]" v-for="item in clientList">
        <a
          class="download-item duration-400 flex-center gap-[10px] transition outline-primary-color outline-[2px] hover:outline"
          :href="item.href"
          @click="item.click"
        >
          <img class="size-[24px]" :src="item.icon" draggable="false" alt="logo icon" loading="lazy">
          <span>{{ item.title }}</span>
        </a>
      </li>

    </ul>
  </div>
</template>

<style scoped>
.download-item {
  background: linear-gradient(0deg, #FFF 0%, #FFF 100%), linear-gradient(90deg, #56E8B3 0%, #18AD25 100%);
  @apply w-full bg-white px-[20px] py-[16px] rounded-[12px]
  text-black font-semibold  shadow-[0px_4px_20px_0px_rgba(24,173,37,0.15)] hover:translate-y-[4px] hover:shadow-none ;
}

.app-store-btn {
  @apply h-[56px] flex-center bg-black rounded-[12px] py-[12px] cursor-pointer shadow-[0_4px_20px_0_rgba(24,173,37,0.15)]
  hover:translate-y-[4px] hover:shadow-none transition duration-400 will-change-transform;
}
</style>

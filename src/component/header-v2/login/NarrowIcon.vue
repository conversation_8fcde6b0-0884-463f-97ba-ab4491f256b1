<script setup lang="ts">

import {useId} from 'vue';

const id = useId()
</script>

<template>
  <svg
    class="[--start:currentColor] [--end:currentColor] group-hover:[--start:#18AD25] group-hover:[--end:#18AD25] dark:group-hover:[--start:#D6FB72] dark:group-hover:[--end:#76F9B1]"
    width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M11 1L6 7L1 1" :stroke="`url(#paint_${id})`" stroke-width="2" stroke-linecap="round"
          stroke-linejoin="round"/>
    <defs>
      <linearGradient
        :id="`paint_${id}`" x1="1" y1="4" x2="10.8948" y2="6.12584"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="var(--start)"/>
        <stop offset="1" stop-color="var(--end)"/>
      </linearGradient>
    </defs>
  </svg>
</template>

<style scoped>

</style>

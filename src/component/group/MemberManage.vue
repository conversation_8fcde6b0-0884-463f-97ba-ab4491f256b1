
<template>
    <div class="manage-top">
        <ul class="table-title">
            <li>
                <span class="title">{{ t("Member name") }}</span>
            </li>
            <li>
                <span class="times">{{ t("role") }}</span>
            </li>
            <li>
                <span class="date">{{ t("Joining date") }}</span>
            </li>
            <li>
                <span class="date">{{ t("Actions") }}</span>
            </li>
        </ul>
    </div>
    <div class="manage-content">
        <div class="table" v-if="memberListLengthRef > 0">
            <div class="hover" v-for="(item, index) in memberListRef" :key="item.user_id">
                <div :class="item.joined_state === 0 ? 'isApproval tr' : 'tr'">
                    <div class="td avatar">
                        <img :src="item.avatar === '' ? (item.joined_state === 0 ? defaultMemberApprovalUrl  : defaultMemberUrl ): item.avatar" :default="item.avatar === '' && item.joined_state !== 0"/>
                        <p class="title" >{{ item.user_name}}</p>
                    </div>
                    <div class="td role-type">
                        {{ item.role_type === 0 ? t('Creator') : t('Viewers') }}
                    </div>
                    <div class="td date">
                        <div class="time">{{ item.joined_at }}</div>
                    </div>
                    <div class="td edit">
                        <div class="box" v-if="item.joined_state !== 0">
                            <span class="edit-btn disabled" v-if="item.role_type === 0">
                               -
                            </span>
                            <span class="edit-btn remove" v-else @click="deleteMember(item.user_id)">
                                {{ t('Remove') }}
                            </span>
                        </div>
                        <div class="box" v-else>
                            <span class="edit-btn agree" @click="approval(1, item.user_id)">
                                {{ t('agree') }}
                            </span>
                            <span class="edit-btn disagree" @click="approval(2, item.user_id)">
                                {{ t('disagree') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <Toast ref="tip" />
    <NoticeDialog v-if="confirmRemoveRef" @clickClose="clickClose" @clickCancel="clickClose"
        @clickOk="clickOk">
        <template #default>
            <div>
                {{ t("Are you sure to remove the member?") }}
            </div>
        </template>
    </NoticeDialog>
</template>

<script  lang="ts" setup>
import {ref, onMounted} from 'vue'
import {useI18n} from "@/locales";
import Toast from "@/component/Toast.vue";
import { removeMember, groupInviteApproval } from "../../api/groupApi";
import {Member} from "@/interface/Group";
import { getMemberList } from "../../api/groupApi";
import NoticeDialog from '../NoticeDialog.vue';
import defaultMemberUrl from '@/assets/images/group/defaultMember.svg';
import defaultMemberApprovalUrl from '@/assets/images/group/defaultMemberApproval.svg';
import { trackUpload } from '@/track/_TrackUtil';
let memberListRef = ref<Array<Member>>([]);
let memberListLengthRef = ref<number>(-1);
let tip = ref<any>();
let confirmRemoveRef = ref<boolean>(false);
let id: string = '';
let userId: number = 0;/* 操作的userId */
const emit = defineEmits(['close']);
const { t } = useI18n();

const clickClose = ()=>{
    confirmRemoveRef.value = false;
};

const clickOk = async ()=>{
    await removeMember(id, tip, userId);
    memberList();
    confirmRemoveRef.value = false;
};

onMounted(async ()=>{
    id = location.search.substring(1,8);
    await memberList();
});

const deleteMember = async (user_id: number) => {
    trackUpload({
        event: "button_click",
        button_name: "group_member_delete"
    })
    userId = user_id;
    confirmRemoveRef.value = true;
}

const memberList = async () => {
    const result = await getMemberList(id);
    if (result.Members) {
        memberListRef.value = result.Members;
        memberListLengthRef.value = result.Members.length;
    } else {
        memberListRef.value = [];
        memberListLengthRef.value = 0;
    }
}

const approval= async (state:number, userId:number) => {
    await groupInviteApproval(state, id, tip, userId);
    memberList();
}
defineExpose({memberList})
</script>

<style lang="scss" scoped>
@import "src/style/common.scss";
.manage-top{
    line-height: 58px;
    padding: 0 20px;
    .table-title {
        width: 100%;
        display: table;
        font-size: 14px;
        border-bottom: 1px solid #dcf2dd;
        li {
            display: table-cell;
            &:nth-of-type(1) {
                width: 35%;
                min-width: 200px;
            }
            &:nth-of-type(2) {
                width: 21.5625%;
                min-width: 150px;
            }
            &:nth-of-type(3) {
                width: 25.25%;
                min-width: 200px;
            }
        }
    }
}
.manage-content{
        height: calc(100vh - 351px);
        position: relative;
        overflow-y: auto;
        overflow-x: hidden;
        &::-webkit-scrollbar{
            display: none;
        }
        @include firefox-disable-scrollbar();
        .table {
            width: 100%;
            .tr {
                height: 72px;
                display: table;
                border-bottom: 1px solid #dcf2dd;
                width: 100%;
                color: rgba(30, 61, 36, 0.8);
                text-align: left;
                font-size:14px;
                &.isApproval{
                    color: rgba(30, 61, 36, 0.5);
                    .title{
                        color: rgba(30, 61, 36, 0.5) !important;
                    }
                }
                .radio{
                    margin: 28px 0px 0 0;
                }
                .td {
                    line-height: 20px;
                    position: relative;
                    display: table-cell;
                    vertical-align: middle;
                    &.avatar {
                        width: 35%;
                        min-width: 200px;
                        height: 72px;
                        img {
                            width: 32px;
                            height: 32px;
                            object-fit: cover;
                            border-radius: 50%;
                        }

                        .title {
                            position: absolute;
                            left: 48px;
                            top: 26px;
                            color: $recTextColor;
                            font-size: 16px;
                            &:hover {
                                color: $recThemeColor;
                            }
                        }

                        input {
                            display: inline-block;
                            user-select: unset;
                            background: transparent;
                            color: $recThemeColor !important;
                            border-bottom: 1px solid $recThemeColor;
                        }
                    }

                    &.role-type {
                       width: 21.5625%;
                        min-width: 150px;
                    }

                    &.date {
                       width: 25.25%;
                        min-width: 200px;
                    }

                    &.edit {
                        color: rgba(30, 61, 36, 0.8);
                        .box{
                            display: none;
                        }
                        .edit-btn {
                            display: inline-block;
                            height: 72px;
                            line-height: 72px;
                            cursor: pointer;
                            margin-right: 20px;
                            &.disabled{
                                cursor: default;
                                &:hover{
                                    color: inherit;
                                }
                            }
                            &:hover{
                                color: #F06464;
                            }
                            &.agree{
                                &:hover{
                                    color: $recThemeColor;
                                }
                            }

                        }
                    }
                }
                &:hover{
                    .td.edit .box{
                        display: block;
                    }
                    .avatar img[default=true]{
                        content: url('/src/assets/images/group/defaultMember-hover.svg');
                    }
                }
            }
        }

        .hover {
            width: 100%;
            height: 72px;
            border-radius: 20px;
            position: relative;
            padding: 0 20px;
            cursor:pointer;
            &:hover {
                background: rgba(24, 173, 37, 0.05);
            }
        }
}
</style>

<script lang="ts" setup>
import starIcon from '@/assets/images/home/<USER>/comment-star.svg'

import {useI18n} from '@/locales'
import {getLang} from '@central/i18n'

defineProps<{
  list: {
    img: string
    title: string
    name: string
  }[]
}>()

const {t} = useI18n()
const lang = getLang()

</script>
<template>
  <ul class="flex flex-wrap gap-[30px] max-w-[1600px] mx-auto justify-center">
    <li v-for="(item, index) in list" :key="index" class="flex gap-[12px] lg:gap-[24px] px-[24px]">
      <div class="h-min bg-white rounded-full shadow-[0_10px_40px_0_rgba(24,173,37,0.50)]">
        <img :src="item.img" alt=""/>
      </div>
      <div class="flex flex-col">
        <img class="w-[75px] lg:w-[150px]" :src="starIcon" alt=""/>
        <div class="max-w-[420px] text-[14px] lg:text-[16px] mt-[12px] lg:mt-[16px] break-words">{{ item.title }}</div>
        <div class="text-[12px] mt-[8px] text-[#788A7B]">{{ item.name }}</div>
      </div>
    </li>
  </ul>
</template>

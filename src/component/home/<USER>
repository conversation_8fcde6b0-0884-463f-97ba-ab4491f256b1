<script lang="ts" setup>
import commentStarIcon from '@/assets/images/home/<USER>/comment-star.svg'

interface commentItem {
  avatar: string,
  comment: string,
  name: string,
}

const props = withDefaults(defineProps<{
  list: commentItem[],
  starIcon?: string,
  liClassName?: string
  avatarClassName?: string,
  commentClassName?: string,
  nameClassName?: string
}>(), {
  starIcon: commentStarIcon
})

</script>
<template>
  <ul class="flex flex-wrap gap-[30px] max-w-[1600px] mx-auto justify-center">
    <li
      v-for="(item, index) in list"
      :key="index"
      class="flex gap-[12px] lg:gap-[24px] px-[24px]"
      :class="liClassName"
    >
      <div
        class="h-min rounded-full bg-white shadow-[0_10px_40px_0_rgba(24,173,37,0.50)]"
        :class="avatarClassName"
      >
        <img :src="item.avatar" alt="avatar" loading="lazy" draggable="false" width="110" height="110"/>
      </div>
      <div class="flex flex-col">
        <img
          class="w-[75px] lg:w-[150px]"
          :src="starIcon"
          alt="star"
          draggable="false"
          loading="lazy"
          width="150" height="30"
        />
        <div
          class="max-w-[420px] text-[14px] lg:text-[16px] mt-[12px] lg:mt-[16px] break-words"
          :class="commentClassName"
        >
          {{ item.comment }}
        </div>
        <div
          class="text-[12px] mt-[8px] text-[#788A7B]"
          :class="nameClassName"
        >
          {{ item.name }}
        </div>
      </div>
    </li>
  </ul>
</template>

<script lang="ts" setup>
import {computed, ref, watch} from 'vue'
import videoPlay from "@/assets/images/online-edit/video-play.svg"
import videoPaused from "@/assets/images/online-edit/video-paused.svg"
import {dateTransfer} from "@/utils/util"
import LoadingIcon from '@/component/online-edit/ai-subtitle-v2/subtitle-operation/LoadingIcon.vue'
import {useElementSize, useEventListener, useMediaControls} from '@vueuse/core'
import EditIcon from '@/component/ai-clip-maker/icons/EditIcon.vue';
import DownloadIcon from '@/component/ai-clip-maker/icons/DownloadIcon.vue';
import {useI18n} from "@/locales";
import AiClipMakerGuideAnimation from '@/component/ai-clip-maker/guide-animation/AiClipMakerGuideAnimation';
import AiClipMakerTrack from '@/track/AiClipMakerTrack';

const {t} = useI18n();

// 常量配置
const sizeMap = {
  controlHandleSize: 16, // 普通进度条手柄宽度
  editHandleWidth: 6,   // 编辑模式手柄宽度
  editHandleHeight: 20, // 编辑模式手柄高度
  minDuration: 1  // 最小剪辑时长（秒）
}

const props = defineProps<{
  index: number,
  url: string,
  clipStart: number,//单位s
  clipEnd: number,//单位s
  onClickDownload: (index: number) => Promise<void>,
}>()

const emit = defineEmits<{
  updateTime: [start: number, end: number]
}>()

const videoElRef = ref<HTMLVideoElement>()
const trackElRef = ref<HTMLElement>()
const rangeTrackElRef = ref<HTMLElement>()

const {playing, currentTime, duration, waiting} = useMediaControls(videoElRef, {
  src: props.url,
})

const {width: trackWidth} = useElementSize(trackElRef)
const {width: rangeTrackWidth} = useElementSize(rangeTrackElRef)

const isDownloadingRef = ref(false)
// 编辑模式状态
const isEditMode = ref(false)
const editStart = ref(props.clipStart)
const editEnd = ref(props.clipEnd)
// 保存进入编辑模式前的播放进度
const savedPlaybackTime = ref(0)

// 监听props变化，更新编辑值
watch(() => [props.clipStart, props.clipEnd], ([start, end]) => {
  editStart.value = start
  editEnd.value = end
}, {immediate: true})

const handlerLeft = computed(() => {
  if (duration.value === 0 || trackWidth.value === 0) return "0px"
  const progress = currentTime.value / duration.value
  const maxPosition = trackWidth.value - sizeMap.controlHandleSize
  const position = Math.min(progress * maxPosition, maxPosition)
  return position + "px"
})

// 双向拖动条的位置计算
const startHandlerLeft = computed(() => {
  if (duration.value === 0 || rangeTrackWidth.value === 0) return "0px"
  const progress = editStart.value / duration.value
  const maxPosition = rangeTrackWidth.value - sizeMap.editHandleWidth
  const position = Math.min(progress * maxPosition, maxPosition)
  return position + "px"
})

const endHandlerLeft = computed(() => {
  if (duration.value === 0 || rangeTrackWidth.value === 0) return "0px"
  const progress = editEnd.value / duration.value
  const maxPosition = rangeTrackWidth.value - sizeMap.editHandleWidth
  const position = Math.min(progress * maxPosition, maxPosition)
  return position + "px"
})

const selectedRangeStyle = computed(() => {
  if (duration.value === 0 || rangeTrackWidth.value === 0) return {left: "0px", width: "0px"}
  const startProgress = editStart.value / duration.value
  const endProgress = editEnd.value / duration.value
  const left = startProgress * rangeTrackWidth.value
  const width = (endProgress - startProgress) * rangeTrackWidth.value
  return {
    left: left + "px",
    width: width + "px"
  }
})

const togglePlay = () => {
  // 编辑模式下不允许播放
  if (isEditMode.value) return
  playing.value = !playing.value
}

const handleSeek = (ev: Event) => {
  const present = (ev as PointerEvent).offsetX / trackWidth.value
  currentTime.value = duration.value * present
}

const moveHandler = (ev: MouseEvent) => {
  const el = ev.target as HTMLElement
  const pageX = ev.pageX
  const style = getComputedStyle(el)
  const left = parseFloat(style.left)

  document.onmousemove = function (ev) {
    const x: number = left + ev.pageX - pageX
    let present = x / trackWidth.value
    if (present < 0) {
      present = 0
    } else if (present > 1) {
      present = 1
    }
    currentTime.value = duration.value * present
  }

  document.onmouseup = function () {
    document.onmousemove = document.onmouseup = null
  }
}

// 双向拖动条处理函数
const handleRangeMove = (ev: MouseEvent, type: 'start' | 'end') => {
  const el = ev.target as HTMLElement
  const pageX = ev.pageX
  const style = getComputedStyle(el)
  const left = parseFloat(style.left)

  document.onmousemove = function (ev) {
    const x: number = left + ev.pageX - pageX
    let present = x / rangeTrackWidth.value
    if (present < 0) {
      present = 0
    } else if (present > 1) {
      present = 1
    }

    const newTime = duration.value * present

    if (type === 'start') {
      // 确保开始时间不能超过结束时间-最小时长
      const maxStart = Math.max(0, editEnd.value - sizeMap.minDuration)
      editStart.value = Math.min(newTime, maxStart)
      currentTime.value = editStart.value
    } else {
      // 确保结束时间不能小于开始时间+最小时长
      const minEnd = Math.min(duration.value, editStart.value + sizeMap.minDuration)
      editEnd.value = Math.max(newTime, minEnd)
      currentTime.value = editEnd.value
    }
  }

  document.onmouseup = function () {
    document.onmousemove = document.onmouseup = null
  }
}

// 整体拖动处理函数
const handleRangeAreaMove = (ev: MouseEvent) => {
  const pageX = ev.pageX
  const initialStart = editStart.value
  const initialEnd = editEnd.value
  const rangeDuration = initialEnd - initialStart // 选区长度

  document.onmousemove = function (ev) {
    const deltaX = ev.pageX - pageX
    const deltaTime = (deltaX / rangeTrackWidth.value) * duration.value

    let newStart = initialStart + deltaTime
    let newEnd = initialEnd + deltaTime

    // 边界检查：确保不超出视频总时长
    if (newStart < 0) {
      newStart = 0
      newEnd = rangeDuration
    } else if (newEnd > duration.value) {
      newEnd = duration.value
      newStart = duration.value - rangeDuration
    }

    editStart.value = newStart
    editEnd.value = newEnd
    currentTime.value = newStart
  }

  document.onmouseup = function () {
    document.onmousemove = document.onmouseup = null
  }
}

// 编辑模式相关函数
const enterEditMode = () => {
  AiClipMakerTrack.clickEditBtn()
  // 保存当前播放进度
  savedPlaybackTime.value = currentTime.value
  isEditMode.value = true
  playing.value = false // 进入编辑模式时暂停播放
  // 将视频进度定位到剪辑开始时间
  currentTime.value = props.clipStart
  AiClipMakerGuideAnimation.start()
}

const exitEditMode = () => {
  isEditMode.value = false
  // 恢复到原始值
  editStart.value = props.clipStart
  editEnd.value = props.clipEnd
  // 恢复之前的播放进度
  currentTime.value = savedPlaybackTime.value
}

const confirmEdit = () => {
  AiClipMakerTrack.clickEditDone()
  isEditMode.value = false
  emit('updateTime', editStart.value, editEnd.value)
  // 恢复之前的播放进度
  currentTime.value = savedPlaybackTime.value
}

const handleDownload = async () => {
  AiClipMakerTrack.clickDownload().then()
  // 下载逻辑
  try {
    isDownloadingRef.value = true
    await props.onClickDownload(props.index)
  } catch (e) {
    console.error(e)
  } finally {
    isDownloadingRef.value = false
  }
}

useEventListener(document, 'keydown', (ev: KeyboardEvent) => {
  const activeElement = document.activeElement;
  if (activeElement instanceof HTMLInputElement ||
    activeElement instanceof HTMLTextAreaElement ||
    activeElement instanceof HTMLSelectElement ||
    (activeElement as HTMLElement).isContentEditable) {
    return
  }

  if (ev.code === 'Space') {
    ev.preventDefault()
    togglePlay()
  }
})

</script>

<template>
  <div class="w-full h-auto video-container">
    <!-- 视频区域 -->
    <div class="mx-auto relative w-full">
      <!-- 加载状态 -->
      <div
        v-show="waiting"
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-1 animate-spin"
      >
        <LoadingIcon class="text-white"/>
      </div>

      <!-- 视频播放器 -->
      <div class="w-full aspect-video mx-auto bg-black justify-center rounded-t-[20px] overflow-hidden flex">
        <video
          ref="videoElRef"
          class="video object-cover size-full"
          preload="metadata"
          playsinline
        >
        </video>
      </div>
    </div>

    <!-- 控制栏 -->
    <div
      v-if="!isEditMode"
      class="pr-[16px] h-[66px] bg-dark rounded-b-[20px] backdrop-blur-[5px] items-center justify-center gap-10px flex"
    >
      <!-- 播放/暂停按钮 -->
      <div
        class="w-[40px] h-[40px] flex justify-center items-center cursor-pointer"
        @click="togglePlay"
      >
        <img
          class="size-[20px]"
          :class="[!playing ? 'ml-1' : '']"
          :src="playing ? videoPaused : videoPlay"
          alt=""
        >
      </div>

      <!-- 进度条 -->
      <div class="flex-1 relative h-full">
        <div
          class="flex-1 h-full flex-center cursor-pointer relative"
          @click="handleSeek"
          ref="trackElRef"
        >
          <div
            class="w-full h-[6px] bg-[#EEE]/[0.1] relative flex items-center cursor-pointer rounded overflow-hidden">
            <div class="h-[6px] bg-white" :style="{width: handlerLeft}"></div>
          </div>
        </div>

        <!-- 进度条拖拽手柄 -->
        <div
          class="bg-white rounded-full absolute top-1/2 -translate-y-1/2 cursor-pointer"
          :style="{left: handlerLeft,width:`${sizeMap.controlHandleSize}px`,height:`${sizeMap.controlHandleSize}px`}"
          @mousedown.stop.prevent="moveHandler"
        ></div>
      </div>

      <!-- 时长显示 -->
      <div class="ml-2 text-[14px] text-right text-white w-[120px]">
        <span>{{ dateTransfer(currentTime) }}</span>
        <span class="opacity-50">/{{ dateTransfer(duration) }}</span>
      </div>
    </div>

    <!-- 编辑模式控制栏 -->
    <div
      v-else
      class="h-[66px] bg-dark rounded-b-[20px] backdrop-blur-[5px] flex items-center justify-center px-4"
    >
      <!-- 双向拖动条 -->
      <div class="flex-1 relative h-full">
        <div
          class="flex-1 h-full flex-center relative"
          ref="rangeTrackElRef"
        >
          <!-- 轨道背景 -->
          <div class="w-full h-[6px] bg-[#EEE]/[0.1] relative flex items-center rounded overflow-hidden">
            <!-- 选中范围 -->
            <div
              id="range-track-id"
              class="h-[6px] bg-white absolute cursor-grab active:cursor-grabbing"
              :style="selectedRangeStyle"
              @mousedown.stop.prevent="handleRangeAreaMove"
            ></div>
          </div>
        </div>

        <!-- 开始时间拖拽手柄 -->
        <div
          ref="startHandlerRef"
          class="bg-white absolute top-1/2 -translate-y-1/2 cursor-pointer z-10 rounded-[3px]"
          :style="{left: startHandlerLeft, width:`${sizeMap.editHandleWidth}px`,height:`${sizeMap.editHandleHeight}px`}"
          @mousedown.stop.prevent="(ev) => handleRangeMove(ev, 'start')"
        ></div>

        <!-- 结束时间拖拽手柄 -->
        <div
          ref="endHandlerRef"
          class="bg-white absolute top-1/2 -translate-y-1/2 cursor-pointer z-10 rounded-[3px]"
          :style="{left: endHandlerLeft, width:`${sizeMap.editHandleWidth}px`,height:`${sizeMap.editHandleHeight}px`}"
          @mousedown.stop.prevent="(ev) => handleRangeMove(ev, 'end')"
        ></div>
      </div>

      <!-- 时长显示 -->
      <div class="ml-2 text-[14px] text-right text-white w-[120px]">
        <span>{{ dateTransfer(currentTime) }}</span>
        <span class="opacity-50">/{{ dateTransfer(duration) }}</span>
      </div>
    </div>

    <div class="gap-4 mt-[30px] pb-[20px] text-[20px] font-semibold flex-center">
      <button
        class="px-[20px] min-w-[220px] h-[58px] bg-[#2A2A2A] hover:bg-[#3A3A3A] rounded-[42px] flex-center gap-[12px] text-white"
        @click="isEditMode ? exitEditMode() : enterEditMode()"
      >
        <template v-if="!isEditMode">
          <EditIcon/>
          <span>{{ t('aiClipTaskResultEditTitle') }}</span>
        </template>

        <template v-else>
          <span>{{ t('speechAudioCancel') }}</span>
        </template>
      </button>

      <button
        class="h-[58px] gradient-button-auto-theme px-[20px] min-w-[220px] flex-center gap-[12px] disabled:opacity-50 disabled:pointer-events-none"
        @click="isEditMode ? confirmEdit() : handleDownload()"
        :disabled="isDownloadingRef"
      >
        <template v-if="isDownloadingRef">
          <span>{{ t('aiClipTaskResultDownloadingTitle') }}</span>
        </template>
        <template v-else>
          <template v-if="!isEditMode">
            <DownloadIcon/>
            <span>{{ t('aiClipTaskResultDownloadTitle') }}</span>
          </template>
          <template v-else>
            <span>{{ t('aiSubtitleConfirm') }}</span>
          </template>
        </template>

      </button>
    </div>
  </div>
</template>

<style scoped>

</style>

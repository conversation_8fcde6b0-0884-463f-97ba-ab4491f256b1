---
import {useI18n} from '../../locales'
import TipsIcon from '~icons/home/<USER>'
import {getLang} from '@central/i18n';
const {t} = useI18n()

const {class: classname} = Astro.props

const textToSpeechChat = getLang() === 'zh' ? '100' : '200'
const tableList = [
  {
    title: t('pricingKey4'),
    desc: t('creditsEveryMint').replace('xx', '1'),
  },
  {
    title: t('pricingKey5'),
    desc: [
      t('pricingKey50') + ': ' + t('creditsEveryMints').replace('xx', '2'),
      t('pricingKey51') + ': ' + t('creditsEveryMints').replace('xx', '6'),
    ],
  },
  {
    title: t('pricingKey6'),
    desc: [
      t('pricingKey55') + ': ' + t('creditsEveryMints').replace('xx', '1'),
      t('pricingKey50') + ': ' + t('creditsEveryMints').replace('xx', '2'),
      t('pricingKey51') + ': ' + t('creditsEveryMints').replace('xx', '1').replace('/', '/2'),
    ],
  },
  {
    title: t('pricingKey7'),
    desc: t('vipCredit100Words').replace('#', '1').replace('@', textToSpeechChat),
  },
  {
    title: t('pricingKey8') + `(${t('textToVideoBasicModel')})`,
    desc: t('creditsEveryTimes').replace('xx', '10'),
  },
  // {
  //   title: t('pricingKey9'),
  //   desc: t('creditsEveryMint').replace('xx', '1'),
  // },
]
---

<div class="w-full border border-[#e2e8f0] dark:border-[rgba(140,139,153,0.2)] overflow-hidden" class:list={[classname]}>
  <table class="w-full">
    <thead class="bg-[#F3FBF4] dark:bg-dark">
      <tr class="text-[18px]">
        <th class="text-start dark:px-[20px] px-[70px] w-[346px] font-bold">
          <span class="dark:opacity-70">{t('apiPricingKey9')}</span>
        </th>
        <th class="text-start !px-[80px]">
          <span class="dark:opacity-70">{t('apiPricingKey11')}</span>
          <span class="group cursor-pointer relative">
            <TipsIcon class="text-dark group-hover:text-theme dark:text-inherit group-hover:text-inherit"/>
            <span
              class="transition scale-0 group-hover:scale-100 delay-100 origin-left absolute w-max top-0 left-[calc(100%+6px)] px-[10px] py-[6px] bg-white dark:bg-[#1B1B1D] shadow z-10 rounded-[6px] text-[14px] font-normal"
            >
              {t('apiPricingKey12')}
            </span>
          </span>
        </th>
      </tr>
    </thead>

    <tbody>
    {
      tableList.map(item => {
        return item && (
          <tr class="text-center bg-white dark:bg-transparent hover:bg-[#E8F7E9] dark:hover:bg-dark">
            <th class="text-[18px] font-bold text-start dark:px-[20px] dark:whitespace-normal pl-[70px] whitespace-nowrap"
                set:html={item.title}></th>
            <td class="text-[16px] py-[20px] text-start pl-[80px] pr-[20px]">
              {
                Array.isArray(item.desc)
                  ? item.desc.map(desc => (<p set:html={desc}></p>))
                  : item.desc
              }
            </td>
          </tr>
        )
      })
    }
    </tbody>
  </table>
</div>

<style>
  table {
    width: 100%;
    border-spacing: 0;
  }

  /* 单元格样式 */
  th, td {
    border-right: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
  }

  /* 暗色模式下的单元格样式 */
  html.dark th, html.dark td {
    border-right: 1px solid rgba(140, 139, 153, 0.2);
    border-bottom: 1px solid rgba(140, 139, 153, 0.2);
  }

  /* 最后一列不要右边框 */
  tr th:last-child, tr td:last-child {
    border-right: none;
  }

  /* 最后一行不要底边框 */
  tbody tr:last-child th, tbody tr:last-child td {
    border-bottom: none;
  }

  th {
    @apply min-h-[64px] p-[20px];
  }

  .title-link {
    @apply text-primary-color font-normal underline cursor-pointer relative;
  }
</style>

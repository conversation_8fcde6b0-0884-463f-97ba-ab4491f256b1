<script setup lang="ts">
import TheCheckbox from '@/component/base/checkbox/TheCheckBox.vue';

defineProps<{
  isActive: boolean,
  title: string,
  yearPrice: string,
  creditPrice: string,
  saleText?: string,
}>()
</script>

<template>
  <div
    :class="[isActive?'active':'normal']"
    class="cursor-pointer pl-[10px] lg:pl-[24px] pr-[20px] lg:pr-[100px] outline rounded-[10px] text-[16px] lg:text-[20px] flex justify-between items-center
    relative transition !hover:outline-2 !hover:outline-theme hover:shadow-[0px_5px_10px_0px_rgba(170,217,197,0.15)] hover:bg-primary-color/[0.1]"
  >
    <div class="flex items-center gap-[10px] lg:gap-[52px] h-[64px] lg:h-[70px] 3xl:h-[78px]">
      <div class="size-[18px]">
        <TheCheckbox v-show="isActive===true" :is-active="isActive"/>
      </div>
      <span class="md:w-[180px]">{{ title }}</span>
    </div>

    <p class="md:w-[140px] text-left">{{ yearPrice }}</p>
    <p class="md:w-[200px] text-left hidden md:block">{{ creditPrice }}</p>

    <div
      v-if="saleText&&false"
      class="min-w-[70px] text-center writing-rotate block right-1% -top-[50%] rotate-animation absolute right-0
      text-sm font-500 z-2 text-white rounded-lg bg-[#F24D6B] shadow-online-edit px-[8px] py-1 rotate-[30deg]"
    >
      {{ saleText }}
      <span
        class="block absolute top-98% left-50% -translate-x-[50%] border-6 border-t-[#F24D6B] border-x-transparent border-b-transparent">
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.active {
  @apply outline-2 outline-theme shadow-[0px_5px_10px_0px_rgba(170,217,197,0.15)] bg-primary-color/[0.1];
}

.normal {
  @apply outline-[rgba(140,139,153,0.2)] shadow-[0px_5px_10px_0px_rgba(170,217,197,0.15)] outline-1
}

.rotate-animation {
  animation: writing-rotate 3s ease-in-out infinite
}

@keyframes writing-rotate {
  0%, to {
    transform: scale(1) rotate(30deg)
  }

  10%, 20% {
    transform: scale(.9) rotate(25deg)
  }

  30%, 50%, 70%, 90% {
    transform: scale(1.1) rotate(35deg)
  }

  40%, 60%, 80% {
    transform: scale(1.1) rotate(25deg)
  }
}
</style>

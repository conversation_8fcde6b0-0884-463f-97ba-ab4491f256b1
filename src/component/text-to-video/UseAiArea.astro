---
import logo from '@/assets/images/favicon.png'
import GradientButton from '@/component/base/button/GradientButton.vue'
import {useI18n} from '@/locales'
import starIcon from '../../assets/images/text-to-video/star.svg'
import {getLang} from "@central/i18n";
//@ts-ignore
import {Picture} from '@central/assets/imagetools';
import MobileDownloadAppBtn from '@/component/online-edit/MobileDownloadAppBtn.vue';
const lang = getLang()
const {t} = useI18n()

const title = t('textUseTitle')
---
<section class="py-[180px] relative">
  <div class="absolute inset-0 w-full h-full z-[1]">
    <Picture
      src='src/assets/images/text-to-video/bg-img.png'
      alt={'background image'}
      layout="fill"
      loading={'lazy'}
    />
  </div>
  <div class="flex justify-center items-center flex-col z-10 relative">
    <img class="size-[120px]" src={logo} alt="logo" draggable="false" loading="lazy">
    <h2
      class="text-[28px] !lg:text-[60px] font-bold mt-[40px] gradient-color px-[24px] text-center ![&:lang(fr)]:text-[50px]"
      set:html={title}
    ></h2>
    <GradientButton class="hidden lg:block min-w-[240px] h-[60px] mt-[80px] contact-us-btn float-move px-[10px]">
      <span class="text-[24px] font-bold">{t('mainStartForFree')}</span>
    </GradientButton>
    <div class="block lg:hidden float-move mt-[80px]">
      <MobileDownloadAppBtn class="" client:only="vue"/>
    </div>

  </div>
  <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-[2] select-none">
    <img
      class="float-move"
      draggable="false"
      src={starIcon}
      alt=""
    >
  </div>
</section>

<style>
  .float-move {
    animation: floatMove 2s infinite alternate ease-in-out;
    animation-play-state: running;

    &:hover {
      animation-play-state: paused;
    }
  }

  @keyframes floatMove {
    0% {
      transform: translate(0, 10px);
    }
    100% {
      transform: translate(0, -10px);
    }
  }
</style>

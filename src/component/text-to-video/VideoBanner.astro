---
import VideoBannerItem from './video-banner/VideoBannerItem.vue'
import bgIcon from '@/assets/images/text-to-video/bg-one.png'
import bgTowIcon from '@/assets/images/text-to-video/bg-two.png'
import bgIcon3 from '@/assets/images/text-to-video/bg-three.png'
import {useI18n} from '../../locales'
import {getCdnUrl} from '../../utils/util';
const {t} = useI18n()
interface Props {
  id?: string
}

const {id} = Astro.props

const mainTitle = t('textVideoListMainTitle')
const item1 = {
  title: t('textVideoPart1Title'),
  descList: [
    t('textVideoPart1List1'),
    t('textVideoPart1List2'),
    t('textVideoPart1List3'),
  ],
  videoList: [
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/1.mp4'),
      desc: t('prompt1')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/2.mp4'),
      desc: t('prompt2')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/3.mp4'),
      desc: t('prompt3')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/4.mp4'),
      desc: t('prompt4')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/5.mp4'),
      desc: t('prompt5')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part2/6.mp4'),
      desc: t('prompt6')
    },
  ]
}
const item2 = {
  title: t('textVideoPart2Title'),
  descList: [
    t('textVideoPart2List1'),
    t('textVideoPart2List2'),
    t('textVideoPart2List3'),
  ],
  videoList: [
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/1.mp4'),
      desc: t('prompt7')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/2.mp4'),
      desc: t('prompt8')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/3.mp4'),
      desc: t('prompt9')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/4.mp4'),
      desc: t('prompt10')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/5.mp4'),
      desc: t('prompt11')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part3/6.mp4'),
      desc: t('prompt12')
    },
  ]
}
const item3 = {
  title: t('textVideoPart3Title'),
  descList: [
    t('textVideoPart3List1'),
    t('textVideoPart3List2'),
    t('textVideoPart3List3'),
  ],
  videoList: [
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/1.mp4'),
      desc: t('prompt13')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/2.mp4'),
      desc: t('prompt14')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/3.mp4'),
      desc: t('prompt15')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/4.mp4'),
      desc: t('prompt16')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/5.mp4'),
      desc: t('prompt17')
    },
    {
      url: getCdnUrl('/video/reccloud/text-to-video/part4/6.mp4'),
      desc: t('prompt18')
    },
  ]
}
---

<section class="">
  <div class="relative flex justify-center items-center pt-[5.2vw] mb-[4.56vw]" id={id}>
    <h2 class="mx-[20px] text-[28px] !lg:text-[52px] font-bold text-center" set:html={mainTitle}></h2>
    <div class="absolute right-0 top-0 -z-[1]">
      <img src={bgIcon} alt="" class="opacity-80 select-none" draggable="false" loading="lazy">
    </div>
  </div>
  <VideoBannerItem
    client:visible
    title={item1.title}
    descList={item1.descList}
    videoList={item1.videoList}
  />
  <VideoBannerItem
    client:visible
    title={item2.title}
    descList={item2.descList}
    videoList={item2.videoList}
  >
    <img src={bgTowIcon} alt="" slot="title-left" loading="lazy">
  </VideoBannerItem>
  <VideoBannerItem
    client:visible
    title={item3.title}
    descList={item3.descList}
    videoList={item3.videoList}
  >
    <img class="h-[536px]" src={bgIcon3} alt="" loading="lazy">
  </VideoBannerItem>
</section>

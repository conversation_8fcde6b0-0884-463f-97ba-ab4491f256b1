<script setup lang="ts">
import downloadIcon from '../../../../assets/images/text-to-video/function-icon/down-icon.svg'
import downloadHoverIcon from '../../../../assets/images/text-to-video/function-icon/down-hover-icon.svg'
import deleteIcon from '../../../../assets/images/text-to-video/function-icon/del-icon.svg'
import deleteHoverIcon from '../../../../assets/images/text-to-video/function-icon/del-hover-icon.svg'
import editIcon from '@/assets/images/text-to-video/function-icon/edit-icon.svg'
import editHoverIcon from '@/assets/images/text-to-video/function-icon/edit-hover-icon.svg'
import onlyVipIcon from '@images/text-to-speech/only-vip.svg';

withDefaults(defineProps<{
  itemClass?: string,
  enableEdit?: boolean,
  enableDelete?: boolean,
  enableDownload?: boolean
}>(), {
  itemClass: '',
  enableEdit: true,
  enableDelete: true,
  enableDownload: true
})

const emit = defineEmits<{
  clickDownload: [],
  clickDelete: [],
  clickEdit: []
}>()

const handleClickDownload = () => {
  emit('clickDownload')
}
const handleClickDelete = () => {
  emit('clickDelete')
}

function handleClickEdit() {
  emit('clickEdit')
}
</script>

<template>
  <div
    class="video-options-container absolute right-0 bottom-[22px] bg-black/[0.6] rounded-l-[10px] px-[10px] py-[16px] flex flex-col
     gap-[16px] justify-center items-center"
  >

    <div
      v-if="enableEdit"
      class="group/active size-[20px] cursor-pointer"
      :class="[itemClass]"
      @click="handleClickEdit"
    >
      <img class="block group-hover/active:hidden w-full h-full" :src="editIcon" alt="">
      <img class="hidden group-hover/active:block w-full h-full" :src="editHoverIcon" alt="">
    </div>

    <div
      v-if="enableDownload"
      class="group/active size-[20px] cursor-pointer relative"
      :class="[itemClass]"
      @click="handleClickDownload"
    >
      <img class="block group-hover/active:hidden w-full h-full" :src="downloadIcon" alt="">
      <img class="hidden group-hover/active:block w-full h-full" :src="downloadHoverIcon" alt="">

      <span class="absolute -right-[5px] top-[3px] -translate-y-full ">
        <img :src="onlyVipIcon" alt="vip icon">
      </span>
    </div>

    <div
      v-if="enableDelete"
      class="group/active size-[20px] cursor-pointer"
      :class="[itemClass]"
      @click="handleClickDelete"
    >
      <img class="block group-hover/active:hidden w-full h-full" :src="deleteIcon" alt="">
      <img class="hidden group-hover/active:block w-full h-full" :src="deleteHoverIcon" alt="">
    </div>

  </div>
</template>

<script setup lang="ts">
import {computed, watch, nextTick} from 'vue';

const props = defineProps({
  percentage: {
    type: Number,
    default: 0,
  },
  size: {
    type: Number,
    default: 70
  },
  color: {
    type: String,
    default: '#FFFFFF'
  },
  stokeWidth: {
    type: Number,
    default: 5
  }
})

const dashArray = computed(() => {
  return 2 * Math.PI * 30; // 圆环周长，半径为30
})

const dashOffset = computed(() => {
  return dashArray.value * (100 - props.percentage) / 100;
})

const formattedPercentage = computed(() => {
  return props.percentage.toFixed(0) + '%';
})

const textAnchor = computed(() => {
  return props.percentage >= 50 ? 'middle' : 'start';
})

watch(() => props.percentage, (newVal) => {
  nextTick(() => {
    const circle = document.querySelector('circle:last-child') as HTMLElement;
    if (circle) {
      circle.style.transition = 'stroke-dashoffset 0.5s ease-out';
      circle.style.strokeDashoffset = dashOffset.value.toString();
    }
  });
})

</script>
<template>
  <div class="percentage-circle flex justify-center items-center">
    <svg :width="size" :height="size" viewBox="0 0 70 70" class="circle rounded-50%">
      <circle cx="35" cy="35" r="30" fill="transparent" stroke="#fff" :stroke-opacity="0.3" :stroke-width="stokeWidth"/>
      <circle cx="35" cy="35" r="30" fill="transparent" :stroke="color" :stroke-width="stokeWidth" :stroke-dasharray="dashArray"
              :stroke-dashoffset="dashOffset"/>
    </svg>
    <div class="rotate-90 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
      <slot name="center"></slot>
    </div>
  </div>
</template>

<style scoped>
.percentage-circle {
  transform: rotate(-90deg);
}

</style>

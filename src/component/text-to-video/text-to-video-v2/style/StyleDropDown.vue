<script setup lang="ts">

import ArrowIcon from '@/component/text-to-video/text-to-video-v2/icon/ArrowIcon.vue'
import useTextToVideoModel from '@/component/text-to-video/text-to-video-v2/useTextToVideoModel'
import {computed, ref} from 'vue'
import {vOnClickOutside} from '@vueuse/components'

const {advancedStyleInfo} = useTextToVideoModel()
const enableRef = ref(false)

function handleClickItem(item: typeof advancedStyleInfo.list[number], index: number) {
  advancedStyleInfo.updateStyle(index)
}

</script>

<template>
  <div
    class="w-full h-full border flex items-center justify-between pl-[16px] pr-[12px] relative cursor-pointer rounded-[8px] border-[#8C8B99]/[0.2]"
    @click="enableRef=!enableRef"
    v-on-click-outside="()=>enableRef=false"
  >
    <span>{{ advancedStyleInfo.activeItem?.title }}</span>
    <ArrowIcon/>

    <div
      v-show="enableRef"
      class="absolute top-[calc(100%+12px)] left-0 z-1 min-w-[620px] bg-[#26272A] rounded-[10px] p-[20px] cursor-auto border border-[#8C8B99]/[0.2]"
    >
      <ul class="w-full grid grid-rows-2 grid-cols-5 gap-[16px]">
        <li
          class="size-[100px] relative rounded-[8px] flex-center cursor-pointer hover:outline outline-primary-color hover:bg-[rgba(24,173,37,0.04)]"
          v-for="(item,index) in advancedStyleInfo.list"
          :class="[advancedStyleInfo.activeIndex===index?'bg-[rgba(24,173,37,0.04)] outline':'bg-black/[0.4]']"
          @click="handleClickItem(item,index)"
        >
          <img class="rounded-inherit" :src="item.icon" alt="">
          <span class="absolute inset-0 flex-center bg-black/[0.3] break-all rounded-inherit title">
            {{ item.title }}
          </span>
        </li>
      </ul>
    </div>

  </div>
</template>

<style scoped>
.title:lang(de) {
  font-size: 13px;
}

.title:lang(en) {
  font-size: 13px;
}

.title:lang(it) {
  font-size: 13px;
}

.title:lang(es) {
  font-size: 12px;
}

.title:lang(fr) {
  font-size: 13px;
}

.title:lang(pt) {
  font-size: 12px;
}
</style>

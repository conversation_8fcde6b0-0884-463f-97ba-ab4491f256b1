
.driver-popover.popover-container-class {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(10px);
}

.driver-popover.popover-container-class::before {
  --top: 42px;
  content: "";
  width: 85px;
  height: 81px;
  display: block;
  position: absolute;
  top: calc(var(--top) + 20px);
  left: -20px;
  z-index: 100;
  transform: translateX(-100%);
  background: url("../../../../assets/images/text-to-video/guide-animation/arrow-icon.svg") no-repeat;
  background-size: 100% 100%;
}

.driver-popover.popover-container-class .driver-popover-arrow {
  display: none;
}


.driver-popover.setting-container-class {
  display: flex !important;
  justify-content: center;
  align-items: center;
  transform: translateY(100%) translateX(-20px);
  max-width: 500px;
}

.driver-popover.popover-container-class.setting-container-class::before {
  top: -10px;
  left: -20px;
}

.driver-popover.setting-container-class .driver-popover-footer {
  margin-top: 0;
}

.driver-popover.popover-container-class.img-to-video-container-class::before {
  top: 100px;
  left: -20px;
}

.driver-popover.popover-container-class.advanced-container-class::before {
  top: 40px;
  left: -10px;
}

.driver-popover.popover-container-class.basic-container-class::before {
  top: 40px;
  left: -10px;
}


.driver-popover.popover-container-class.replace-container-class {
  transform: translateX(20px) translateY(-40px);
}

.driver-popover.popover-container-class.replace-container-class:lang(de) {
  transform: translateX(-20px) translateY(-40px);
}

.driver-popover.popover-container-class.replace-container-class:lang(es) {
  transform: translateX(-30px) translateY(-40px);
}

.driver-popover.popover-container-class.replace-container-class:lang(fr) {
  transform: translateX(-30px) translateY(-40px);
}

.driver-popover.popover-container-class.replace-container-class:lang(pt) {
  transform: translateX(0px) translateY(-40px);
}

.driver-popover.popover-container-class.replace-container-class::before {
  top: 70px;
  left: -10px;
}


.driver-popover.editor-container-class{
  display: flex !important;
  justify-content: center;
  align-items: center;
  transform: translateY(200%) translateX(0%);
  max-width: fit-content;
}

.driver-popover.popover-container-class.editor-container-class::before {
  top: -50%;
  left: 100%;
  transform: translate(-50px, -100%) rotate(120deg);
}

.driver-popover.editor-container-class.special-left-class {
  transform: translateY(100%);
}

.driver-popover.popover-container-class.editor-container-class.special-left-class::before {
  top: -100%;
  left: 20px;
  transform: translate(-160%, 50%) rotate(0deg);
}


.driver-popover.editor-container-class .driver-popover-footer {
  margin-top: 0;
}


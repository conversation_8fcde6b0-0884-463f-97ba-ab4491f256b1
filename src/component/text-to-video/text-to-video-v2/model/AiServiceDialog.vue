<script setup lang="ts">
import BaseDialog from '@/component/dialog/BaseDialog.vue'
import {useI18n} from '@/locales'

const {t} = useI18n()

defineEmits<{
  clickCancel: [],
  clickOk: []
}>()

</script>

<template>
  <BaseDialog :enable-teleport="false">
    <div class="bg-[#353437] py-[40px] rounded-[28px] w-[90vw] 2xl:w-[60vw] 3xl:w-[40vw] flex flex-col text-[16px] text-white">
      <h1 class="font-bold text-center text-[30px]">AIGC创作协议</h1>
      <p class="mt-[20px] px-[30px]">
        欢迎您使用录咖 AIGC 创作功能。您应充分阅读本协议，并在完全理解并接受本协议内容后，再使用对应功能。
      </p>
      <ul class="mt-[20px] flex flex-col gap-3 list-decimal px-[60px] pr-[40px]">
        <li>
          您应保证您对上传或输入的内容拥有合法权利，不存在任何侵犯第三方权益的内容。此外，您上传或输入的内容不得违反任何法律法规的规定或社会公序良俗。您应当使用清晰、准确且积极正面的语言来指导AI生成文字、图像、视频或音频，并避免模糊不清或者具有潜在误导性的描述。同时，我们可能会采取限制活跃或关停账号等方式处理存在违规行为的账号。
        </li>
        <li>
          您通过本功能生成的内容仅限于非商业目的使用，如学习或科研目的。
        </li>
        <li>
          为更好地符合相关法律法规，建设和谐的网络生态环境。我们可能会采取内容审核措施，过滤可能涉及色情、暴力、恐怖、歧视、虚假或其他类型的违规信息。
        </li>

        <li>
          录咖AIGC功能由人工智能模型提供支持，鉴于算法服务的不准确性及互联网服务的不稳定性，因此我们不对生成结果做任何保证，其可能存在瑕疵。但我们会尽努力为您提供完整且优质的服务。
        </li>

        <li>
          本协议作为《个人信息保护政策》《服务协议》附件，与之具有同等法律效力。如本协议内容与《个人信息保护政策》《服务协议》内容相冲突的，则以本协议为准。
        </li>
      </ul>
      <p class="text-end px-[40px] mt-[20px]">
        深圳市网旭科技有限公司
      </p>
      <p class="mt-[10px] text-end px-[40px]">
        2024年6月7日
      </p>
      <div
        class="flex-center gap-[20px] mt-[30px] *:flex-center *:rounded-[32px] *:min-w-[130px] *:h-[32px] *:cursor-pointer">
        <div class="border-[#ccc] border hover:opacity-70" @click="$emit('clickCancel')">
          {{ t('disagree') }}
        </div>
        <div class="bg-primary-color hover:opacity-70" @click="$emit('clickOk')">
          {{ t('agree') }}
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<script setup lang="ts">
import {ModelType} from '@/interface/TextToVideo'
import useTextToVideoModel from '@/component/text-to-video/text-to-video-v2/useTextToVideoModel';
import {useI18n} from '@/locales';
import {onMounted} from 'vue';
import GuideAnimation, {GuideAnimationType} from '@/component/text-to-video/text-to-video-v2/animation/GuideAnimation';
import {updateInfoKey} from '@/component/global/load/globalLoad';

const {t} = useI18n()
const {modelInfo} = useTextToVideoModel()

const list = [
  {
    name: t('textToVideoProModel'),
    type: ModelType.advanced,
    title: t('textToVideoProModelTitle'),
    desc: t('textToVideoProModelDesc').replace('#', '30'),
  },
  {
    name: t('textToVideoBasicModel'),
    type: ModelType.basic,
    title: t('textToVideoBasicModelTitle'),
    desc: t('textToVideoBasicModelDesc').replace('#', '3'),
  },
]

function handleClickModel(type: ModelType) {
  modelInfo.setModel(type)
  if (type === ModelType.basic) {
    const {startAnimation} = GuideAnimation.checkStartGuideAnimation(GuideAnimationType.basic)
    startAnimation()
  }
}

onMounted(() => {
  if (Boolean(localStorage.getItem(updateInfoKey)) === true) {
    requestAnimationFrame(() => {
      const {startAnimation} = GuideAnimation.checkStartGuideAnimation(GuideAnimationType.advanced)
      startAnimation()
    })
  }
})
</script>

<template>
  <div
    id="advanced-model-id"
    class="flex justify-center items-center text-[16px] text-white text-center rounded-[32px] border border-[#8C8B9980]"
  >
    <div
      v-for="item in list"
      class="min-w-[138px] px-[16px] py-[8px] rounded-[inherit] cursor-pointer group/item relative"
      :class="[item.type === modelInfo.model ? 'bg-primary-color' : '']"
      @click="handleClickModel(item.type)"
    >
      <span class="flex justify-center items-center">{{ item.name }}</span>
      <div
        class="scale-0 group-hover/item:scale-100 transition origin-top absolute w-max p-[12px] top-full left-1/2 -translate-x-1/2 bg-[#2C2C30] shadow-[0_10px_20px_0_rgba(30,61,36,0.10)] translate-y-[10px] z-10 rounded-[8px]"
      >
        <div class="text-start">
          <p class="text-white text-[14px]">{{ item.title }}</p>
          <p class="text-white/[0.5] text-[12px]">{{ item.desc }}</p>
        </div>
      </div>
    </div>
  </div>

</template>

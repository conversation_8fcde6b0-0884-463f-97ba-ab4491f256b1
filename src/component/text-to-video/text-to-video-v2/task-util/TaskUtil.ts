import {querySceneInfoApi} from '@/api/textToVideoApi';
import {QuerySceneTaskType} from '@/interface/TextToVideo';
import {checkItemStatus} from '@/component/text-to-video/text-to-video-v2/useTextToVideoV2';
import QueryTaskError from '@/services/exception/QueryTaskError';

type QuerySceneOptionType = {
  onProgressChange: (progress: number) => void
}

class TaskUtil {
  private _timer: any = null

  /**
   * 根据taskId查询场景信息
   * @return 场景信息 {Promise<QuerySceneTaskType>}
   */
  public async querySceneInfoByTaskIds(taskIds: string[], options: QuerySceneOptionType): Promise<QuerySceneTaskType> {
    return new Promise(async (resolve, reject) => {
      try {
        const result = await querySceneInfoApi(taskIds[0])
        const s = checkItemStatus(result)
        if (s === 'fail') {
          throw new QueryTaskError('SingleTaskUtil', result)
        } else if (s === 'success') {
          resolve(result)
        } else if (s === 'loading') {
          options.onProgressChange(result.progress)
          this._timer = setTimeout(() => {
            this.querySceneInfoByTaskIds(taskIds, options).then(resolve).catch(reject)
          }, 3000)
        }
      } catch (e) {
        reject(String(e))
      }
    })
  }

  public dispose() {
    clearTimeout(this._timer)
  }
}


const taskUtil = new TaskUtil()

export default taskUtil

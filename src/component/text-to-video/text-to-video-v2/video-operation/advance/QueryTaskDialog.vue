<script setup lang="ts">
import CircleProcess from '@/component/text-to-video/text-to-video-v2/video-manager/CircleProcess.vue'
import {useI18n} from '@/locales'
import {computed, onMounted, onUnmounted, reactive} from 'vue'
import {VideoStatusType} from '@/interface/TextToVideo'
import {handleRetryApi} from '@/api/textToVideoApi'
import taskUtil from '@/component/text-to-video/text-to-video-v2/task-util/TaskUtil'

const {t} = useI18n()
const props = defineProps<{
  taskId: string,
}>()

const emit = defineEmits<{
  success: [],
  fail: [msg: unknown],
  clickToVideoManager: [],
}>()

const info = reactive({
  progress: 0,
  resultType: 'loading' as VideoStatusType,
  handleClickToVideoManager() {
    emit('clickToVideoManager')
  },
  handleRetry() {
    info.progress = 0
    info.resultType = 'loading'
    handleRetryApi(props.taskId)
      .then((taskId: string) => {

        //加点延迟, 让后台有时间处理任务
        setTimeout(() => {
          this.startQuery(taskId)
        }, 2000)

      })
  },
  async startQuery(taskId: string) {
    try {
      info.progress = 0
      const result = await taskUtil.querySceneInfoByTaskIds([taskId], {
        onProgressChange(progress) {
          info.progress = progress
        }
      })
      if (result.progress === 100) {
        emit('success')
      } else {
        console.error('进度问题')
      }
    } catch (e) {
      console.error(e)
      info.resultType = 'fail'
      emit('fail', e)
    }
  },
  dispose() {
    taskUtil.dispose()
  }
})


onMounted(() => {
  info.startQuery(props.taskId)
})
onUnmounted(() => {
  info.dispose()
})

const destRef = computed(() => {
  return t('textToVideoSceneLoadingDesc', [`<span class="text-primary-color underline underline-offset-2 cursor-pointer">`, `</span>`])
})

</script>

<template>
  <div
    class="z-50 fixed w-screen h-screen inset-0 flex flex-col justify-center items-center text-white gap-[8px] rounded-inherit bg-black/[0.3] backdrop-blur-[15px]"
  >
    <div class="flex-center flex-col" v-if="info.resultType==='loading'">
      <CircleProcess :size="180" :percentage="info.progress" :stoke-width="2">
        <template #center>
          <div class="text-white font-semibold">
            <span class="text-[56px]">{{ info.progress }}</span>
            <span class="text-[28px]">%</span>
          </div>
        </template>
      </CircleProcess>
      <p class="text-[28px] mt-[16px] mb-[30px]">{{ t('textToVideoSceneLoadingTitle') }}...</p>
      <p class="text-[24px] cursor-pointer" @click="info.handleClickToVideoManager" v-html="destRef"></p>
    </div>
    <div v-if="info.resultType==='fail'" @click="info.handleRetry">
      <p class="text-[36px] text-danger-color">{{ t('generationFailed') }}</p>
      <div class="flex-center gap-[16px] hover:text-primary-color text-white cursor-pointer">
        <svg width="32" height="32" viewBox="0 0 38 36" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M28.8 29.8C26 32.4 22.2 34 18 34C9.2 34 2 26.8 2 18C2 9.2 9.2 2 18 2C26.8 2 34 9.2 34 18"
                stroke="currentColor" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                stroke-linejoin="round"/>
          <path d="M36.4008 11.2002L34.0008 18.0002L28.8008 13.0002L36.4008 11.2002Z" fill="currentColor"
                stroke="currentColor"
                stroke-width="3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="text-[32px]">{{ t('textToVideoRetry') }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>

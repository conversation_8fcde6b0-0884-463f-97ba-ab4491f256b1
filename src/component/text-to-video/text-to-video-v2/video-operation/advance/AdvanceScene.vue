<script setup lang="ts">
import {computed, onBeforeUnmount, onMounted} from 'vue';
import SceneList from '@/component/text-to-video/text-to-video-v2/video-operation/advance/scene-list/SceneList.vue';
import useTextToVideoModel from '@/component/text-to-video/text-to-video-v2/useTextToVideoModel'
import ScenePlayer from './scene-player/ScenePlayer.vue'
import magicIcon from '@/assets/images/text-to-video/function-icon/prompt-icon.svg'
import vipIcon from '@/assets/images/text-to-video/function-icon/diamond-icon.svg'
import TextToVideoTrack from '@/track/TextToVideoTrack';
import {textToVideoPointCost} from '@/component/text-to-video/text-to-video-v2/useTextToVideoV2';
import {useI18n} from '@/locales';
import {getPointText} from '@/utils/util';
import {SceneStatus} from '@/interface/TextToVideo';
import useLoginService from '@/services/useLoginService';
import showDialog from '@/render/showDialog';
import GuideAnimation, {GuideAnimationType} from '@/component/text-to-video/text-to-video-v2/animation/GuideAnimation';
import {removeUrlSearchParams} from '@/utils/browser';
import LimitDialog from '@/render/LimitDialog';
import PricingDialog from '@/render/PricingDialog';

const {t} = useI18n()
const props = defineProps<{
  taskId: string,
}>()

const {pointCountRef, isVipRef} = useLoginService()

const {advanceTaskInfo, handleClickAllImgToVideo} = useTextToVideoModel()

const addTaskIdQuery = (taskId: string) => {
  const url = new URL(location.toString())
  url.searchParams.set('task-id', taskId)
  history.replaceState({}, "", url)
}

onMounted(() => {
  if (props.taskId) {
    addTaskIdQuery(props.taskId)
  }
  //进入列表查询场景信息
  advanceTaskInfo.getSceneInfoByTaskId(props.taskId)
  TextToVideoTrack.goToSceneList(advanceTaskInfo.sceneList?.length ?? 0)
  _handleSceneAnimation()
})

//列表可见的时候需要开始动画
function _handleSceneAnimation() {

  //需要让第一个img场景显示hover状态, 并且给他的container设置id用于引导动画选中
  const index = advanceTaskInfo.sceneList.findIndex(item => item.item_status === SceneStatus.img)
  const listEl = document.querySelector<HTMLUListElement>('#scene-list-id')!
  const itemEl = listEl.children[index] as HTMLElement
  const imgToVideoBtnEl = itemEl?.querySelector('.img-to-video-animation-item') as HTMLElement
  const imgToVideoBtnContainerEl = itemEl?.querySelector('.img-to-video-animation-container') as HTMLElement
  const replaceBtnEl = itemEl?.querySelector('.replace-animation') as HTMLElement

  const {enable, startAnimation} = GuideAnimation.checkStartGuideAnimation(GuideAnimationType.sceneImgToVideo, {
    onstop() {
      itemEl.classList.remove('active-animation')
      imgToVideoBtnEl.classList.remove('!flex-center')
      imgToVideoBtnContainerEl.id = ''
      replaceBtnEl.id = ''
    }
  })
  if (itemEl && imgToVideoBtnEl && imgToVideoBtnContainerEl && replaceBtnEl && enable) {
    itemEl.classList.add('active-animation')
    imgToVideoBtnEl.classList.add('!flex-center')
    imgToVideoBtnContainerEl.id = 'img-to-video-animation-active-item-id'
    replaceBtnEl.id = 'img-to-video-animation-active-item-replace-id'
    requestAnimationFrame(() => {
      startAnimation()
    })
  }
}

const allPointNumRef = computed(() => {
  //需要转换的数量
  const count = advanceTaskInfo.sceneList.filter((item, index) => {
    if (item.item_status === SceneStatus.img || item.item_status === SceneStatus.imgToVideoFail || item.item_status === undefined) {
      return true
    }
  }).length
  //每个需要消耗的点数
  const singlePoint = textToVideoPointCost.sceneImgToVideo

  return {
    //总共预计消耗的单数数量
    totalPoint: count * singlePoint,
    //单个消耗的点数
    singlePoint: singlePoint,
    //多少个
    count: count
  }
})

async function handleClickAll() {
  TextToVideoTrack.clickAllImgToVideo().then()
  //检查点数是否足够
  if (pointCountRef.value < allPointNumRef.value.totalPoint) {
    const result = await LimitDialog.showNoPointDialog(allPointNumRef.value.totalPoint)
    const source = PricingDialog.source.allPicToVideoNotEnough
    if (result === 'ok') {
      if (isVipRef.value) {
        PricingDialog.showVipWithPointDialog({
          source: source,
        }).then()
      } else {
        PricingDialog.showOnlyVipDialog({
          source: source,
        }).then()
      }
    }
    return
  }

  const pointInfo = allPointNumRef.value
  showDialog(t('textToVideoAllImgToVideoConfirmTitle').replace('#', String(pointInfo.totalPoint)), {
    subtitle: t('textToVideoAllImgToVideoConfirmSubtitle').replace('#', `${pointInfo.count}*${pointInfo.singlePoint}`),
    onClickOk(close) {
      handleClickAllImgToVideo()
      close()
    }
  })

}

onBeforeUnmount(() => {
  removeUrlSearchParams('task-id')
})
</script>

<template>
  <div class="flex gap-[25px] w-full">
    <div class="w-[35%] h-full">
      <SceneList class="max-h-[60vh]" id="scene-list-id"/>
      <div
        v-if="allPointNumRef.totalPoint>0"
        class="flex-center h-[40px] bg-[#EEEEF0]/[0.1] mt-[16px] rounded-[8px] w-fit px-[20px] cursor-pointer"
        @click="handleClickAll"
      >
        <img class="size-[14px]" :src="magicIcon" alt="">
        <span class="text-[16px] font-semibold ml-[8px] mr-[16px]">{{ t('textToVideoAllImgToVideo') }}</span>
        <img :src="vipIcon" alt="">
        <span class="text-[12px] ml-1">{{ getPointText(allPointNumRef.totalPoint) }}</span>
      </div>
    </div>
    <div class="flex-1">
      <ScenePlayer/>
    </div>
  </div>
</template>

<style scoped>

</style>

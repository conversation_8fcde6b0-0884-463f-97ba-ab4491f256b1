<script setup lang="ts">
import {FontFamilyType, SubtitlePosition} from '@/interface/TextToVideo'
import useTextToVideoV2 from '@/component/text-to-video/text-to-video-v2/useTextToVideoV2'
import TextToVideoDropdown from './base/TextToVideoDropdown.vue'
import {useI18n} from '@/locales'
import {computed} from 'vue'
import TheCheckBox from '@/component/text-to-video/text-to-video-v2/video-operation/settings/TheCheckBoxSquare.vue'
import TextToVideoColorPicker from './TextToVideoColorPicker.vue'

const {t} = useI18n()
const {settingInfo, defaultSettingInfo} = useTextToVideoV2()
const sizeList = [20, 24, 28, 32, 36, 40, 46, 52, 58, 64, 70, 80]
const sizeListBorder = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
const fontTypeList = [
  {
    type: FontFamilyType['microsoft_Yahei'],
    name: t('aiSubtitleStyleYaHei'),
    value: '微软雅黑',
  },
  {
    type: FontFamilyType['liShu'],
    name: t('aiSubtitleStyleLishu'),
    value: '隶书,SimLi',
  },
  {
    type: FontFamilyType['italics'],
    name: t('aiSubtitleStyleSimKai'),
    value: '楷体,楷体_GB2312,SimKai,STKaiti',
  },
  {
    type: FontFamilyType['blackFont'],
    name: t('aiSubtitleStyleSimHei'),
    value: '黑体,SimHei,Heiti SC',
  },
  {
    type: FontFamilyType['songTi'],
    name: t('aiSubtitleStyleSongTi'),
    value: '宋体,SimSun,Songti SC',
  },
  {
    type: FontFamilyType['time_new_roman'],
    name: 'Times New Roman',
    value: 'times new roman',
  },
  {
    type: FontFamilyType['impact_chicago'],
    name: 'Impact',
    value: 'impact,chicago',
  },
  {
    type: FontFamilyType['comic_sans_ms'],
    name: 'Comic Sans Ms',
    value: 'comic sans ms',
  },
  {
    type: FontFamilyType['open_sans'],
    name: 'Open Sans',
    value: 'Open Sans',
  },
  {
    type: FontFamilyType['andale_mono'],
    name: 'Andale Mono',
    value: 'andale mono',
  },
]
const fontFamilyTitleRef = computed(() => {
  return fontTypeList.find(i => i.type === settingInfo.fontFamily)?.name
})

const subtitlePositionList = [
  {
    title: t('settingBottom'),
    value: 'bottom',
  },
  {
    title: t('settingCenter'),
    value: 'center',
  },
  {
    title: t('settingTop'),
    value: 'top',
  }
]
const positionRef = computed(() => {
  return subtitlePositionList.find(i => i.value === settingInfo.subtitlePosition)?.title
})
</script>

<template>
  <div>
    <div
      class="flex items-center gap-[8px] text-white/[0.6] cursor-pointer w-fit"
      @click="settingInfo.enableSubtitle=!settingInfo.enableSubtitle"
    >
      <TheCheckBox :is-active="settingInfo.enableSubtitle" class="rounded-[4px]"/>
      {{ t('settingSubtitle') }}
    </div>
    <div
      class="mt-[16px] grid grid-rows-2 grid-cols-[auto_auto] justify-between items-center gap-[16px] transition"
      :class="[settingInfo.enableSubtitle?'':'opacity-30 pointer-events-none']"
    >
      <div class="text-end">{{ t('aiSubtitleFontStyle') }}</div>
      <div class="flex gap-[16px] h-[36px] 2xl:h-[44px]">

        <TextToVideoDropdown
          class="w-[150px]"
          title-class="line-clamp-1"
          :select-title="fontFamilyTitleRef??''"
          :select-list="fontTypeList"
          @item-click="settingInfo.fontFamily = $event.type"
        >
          <template #default="{item:{name,value}}">
            <span
              :class="{active:fontFamilyTitleRef===name}"
              :style="{fontFamily:value}"
            >
              {{ name }}
            </span>
          </template>
        </TextToVideoDropdown>

        <TextToVideoDropdown
          class="w-[90px]"
          :select-title="settingInfo.fontSize+'px'"
          :select-list="sizeList"
          @item-click="settingInfo.fontSize = $event"
        >
          <template #default="{item}">
            <span :class="{active:item===settingInfo.fontSize}">{{ item }}</span>
          </template>
        </TextToVideoDropdown>

        <div class="w-[150px] border border-[#8C8B99]/[0.2] rounded-8px">
          <TextToVideoColorPicker
            v-model:color="settingInfo.fontColor"
            :default-color="defaultSettingInfo.fontColor"
            class="text-[#fff] opacity-100"
          />
        </div>
      </div>
      <div class="text-end">{{ t('textToVideoBorders') }}</div>
      <div class="h-full flex gap-[16px]">

        <div class="w-[150px] border border-[#8C8B99]/[0.2] rounded-8px">
          <TextToVideoColorPicker
            v-model:color="settingInfo.borderColor"
            :default-color="defaultSettingInfo.borderColor"
            class="text-[#fff] opacity-100"
          />
        </div>

        <TextToVideoDropdown
          class="w-[90px]"
          :select-title="settingInfo.borderWidth+'px'"
          :select-list="sizeListBorder"
          @item-click="settingInfo.borderWidth = $event"
        >
          <template #default="{item}">
            <span :class="{active:item===settingInfo.borderWidth}">{{ item }}</span>
          </template>
        </TextToVideoDropdown>

      </div>
      <div class="text-end">{{ t('textToVideoPosition') }}</div>
      <div class="h-[36px] 2xl:h-[44px]">
        <TextToVideoDropdown
          class="w-[150px]"
          :select-title="positionRef??''"
          :select-list="subtitlePositionList"
          @item-click="settingInfo.subtitlePosition=($event.value as SubtitlePosition)"
        >
          <template #default="{item:{title}}">
            <span :class="{active:title===positionRef}">{{ title }}</span>
          </template>
        </TextToVideoDropdown>
      </div>
    </div>

  </div>
</template>

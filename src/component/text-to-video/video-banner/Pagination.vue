<script setup lang="ts">
const props = defineProps<{
  isPlaying?: boolean,
  itemLength: number,
  activeIndex: number,
  progress: string,
}>()

const emit = defineEmits<{
  clickPlay: [],
  clickItem: [number]
}>()

const handleClickPlay = () => {
  emit('clickPlay')
}
</script>

<template>
  <div class="">
    <ul class="flex rounded-[48px] bg-[#EEE] px-[32px] py-[24px] gap-[16px] !w-fit">
      <li
        v-for="(item,index) in itemLength"
        class="w-[10px] h-[10px] rounded-full bg-deep-color/[0.4] cursor-pointer transition-all duration-300 overflow-hidden hover:bg-deep-color"
        :class="{
          'video-active': index === activeIndex,
        }"
        @click="$emit('clickItem',index)"
      >
      </li>
    </ul>
  </div>
</template>

<style scoped>
.video-active {
  @apply w-[62px] after:content-[''] after:w-[0%] after:h-[10px] after:block after:bg-deep-color after:transition-all after:rounded-full pointer-events-none;
}

.video-active::after {
  width: v-bind('props.progress');
}
</style>

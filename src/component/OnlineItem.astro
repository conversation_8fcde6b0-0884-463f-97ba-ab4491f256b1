---
import onlineBg from "../assets/images/online-edit/online-bg.svg"
const {icon,alt} = Astro.props
---
<div class="w-full lg:h-[105px] h-[30px]">
  <div class="relative w-full h-full">
    <img src={onlineBg} alt='background image' loading="lazy">
    <img class="icon" src={icon} alt={alt} loading="lazy">
  </div>
</div>

<style>
  img {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
  }

  .icon {
    top: 50%;
    transform: translate(-50%, -50%);
  }
</style>

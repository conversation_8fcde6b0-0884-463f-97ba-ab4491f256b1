---
import {useI18n} from '@/locales';
import {getRegionPath} from '../../utils/util';
import {getLang} from '@central/i18n';
const {t} = useI18n();

const serviceList = [
  {
    title: t("Terms"),
    href: getRegionPath() + '/terms',
  },
  {
    title: t("Privacy"),
    href: getRegionPath() + '/privacy',
  },
  {
    title: t("Cookies Policy"),
    href: getRegionPath() + '/cookies-policy',
  },
  getLang() === 'zh' && {
    title: '许可协议',
    href: getRegionPath() + '/user-license',
  }

]
---
<div class="w-full h-[52px] text-[14px] flex-center text-dark dark:text-white opacity-60 gap-[30px]">
  <span>{t('RecCloud Copyright')}</span>
  {
    serviceList.map((item) => (
      item && <a class="hover:underline" rel="noopener noreferrer" target="_blank" href={item.href}>{item.title}</a>
    ))
  }
</div>

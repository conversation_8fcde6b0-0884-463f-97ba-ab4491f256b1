<script setup lang="ts">
import {useId} from 'vue';

const props = defineProps<{
  class?: string;
}>()

const uniqueId = useId()
</script>

<template>
  <svg width="25" height="14" viewBox="0 0 25 14" fill="none" xmlns="http://www.w3.org/2000/svg" :class="props.class">
    <path d="M7 1L1 7.24L7 13" :stroke="`url(#paint0_${uniqueId})`" stroke-width="1.5" stroke-linecap="round"
          stroke-linejoin="round"/>
    <path d="M18 1L24 7.24L18 13" :stroke="`url(#paint1_${uniqueId})`" stroke-width="1.5" stroke-linecap="round"
          stroke-linejoin="round"/>
    <path d="M14 1L11 13" :stroke="`url(#paint2_${uniqueId})`" stroke-width="1.5" stroke-linecap="round"/>
    <defs>
      <linearGradient :id="`paint0_${uniqueId}`" x1="1" y1="7" x2="7.18524" y2="7.39866"
                      gradientUnits="userSpaceOnUse">
        <stop stop-color="var(--start)"/>
        <stop offset="1" stop-color="var(--end)"/>
      </linearGradient>
      <linearGradient :id="`paint1_${uniqueId}`" x1="24" y1="7" x2="17.8148" y2="7.39866"
                      gradientUnits="userSpaceOnUse">
        <stop stop-color="var(--start)"/>
        <stop offset="1" stop-color="var(--end)"/>
      </linearGradient>
      <linearGradient :id="`paint2_${uniqueId}`" x1="11" y1="7" x2="14.1022" y2="7.09997"
                      gradientUnits="userSpaceOnUse">
        <stop stop-color="var(--start)"/>
        <stop offset="1" stop-color="var(--end)"/>
      </linearGradient>
    </defs>
  </svg>
</template>

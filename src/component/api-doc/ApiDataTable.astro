---
import {useI18n} from '../../locales'

const {t} = useI18n()

interface Item {
  title: string
  descHTML: string | string[]
}

export interface Props {
  class?: string,
  tableHeadKey: string,
  tableHeadValue: string,
  tableList: Item[]
}

const {class: classname, tableList, tableHeadKey, tableHeadValue} = Astro.props
---

<div
  class="w-full border border-[#e2e8f0] dark:border-[rgba(140,139,153,0.2)] overflow-hidden rounded-[12px]"
  class:list={[classname]}
>
  <table class="w-full">
    <thead class="bg-[#F3FBF4] dark:bg-dark">
      <tr class="text-[18px]">
        <th class="text-start w-[346px] font-bold"><span>{tableHeadKey}</span></th>
        <th class="text-start !px-[80px]"><span>{tableHeadValue}</span></th>
      </tr>
    </thead>

    <tbody>
    {
      tableList.map(item => {
        return item && (
          <tr class="text-center bg-white dark:bg-transparent hover:bg-[#E8F7E9] dark:hover:bg-dark">
            <th
              class="text-[18px] font-bold text-start dark:px-[20px] dark:whitespace-normal pl-[70px] whitespace-nowrap"
              set:html={item.title}></th>
            <td class="text-[16px] py-[20px] text-start pl-[80px] pr-[20px]">
              {
                Array.isArray(item.descHTML)
                  ? item.descHTML.map(desc => (<p set:html={desc}></p>))
                  : item.descHTML
              }
            </td>
          </tr>
        )
      })
    }
    </tbody>
  </table>
</div>

<style>

  table {
    width: 100%;
    border-spacing: 0;
  }

  /* 单元格样式 */
  th, td {
    border-right: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
  }

  /* 暗色模式下的单元格样式 */
  html.dark th, html.dark td {
    border-right: 1px solid rgba(140, 139, 153, 0.2);
    border-bottom: 1px solid rgba(140, 139, 153, 0.2);
  }


  /* 最后一列不要右边框 */
  tr th:last-child, tr td:last-child {
    border-right: none;
  }

  /* 最后一行不要底边框 */
  tbody tr:last-child th, tbody tr:last-child td {
    border-bottom: none;
  }

  th {
    @apply min-h-[64px] p-[20px];
  }

  .title-link {
    @apply text-primary-color font-normal underline cursor-pointer relative;
  }
</style>

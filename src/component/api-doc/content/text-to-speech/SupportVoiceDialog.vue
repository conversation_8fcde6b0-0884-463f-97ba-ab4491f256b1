<script setup lang="ts">
import BaseDialog from '@/component/dialog/BaseDialog.vue';
import {getBaseVoiceList, getProVoiceList} from './support-voice';
import {useI18n} from '@/locales';
import {useEventListener} from '@vueuse/core';

const {t} = useI18n();

const emit = defineEmits<{
  clickClose: []
}>();

interface VoiceData {
  [key: string]: {
    female?: string[];
    male?: string[];
  };
}

const baseVoiceList: VoiceData = getBaseVoiceList()
const proVoiceList: VoiceData = getProVoiceList()

// 转换为数组以便使用索引
const baseVoiceEntries = Object.entries(baseVoiceList)
const proVoiceEntries = Object.entries(proVoiceList)

const stop = useEventListener(document, 'keydown', (e) => {
  if (e.key === 'Escape') {
    handleClickClose()
  }
})

const handleClickClose = () => {
  stop()
  emit('clickClose')
}

</script>

<template>
  <BaseDialog :enable-teleport="false">
    <div
      class="relative bg-[#F8F9FB] dark:bg-dark dark:text-white text-dark pl-[40px] py-[40px] pr-[8px] rounded-[24px]"
    >
      <div
        class="absolute top-0 right-0 w-[70px] h-[50px] flex-center cursor-pointer"
        @click="handleClickClose"
      >
        <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14.3996 13.8008L1.59961 1.34673" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          <path d="M1.59961 13.8L14.0537 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        </svg>

      </div>

      <div
        class="max-h-[62vh] pr-[40px] overflow-auto scroll-bar dark:scroll-dark-gradient"
      >
        <table class="w-full">
          <thead class="sticky -top-[1px] left-0 w-full">
          <tr
            class="*:text-start text-[16px] bg-[#dcefe3] dark:bg-[#1B1B1D] dark:text-white text-dark/[0.8] dark:text-white/[0.8]"
          >
            <th class="text-inherit min-w-[280px] px-[40px] font-normal" v-html="t('apiDocKey62')"></th>
            <th class="text-inherit min-w-[280px] px-[70px] font-normal h-[110px]" v-html="t('apiDocKey63')"></th>
            <th class="text-inherit min-w-[280px] px-[30px] font-normal" v-html="t('apiDocKey64')"></th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(voices, code, index) in baseVoiceList">
            <td
              v-if="index === 0"
              :rowspan="baseVoiceEntries.length"
              class="text-[18px] font-bold "
            >
              <span class="block size-full text-center mt-[20vh]"> 1</span>
            </td>

            <td>{{ code }}</td>

            <td>
              <div class="text-[16px] flex flex-col gap-[4px] dark:text-white text-dark">
                <template v-if="voices.male && voices.male.length > 0">
                  <div>
                    <span class="text-[14px] dark:text-white/[0.8] text-dark/[0.8]">male:</span>
                    <span class="ml-2">[{{ voices.male.join(', ') }}]</span>
                  </div>
                </template>

                <template v-if="voices.female && voices.female.length > 0">
                  <div>
                    <span class="text-[14px] dark:text-white/[0.8] text-dark/[0.8]">female:</span>
                    <span class="ml-2">[{{ voices.female.join(', ') }}]</span>
                  </div>
                </template>

              </div>
            </td>
          </tr>

          <tr v-for="(voices, code, index) in proVoiceList">
            <td
              v-if="index === 0"
              :rowspan="proVoiceEntries.length"
              class="text-[18px] text-center font-bold"
            >
              <div class="block size-full text-center mt-[20vh]">
                <p>2</p>
                <p class="text-[16px] font-normal">{{ t('apiDocKey65') }}</p>
              </div>

            </td>

            <td>{{ code }}</td>

            <td>
              <div class="text-[16px] flex flex-col gap-[4px] dark:text-white text-dark">
                <template v-if="voices.male && voices.male.length > 0">
                  <div>
                    <span class="text-[14px] dark:text-white/[0.8] text-dark/[0.8]">male:</span>
                    <span class="ml-2">[{{ voices.male.join(', ') }}]</span>
                  </div>
                </template>

                <template v-if="voices.female && voices.female.length > 0">
                  <div>
                    <span class="text-[14px] dark:text-white/[0.8] text-dark/[0.8]">female:</span>
                    <span class="ml-2">[{{ voices.female.join(', ') }}]</span>
                  </div>
                </template>
              </div>
            </td>
          </tr>

          </tbody>
        </table>
      </div>
    </div>
  </BaseDialog>
</template>

<style scoped>
table, tr, th, td {
  border: 1px solid rgba(140, 139, 153, 0.2);
  border-collapse: collapse;
}

td {
  padding: 0 70px;
  height: 82px;
}
</style>

---
import Layout from "@/layouts/EasyLayout.astro";
import Header from "@/component/header-v2/Header.vue"
import ApiNav from '@/component/api-doc/ApiNav.astro';
import SaleTop from '@/component/start/views/header/SaleTop.vue'
import ThemeLightIcon from '~icons/api-doc/theme-light.svg'
import ThemeDarkIcon from '~icons/api-doc/theme-dark.svg'
import {getLang} from '@central/i18n';
import {useI18n} from '@/locales';
import {getRegionPath} from '../../utils/util';
const {t} = useI18n()
const seo = await import((`@/metadata/${getLang()}/video-api-doc.ts`)).then(res => res.default)

---
<Layout theme="dark">

  <script is:inline>
    const theme = localStorage.getItem('theme')
    if (theme === 'light') {
      document.documentElement.classList.remove('dark')
      document.documentElement.classList.add('light')
    } else {
      document.documentElement.classList.remove('light')
      document.documentElement.classList.add('dark')
    }
  </script>

  <Header client:load>
    <div slot="login-left">
      <SaleTop
        class="mr-[16px] text-dark"
        slot="login-left"
        title={t('apiPricingKey1')}
        href={`${getRegionPath()}/api-pricing`}
      />
    </div>

    <div
      slot="login-right"
      class="ml-[16px]"
      onclick="toggleTheme()"
    >
      <ThemeLightIcon class="hidden dark:block"/>
      <ThemeDarkIcon class="block dark:hidden"/>

      <script is:inline>
        function toggleTheme() {
          const classList = document.documentElement.classList
          if (classList.contains('dark')) {
            classList.remove('dark')
            classList.add('light')
            localStorage.setItem('theme', 'light')
          } else {
            classList.remove('light')
            classList.add('dark')
            localStorage.setItem('theme', 'dark')
          }
        }

      </script>
    </div>
  </Header>
  <div
    id="api-doc-layout-container-id"
    class="size-full dark:bg-[#1B1B1D] bg-white min-h-screen text-dark dark:text-white [--nav-width:300px] 3xl:[--nav-width:320px]"
  >

    <div
      id="api-doc-nav-container-id"
      class="w-[var(--nav-width)] fixed top-0 left-0 h-screen pt-header-offset z-10 border-r-1 dark:border-transparent"
    >
      <ApiNav/>
    </div>

    <div
      id="api-doc-content-container-id"
      class="pt-header-offset pl-[var(--nav-width)]"
    >
      <slot></slot>
    </div>
  </div>

</Layout>

<style is:global>

  .active-text {
    color: #18AD25;
  }

  html.dark .active-text {
    background: linear-gradient(to right, #D6FB72, #76F9B1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent
  }

  header nav.nav-container {
    display: none;
  }
</style>

<script is:inline define:vars={{title: seo.Title, desc: seo.Description}}>
  document.title = title;

  /**@type {HTMLElement}*/
  const el = document.querySelector('#api-nav-aside-switch-id')
  /**@type {HTMLElement}*/
  const containerEl = document.querySelector('#api-doc-nav-container-id')
  /**@type {HTMLElement}*/
  const contentEl = document.querySelector('#api-doc-content-container-id')

  containerEl.addEventListener('mouseenter', () => {
    el.style.display = 'block'
  })
  containerEl.addEventListener('mouseleave', () => {
    if (options.open) {
      el.style.display = 'none'
    } else {
      el.style.display = 'block'
    }
  })

  const options = {
    _open: true,
    get open() {
      return this._open
    },
    set open(value) {
      this._open = value
      el.dataset.open = value.toString()
      sessionStorage.setItem('_api-nav-aside-switch', value.toString())
      if (value === true) {
        this._showNav()
      } else {
        this._hiddenNav()
      }
    },
    toggle() {
      this.open = !this.open
    },
    _hiddenNav() {
      containerEl.style.setProperty('transform', 'translateX(-100%)')
      el.style.setProperty('transform', 'translateX(100%) translateY(-50%) rotate(180deg)')
      contentEl.style.setProperty('padding-left', '0')
      el.style.display = 'block'
    },
    _showNav() {
      containerEl.style.removeProperty('transform')
      el.style.removeProperty('transform')
      contentEl.style.removeProperty('padding-left')
    }
  }
  options.open = sessionStorage.getItem('_api-nav-aside-switch') !== 'false'
  el.onclick = () => {
    options.toggle()
  }
</script>



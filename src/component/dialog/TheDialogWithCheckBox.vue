<script setup lang="ts">
import TheDialog from '@/component/dialog/TheDialog.vue'
import {ref} from 'vue'
import {updateUserinfo} from '@/services/useLoginService'
import {isClient} from '@vueuse/core';
import {CheckedBoxDialogEmitResult} from '@/interface/OnlineEdit';


const props = defineProps<{
  title: string
  defaultChecked: boolean
  subtitle?: string
  checkLabel?: string
  okText?: string
  cancelText?: string
  cancelBtnClass?: string,
  desc?: string,
  descClass?: string
}>()

const emit = defineEmits<{
  'clickCancel': [params: CheckedBoxDialogEmitResult],
  'clickClose': [params: CheckedBoxDialogEmitResult],
  'clickOk': [params: CheckedBoxDialogEmitResult],
}>()
if (isClient) {
  updateUserinfo()
}

const isCheckedRef = ref(props.defaultChecked)

const handleClose = (type: 'cancel' | 'close') => {
  if (type === 'cancel') {
    emit('clickCancel', {
      isChecked: isCheckedRef.value
    })
  } else if (type === 'close') {
    emit('clickClose', {
      isChecked: isCheckedRef.value
    })
  } else {
    throw new Error('错误类型')
  }
}
const handleOk = async () => {
  emit('clickOk', {
    isChecked: isCheckedRef.value
  })
}

</script>

<template>
  <TheDialog
    :enable-teleport="false"
    @click-cancel="handleClose('cancel')"
    @click-close="handleClose('close')"
    @click-ok="handleOk"
    :cancel-text="cancelText"
    :ok-text="okText"
    :cancel-btn-class="cancelBtnClass"
  >
    <div class="min-h-[158px] max-w-[600px] w-max mx-auto flex justify-center">
      <div class="flex flex-col justify-center items-start text-left gap-[8px] p-[40px]">
        <span>{{ title }}</span>
        <span>{{ subtitle }}</span>
        <span v-if="desc" :class="descClass">{{ desc }}</span>
      </div>
    </div>

    <template #btn-left>
      <div
        class="flex pl-[40px] gap-[8px] justify-start items-center cursor-pointer w-fit mr-auto"
        @click="isCheckedRef = !isCheckedRef"
      >
        <svg v-show="!isCheckedRef" width="18" height="18" viewBox="0 0 18 18" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <rect x="0.5" y="0.5" width="17" height="17" rx="3.5" fill="white" stroke="#CCCCCC"/>
        </svg>

        <svg v-show="isCheckedRef" width="18" height="18" viewBox="0 0 18 18" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <rect x="1.33789" y="0.796875" width="15" height="15" rx="2.5" fill="#18AD25" stroke="#18AD25"/>
          <path d="M4.83789 9.29688L7.43789 12.2969L12.8379 5.29688" stroke="white" stroke-width="2"
                stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        {{ checkLabel }}
      </div>
    </template>
  </TheDialog>
</template>

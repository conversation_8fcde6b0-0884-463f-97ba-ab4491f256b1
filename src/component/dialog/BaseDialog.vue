<!-- BaseDialog 提供mask和slot-center 用于自定义dialog内容 -->
<script lang="ts" setup>
const props = withDefaults(defineProps<{
  class?: string
  enableTeleport?: boolean
}>(), {
  enableTeleport: true
})
</script>

<template>
  <Teleport to="body" :disabled="!enableTeleport">
    <div class="fixed left-0 top-0 w-full h-full z-[10000]" :class="[props.class]">
      <div class="absolute left-0 top-0 w-full h-full mask bg-[rgba(0,0,0,.5)]"></div>
      <slot name="full">
        <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <slot></slot>
        </div>
      </slot>
    </div>
  </Teleport>
</template>

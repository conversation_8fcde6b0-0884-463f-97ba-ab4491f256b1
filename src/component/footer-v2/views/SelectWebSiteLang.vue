<script setup lang="ts">
import {getLang} from '@central/i18n';
import GlobalIcon from '~icons/footer/global.svg';
import ArrowIcon from '~icons/footer/arrow.svg';
import {computed, onMounted, reactive} from 'vue';
import {getPageName} from '@/track/_TrackUtil';

const props = defineProps<{
  class?: string,
  listContainerClass?: string
}>()

const list = reactive([
  {
    lang: 'en',
    url: 'https://reccloud.com',
    title: 'English'
  },
  {
    lang: 'fr',
    url: 'https://reccloud.com/fr',
    title: 'Français'
  },
  {
    lang: 'de',
    url: 'https://reccloud.com/de',
    title: 'Deuts<PERSON>'
  },
  {
    lang: 'es',
    url: 'https://reccloud.com/es',
    title: 'Español'
  },
  {
    lang: 'pt',
    url: 'https://reccloud.com/pt',
    title: 'Português'
  },
  {
    lang: 'jp',
    url: 'https://reccloud.com/jp',
    title: '日本語'
  },
  {
    lang: 'tw',
    url: 'https://reccloud.com/tw',
    title: '繁體中文'
  },
  {
    lang: 'zh',
    url: 'https://reccloud.cn',
    title: '简体中文'
  }
])
const currentLangTitleRef = computed(() => {
  return list.find((item) => item.lang === getLang())?.title ?? ''
})

onMounted(() => {
  const name = getPageName().replace('home', '')
  list.forEach(i => i.url = `${i.url}/${name}`)
})

</script>

<template>
  <div
    :class="props.class"
    class="flex items-center gap-[8px] text-[16px] p-[14px_16px] cursor-pointer rounded-[6px] relative group"
  >
    <div class="size-[20px] flex-center">
      <GlobalIcon/>
    </div>

    <span>{{ currentLangTitleRef }}</span>

    <span><ArrowIcon/></span>

    <ul
      data-list-container-class
      :class="props.listContainerClass"
      class="hidden group-hover:block min-w-[160px] absolute bottom-10 left-1/2 -translate-x-1/2 bg-white dark:bg-dark rounded-[8px] p-[10px_12px] shadow-md whitespace-nowrap text-[16px] leading-8 z-20"
    >
      <li v-for="item in list">
        <a class="block group/link" :href="item.url">
          <span class="dark:text-white/[0.9] group-hover/link:text-theme dark:group-hover/link:text-gradient">
            {{ item.title }}
          </span>
        </a>
      </li>
    </ul>
  </div>
</template>


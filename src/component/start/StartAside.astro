---
import {useI18n} from '../../locales';
import F1Icon from '~icons/start/aside/f1.svg'
import F1HoverIcon from '~icons/start/aside/f1-hover.svg'
import F2Icon from '~icons/start/aside/f2.svg'
import F2HoverIcon from '~icons/start/aside/f2-hover.svg'
import F3Icon from '~icons/start/aside/f3.svg'
import F3HoverIcon from '~icons/start/aside/f3-hover.svg'
import F4Icon from '~icons/start/aside/f4.svg'
import F4HoverIcon from '~icons/start/aside/f4-hover.svg'
import F5Icon from '~icons/start/aside/f5.svg'
import F5HoverIcon from '~icons/start/aside/f5-hover.svg'
import ApiLogoIcon from '~icons/start/aside/api-logo.svg'
import ContactUsIoLogoIcon from '~icons/start/aside/contact-us.svg'
import LogoIcon from '~icons/start/aside/logo.svg'

import downloadIcon from '../../assets/images/header-nav/client-download/reccloud-mobile.png'
import {getLang} from '@central/i18n';
import SelectWebSiteLang from './views/SelectWebSiteLang.astro';
import {getRegionPath} from '../../utils/util';
import {PositionType} from '../../interface/Start';

const {t} = useI18n()

const moreList = [
  {
    title: t('startApi'),
    icon: ApiLogoIcon,
    href: getRegionPath() + '/video-api-doc',
    trackKey: 'click_api',
  },
  {
    title: t('footerContactUs'),
    icon: ContactUsIoLogoIcon,
    href: 'javascript:document.querySelector(".wx-widget-bubble .widget-bubble,.woot-widget-bubble").click();',
    trackKey: 'click_contact_us',
  }
]

const featList = [
  {
    title: t('startAiTools'),
    icon: F1Icon,
    hover: F1HoverIcon,
    href: 'javascript:void(0);',
    trackKey: 'click_ai_tools',
    position: PositionType.tab1,
  },
  {
    title: t('startHistoryTitle'),
    icon: F5Icon,
    hover: F5HoverIcon,
    href: 'javascript:void(0);',
    trackKey: 'click_ai_record',
    mustLogin: true,
    position: PositionType.tab2,
  },
  {
    title: t('startFreeRecord'),
    icon: F2Icon,
    hover: F2HoverIcon,
    href: getRegionPath() + '/free-online-recorder',
    trackKey: 'click_online_recorder',
  },
  {
    title: t('My video'),
    icon: F3Icon,
    hover: F3HoverIcon,
    href: getRegionPath() + '/user',
    trackKey: 'click_my_space',
  },
  {
    title: t('startGroupTitle'),
    icon: F4Icon,
    hover: F4HoverIcon,
    href: getRegionPath() + '/group',
    trackKey: 'click_my_group',
  },
]

---
<div class="bg-[#F7F8F7] h-full p-[32px_26px] start-aside-container">
  <div class="h-full flex flex-col justify-between">
    <!--功能列表-->
    <ul class="flex flex-col gap-[8px]" id="feat-list-container-id">
      {
        featList.map((item, index) => {
          return (
            <li>
              <a
                class="min-w-[226px] h-[48px] flex items-center text-black hover:text-theme group gap-[8px] text-[16px] hover:font-semibold px-[16px] hover:bg-white cursor-pointer rounded-[6px]"
                :class="[index === asideInfo.activeIndex ? 'active !text-theme !font-semibold !bg-white' : '']"
                href={item.href}
                data-track-key={item.trackKey}
                data-must-login={item.mustLogin ? 'true' : 'false'}
                data-position={item.position}
                @click="()=>handleClickItem(item,index)"
              >
                <div class="size-[18px] flex-center">
                  <item.icon :is="item.icon" class="block group-hover:hidden group-[.active]:hidden"/>
                  <item.hover :is="item.hover" class="hidden group-hover:block group-[.active]:block"/>
                </div>

                <span>{item.title}</span>
              </a>
            </li>
          )
        })
      }
    </ul>
    <!--联系我们-->
    <div class="mt-auto">
      <ul class="flex flex-col gap-[8px]">
        {
          moreList.map((item, index) => {
            return (
              <li>
                <a
                  class="min-w-[226px] flex items-center text-black hover:text-theme group gap-[8px] text-[16px] hover:font-medium p-[14px_16px] hover:bg-white cursor-pointer rounded-[6px]"
                  href={item.href}
                  data-track-key={item.trackKey}
                >
                  <span class="size-[20px] flex-center">
                    <item.icon class="text-dark group-hover:text-theme"/>
                  </span>

                  <span>{item.title}</span>
                </a>
              </li>
            )
          })
        }
      </ul>
      {
        getLang() !== 'zh' &&
        <SelectWebSiteLang class="w-full"/>
      }
    </div>
    <!--中文二维码下载-->
    <>
      {
        getLang() === 'zh' && (
          <div class="px-[16px] flex-center flex-col bg-white py-[14px] rounded-[6px] mt-[16px]">
            <p class="flex items-center gap-[6px]">
              <LogoIcon/>
              <span class="text-theme font-semibold">录咖APP</span>
            </p>
            <img src={downloadIcon} alt="下载客户端" class="size-[88px] mt-[12px]"/>
            <p class="mt-[8px] text-[12px] text-dark/[0.7]">扫码下载</p>
          </div>
        )
      }
    </>
  </div>
</div>

<script>
  import TrackUtil from '../../track/_TrackUtil';
  import useLoginService from '../../services/useLoginService';
  import {useUrlSearchParams} from '@vueuse/core';
  import {PositionType, SearchParamsType} from '../../interface/Start';
  import {watch, watchEffect} from 'vue';

  const {isLoginRef, doLogin} = useLoginService();
  const searchParams = useUrlSearchParams<SearchParamsType>('history', {
    initialValue: {
      position: PositionType.tab1,
    },
  })

  watch(isLoginRef, () => {
    if (isLoginRef.value === false) {
      searchParams.position = PositionType.tab1
    }
  })

  function updateAside() {
    const startListEl = document.querySelector<HTMLElement>('#start-list-id')
    const recordListEl = document.querySelector<HTMLElement>('#record-list-id')

    if (searchParams.position === PositionType.tab1) {
      recordListEl?.style.removeProperty('display')
      startListEl?.style.removeProperty('display')
    } else {
      recordListEl?.style.setProperty('display', 'block')
      startListEl?.style.setProperty('display', 'none')
    }
  }

  watch(() => searchParams.position, () => {
    document.querySelectorAll<HTMLElement>('#feat-list-container-id a[data-position]').forEach((item) => {
      if (item.dataset.position === searchParams.position) {
        item.classList.add('active', '!text-theme', '!font-semibold', '!bg-white')
      } else {
        item.classList.remove('active', '!text-theme', '!font-semibold', '!bg-white')
      }
    })
    updateAside()
  }, {immediate: true})

  function init() {
    const el = document.querySelector<HTMLElement>('#feat-list-container-id')
    if (el) {
      Array.from(el.querySelectorAll('a') ?? []).forEach((item, index) => {
        item.onclick = async (ev: Event) => {
          TrackUtil.trackButtonClick(item.dataset.trackKey!).then()
          if (item.dataset.mustLogin === 'true' && isLoginRef.value === false) {
            await doLogin()
          }
          if (item.dataset.position) {
            searchParams.position = item.dataset.position as PositionType
          }
        }
      })
    }
  }

  init()
</script>

---
import {getLang} from '@central/i18n';
import GlobalIcon from '~icons/footer/global.svg';
import ArrowIcon from '~icons/footer/arrow.svg';
import {getPageUrl} from '@central/i18n/utils';
interface Props {
  class?: string,
  listContainerClass?: string
}

const list = [
  {
    lang: 'en',
    url: 'https://reccloud.com',
    title: 'English'
  },
  {
    lang: 'fr',
    url: 'https://reccloud.com/fr',
    title: 'Français'
  },
  {
    lang: 'de',
    url: 'https://reccloud.com/de',
    title: 'Deuts<PERSON>'
  },
  {
    lang: 'es',
    url: 'https://reccloud.com/es',
    title: 'Español'
  },
  {
    lang: 'pt',
    url: 'https://reccloud.com/pt',
    title: 'Português'
  },
  {
    lang: 'jp',
    url: 'https://reccloud.com/jp',
    title: '日本語'
  },
  {
    lang: 'tw',
    url: 'https://reccloud.com/tw',
    title: '繁體中文'
  },
  {
    lang: 'zh',
    url: 'https://reccloud.cn',
    title: '简体中文'
  }
] as const

function getPathname() {
  const pageUrl = getPageUrl(Astro, false);
  return new URL(pageUrl).pathname
}
const {class: className, listContainerClass} = Astro.props
---
<div
  class="flex items-center text-black gap-[8px] text-[16px] p-[14px_16px] cursor-pointer rounded-[6px] relative group"
  class:list={className}
>
  <div class="size-[20px] flex-center">
    <GlobalIcon/>
  </div>

  <span>
    {
      list.find((item) => item.lang === getLang())?.title ?? ''
    }
  </span>

  <span><ArrowIcon/></span>

  <ul
    data-list-container-class
    class="hidden group-hover:block min-w-[160px] absolute bottom-10 left-1/2 -translate-x-1/2 bg-white rounded-[8px] p-[10px_12px] shadow-md whitespace-nowrap text-[16px] leading-8 hover:*:text-theme z-20"
    class:list={[listContainerClass]}
  >
    <>
      {
        list.map((item, index) => (
          <li>
            <a class="block" href={item.url + getPathname()}>
              {item.title}
            </a>
          </li>
        ))
      }
    </>
  </ul>
</div>

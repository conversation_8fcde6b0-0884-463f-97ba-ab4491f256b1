<script setup lang="ts">
import {computed, nextTick, onMounted, reactive, ref, watch, watchEffect} from 'vue'
import BackBtn from '@/component/online-edit/BackBtn.vue'
import showAiSatisfyDialog from '@/render/showAiSatisfyDialog';
import {useI18n} from '@/locales';
import Settings from '@/component/online-edit/text-to-speech-v2/settings/Settings.vue';
import TextInput from '@/component/online-edit/text-to-speech-v2/text-input/TextInput.vue';
import TitleTab from '@/component/online-edit/text-to-speech-v2/multi/views/TitleTab.vue';
import useTextToSpeech from '@/component/online-edit/text-to-speech-v2/useTextToSpeech';
import {PreviewStatus, TabType} from '@/interface/TextToSpeech';
import TheLoading from '@/component/online-edit/TheLoading.vue';
import MultiAudioDetail from '@/component/online-edit/text-to-speech-v2/multi/MultiAudioDetail.vue';
import {createMultiTTSSceneTask, queryMultiTTSSceneTask} from '@/api/textToSpeechApi';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import HistoryList from '@/component/online-edit/text-to-speech-v2/multi/HistoryList.vue';
import {UseElementBounding} from '@vueuse/components'
import useLoginService from '@/services/useLoginService';
import bgGreen from '@images/online-edit/bg-green.svg';
import bgLine from '@images/online-edit/bg-line.svg';
import TextToSpeechGuideAnimation
  from '@/component/online-edit/text-to-speech-v2/guide-animation/TextToSpeechGuideAnimation';
import vipIcon from '@images/text-to-speech/vip-icon.svg';
import {notEnoughPointCode} from '@/services/constants';
import {showPointNotEnoughDialog} from '@/render/showPointDialog';
import showTaskFailDialog, {showNetworkErrorDialog} from '@/render/showTaskFailDialog';
import showDialog from '@/render/showDialog';
import useTextInput from '@/component/online-edit/text-to-speech-v2/text-input/useTextInput';
import {showAiServiceDialog} from '@/render/showAiServiceDialog';
import TextToSpeechTrack from '@/track/TextToSpeechTrack';
import {showDangerToast} from '@/render/showToast';
import {useElementSize} from '@vueuse/core';
import searchIcon from '@images/ai-subtitle/search-icon-bg.svg';
import useHistoryList from '@/component/online-edit/text-to-speech-v2/multi/useHistoryList';
import {debounce} from '@/utils/debounce';
import NoVipCacheClearNotice from '@/component/global/NoVipCacheClearNotice.vue';
import LimitDialog from '@/render/LimitDialog';
import PricingDialog from '@/render/PricingDialog';
import CreateTaskError from '@/services/exception/CreateTaskError';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {
  tabInfo,
  textInputInfo,
  previewStatusInfo,
  invalidInfo,
  multiEquityInfo,
  showStorageDialogWithPricing,
  showCountLimitDialogWithPricing,
  getMultiDeviceId,
} = useTextToSpeech()

const {isLoginRef, doLogin, pointCountRef, checkLimitHasAvailable, isVipRef} = useLoginService()
const {tipsInfo} = useTextInput()
const {historyListInfo} = useHistoryList()
const searchInputElRef = ref<HTMLInputElement>()
const {width: screenWidthRef} = useElementSize(document.body)

const {t} = useI18n()

onMounted(() => {
  const hasQuery = (v: string | null | undefined) => {
    return !!v && !!Reflect.get(PreviewStatus, v)
  }
  //一进页面需要如果地址栏没有v参数, 需要主动设置一次
  const searchMap = new URL(location.toString()).searchParams
  const v = searchMap.get('v')

  //如果地址栏带有task-id, 直接跳转到详情
  if (searchMap.get('task-id')) {
    previewStatusInfo.toMultiDetail(searchMap.get('task-id')!, {writeMode: 'replace'})
    return
  }

  if (hasQuery(v) === false) {
    previewStatusInfo.toPage(PreviewStatus.singleInput, {writeMode: 'replace'})
  } else {
    previewStatusInfo.toPage(v as PreviewStatus, {writeMode: 'replace'})
  }

  showAiSatisfyDialog()
  watchEffect(() => {
    const _ = isLoginRef.value
    multiEquityInfo.fetchFreeInfo()
  })

  _handleStartAnimation()
})

function handleClickBack() {
  history.back()
}

async function handleToHistoryList() {
  TextToSpeechTrack.clickToHistoryList().then()
  if (isLoginRef.value === false) {
    await doLogin()
  }
  previewStatusInfo.toHistoryList()
}

//多人音色引导动画注册
function _handleStartAnimation() {

  const _stop = watch([() => previewStatusInfo.isMultiInput, () => textInputInfo.text], () => {
    const {checkEnable, doStart} = TextToSpeechGuideAnimation.startMultiSceneAnimation()
    if (checkEnable() === false) {
      return
    }

    if (previewStatusInfo.isMultiInput && textInputInfo.text.length > 0) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          doStart()
          _stop()
        })
      })
    }

  })
}

function handleUpdateTab(index: TabType) {
  if (index === TabType.multi) {
    previewStatusInfo.toMultiInput()
    TextToSpeechTrack.clickMultiTabItem().then()
  } else if (index === TabType.single) {
    previewStatusInfo.toSingleInput()
  }
}

//多人音色场景info
const multiInfo = reactive({
  progress: 0,
  isLoading: false,
})

async function handleClickCreateSceneListTask() {
  TextToSpeechTrack.clickStartSmartMatching().then()
  //最小50字
  const minCount = 50
  if (textInputInfo.text.length < minCount) {
    showDangerToast(t('smartMatchTextTooShortTitle').replace('#', minCount.toString()))
    return
  }

  const enableAiService = await showAiServiceDialog()
  if (enableAiService === false) {
    //点击了不同意
    return
  }


  if (isLoginRef.value === false) {
    await doLogin()
  }
  const enableLimit = await checkLimitHasAvailable()

  if (enableLimit.countHasAvailable === false) {
    showCountLimitDialogWithPricing().then()
    console.log('数量限制')
    return
  } else if (enableLimit.storageHasAvailable === false) {
    showStorageDialogWithPricing().then()
    console.log('大小限制')
    return
  }

  await multiEquityInfo.fetchFreeInfo()

  let text = textInputInfo.text
  //计算消耗点数
  const cost = Math.ceil(text.length / multiEquityInfo.price)

  if (multiEquityInfo.existFreeCount) {

  } else {
    if (pointCountRef.value < cost) {
      //点数不足
      if (isVipRef.value) {
        const result = await LimitDialog.showNoPointDialog(cost)
        if (result === 'ok') {
          PricingDialog.showVipWithPointDialog().then()
        }
      } else {
        const result = await LimitDialog.showNoTrialTimeDialog(cost)
        if (result === 'ok') {
          PricingDialog.showOnlyVipDialog({
            source: PricingDialog.source.pointNotEnough
          }).then()
        }
      }
      return
    }

  }

  try {
    invalidInfo.isShowInvalidStar = false
    invalidInfo.enableDisableMatchWords = false
    multiInfo.progress = 0
    multiInfo.isLoading = true
    TextToSpeechTrack.realStartSmartMatching({
      text: text,
      text_count: text.length,
    }).then()
    const result = await _handleMultiTTSTask(text)
    previewStatusInfo.toMultiDetail(result.task_id)
    multiEquityInfo.fetchFreeInfo().then()
    multiInfo.isLoading = false
    TextToSpeechTrack.successSmartMatching({
      role_count: result?.characters?.length ?? 0,
      text_count: result.contents?.reduce((acc, cur) => acc + cur.content.length, 0),
      text: result.contents?.map(item => item.content).join('\n')
    }).then()
    TextToSpeechTrack.smartMatchingResult({
      result: 'success',
      role_count: result?.characters?.length ?? 0,
      text_count: result.contents?.reduce((acc, cur) => acc + cur.content.length, 0),
      text: result.contents?.map(item => item.content).join('\n')
    }).then()
  } catch (e) {
    let errorStatus = ''

    if (e instanceof CreateTaskError) {
      const s = e.payload?.status
      if (s) {
        errorStatus = s.toString()
      }
    } else if (e instanceof QueryTaskError) {
      const s = e.payload?.state?.toString()
      if (s) {
        errorStatus = s.toString()
      }
    }
    multiInfo.isLoading = false

    TextToSpeechTrack.smartMatchingResult({
      result: 'fail',
      reason: TaskErrorMsg.safeToJSON(e),
      message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
      status: errorStatus,
    }).then()

    // 网络异常优先判断
    if (e instanceof TypeError) {
      const result = await showNetworkErrorDialog()
      if (result === 'ok') {
        //点击重试
        handleClickCreateSceneListTask().then()
      }
      return
    } else if (e instanceof Error) {
      //点数不足
      if (e.message === notEnoughPointCode.toString()) {
        showPointNotEnoughDialog('create-multi-scene-task')
        return
      } else if (e.message === '19916' || e.message === '19915') {
        //违禁词列表
        const wordList = Array.from(new Set(e.cause as string[]))
        const hasMatch = wordList.some(i => textInputInfo.text.includes(i))
        if (hasMatch) {
          //能匹配到, 用户点击确定的时候将违禁词替换成***
          showDialog(t('InvalidPrompts'), {
            subtitle: '敏感内容将用***代替',
            onClickOk(close) {
              const text = wordList.reduce((previousValue, currentValue) => {
                return previousValue.replaceAll(currentValue, '***')
              }, textInputInfo.text)

              textInputInfo.text = text
              tipsInfo.isShowReport = false
              invalidInfo.invalidStarCurrentIndex = 0
              invalidInfo.countInvalidStars(text)
              nextTick(() => {
                document.querySelector<HTMLButtonElement>('#next-btn-id')?.click()
              })
              close()
            },
          })
        } else {
          //无法匹配, 则在弹窗中提示违规内容
          showDialog(t('InvalidPrompts'), {
            subtitle: `敏感内容: ${wordList.join(', ')}`,
            subtitleClass: 'text-red',
            onClickOk(close) {
              invalidInfo.enableDisableMatchWords = true
              invalidInfo.disableDisableMatchWordsList = wordList
              close()
            }
          })
        }
      }
    } else {
      showTaskFailDialog({
        title: t('dialogResultTimeoutError'),
        okText: t('002428'),
        cancelText: t('speechAudioCancel'),
      }).then()
    }
  }

  async function _handleMultiTTSTask(text: string) {
    const taskId = await createMultiTTSSceneTask(text, getMultiDeviceId())
    const instance = new SingleTaskUtil({
      api() {
        return queryMultiTTSSceneTask(taskId)
      }
    })
    const result = await instance.startQuery({
      onProgress(progress: number) {
        const random = Math.floor(Math.random() * 10) + 1
        if (multiInfo.progress + random < 99) {
          //1~10的随机数
          multiInfo.progress = multiInfo.progress + random
        } else {
          multiInfo.progress = 99
        }
      }
    })
    return result
  }

}

function handleClickCreateTask() {
  TextToSpeechTrack.clickCreateRecord().then()
  previewStatusInfo.toMultiInput()
  textInputInfo.text = ''
}

const enableSearchRef = ref(true)

const doSearch = debounce((value) => {
  if (!enableSearchRef.value) return
  historyListInfo.searchWord = value
  historyListInfo.fetchList()
}, 300)

const checkIsMultiFreeLimitTextLength = computed(() => {
  return multiEquityInfo.existFreeCount
})
</script>

<template>
  <div class="w-full h-auto bg-[#FAFAFA] pt-header-offset relative text-dark">

    <div class="absolute right-0 top-0 z-0">
      <img :src="bgGreen" alt="bg icon" draggable="false">
    </div>
    <div class="absolute top-0 left-1/2 -translate-x-1/2 z-0">
      <img :src="bgLine" alt="bg icon" draggable="false">
    </div>
    <NoVipCacheClearNotice
      v-if="previewStatusInfo.isHistoryList"
      class="relative z-10"
    />
    <main
      class="px-[3.125vw] pt-[20px] lg:pt-[30px] flex flex-col 2xl:gap-[30px] gap-[20px] w-full z-10 relative"
      :class="[previewStatusInfo.isMultiInput?'':'']"
    >
      <div class="flex items-center justify-between">
        <BackBtn @click="handleClickBack">{{ t('001293') }}</BackBtn>

        <TitleTab
          v-show="previewStatusInfo.isSingleInput||previewStatusInfo.isMultiInput"
          class="absolute left-0 z-10 top-0 translate-y-1/2 -translate-x-1/2"
          :style="{left:`${screenWidthRef/2}px`}"
          :tab-list="tabInfo.list"
          :active-index="tabInfo.activeIndex"
          @clickItem="handleUpdateTab"
        >
          <template v-slot="{item}">
            <span>{{ item.title }}</span>
          </template>

        </TitleTab>

        <div class="ml-auto">
          <form
            v-show="previewStatusInfo.isHistoryList"
            @submit.prevent="historyListInfo.fetchList"
            class="w-[268px] h-[32px] mr-[15px] rounded-[100px] flex-center bg-white text-[14px] has-[:focus]:outline-primary-color outline outline-1 outline-transparent"
          >
            <input
              ref="searchInputElRef"
              type="text"
              class="w-full h-full bg-transparent pl-[20px] pr-[14px]"
              :placeholder="t('historyListSearchText')"
              :value="historyListInfo.searchWord"
              @input="(ev)=>doSearch((ev.target as HTMLInputElement).value)"
              @compositionstart="enableSearchRef = false"
              @compositionend="enableSearchRef = true"
            />
            <button class="pr-[4px]">
              <img class="size-[26px] block object-cover" :src="searchIcon" alt="">
            </button>
          </form>
        </div>
        <button
          v-show="!previewStatusInfo.isHistoryList"
          class="border border-deep-color/[0.5] p-[6px_20px] rounded-[32px] text-[14px] text-dark font-semibold transition hover:translate-y-[4px] duration-500"
          :class="[!previewStatusInfo.isSingleInput?'visible':'invisible']"
          @click="handleToHistoryList"
        >
          {{ t('speechConversionRecords') }}
        </button>

        <button
          v-show="previewStatusInfo.isHistoryList"
          class="p-[6px_30px] bg-theme rounded-[32px] hover:translate-y-[4px] transition text-white"
          @click="handleClickCreateTask"
        >
          {{ t('speechNewCreate') }}
        </button>
      </div>

      <div
        class="w-full flex gap-[2vw] relative"
        v-show="previewStatusInfo.isSingleInput||previewStatusInfo.isMultiInput"
      >
        <div :class="[previewStatusInfo.isSingleInput?'w-[62vw] min-h-full':'w-full !min-h-[80vh]']">
          <TextInput>
            <template #menu-item v-if="previewStatusInfo.isMultiInput">
              <div
                v-show="textInputInfo.text.length>0"
                class="ml-auto flex items-center text-[12px] text-deep-color/[0.5]"
                id="start-multi-scene-task-id"
              >

                <div class="flex items-center mr-[16px]" v-if="!checkIsMultiFreeLimitTextLength">
                  <span class="mr-2">{{ t('noFreeNotice').replace('#', String(multiEquityInfo.price)) }}</span>
                  <span class="flex items-center">
                    <img class="mr-[2px] w-[16px] h-[14px]" :src="vipIcon" alt="icon"/>1
                  </span>
                </div>

                <button
                  class="gradient-button h-[40px] min-w-[200px] relative px-[20px]"
                  :class="[textInputInfo.text.length>0?'':'gradient-button-disable pointer-events-none']"
                  @click="handleClickCreateSceneListTask"
                >
                  <span class="text-[16px] font-bold text-white">{{ t('smartMatchBtnTitle') }}</span>
                  <span
                    id="start-multi-scene-task-tag"
                    v-if="checkIsMultiFreeLimitTextLength"
                    class="absolute text-[12px] transition p-[4px_10px] -right-[26px] -top-[4px] bg-[#FE4336] -translate-y-1/2 rounded-tl-[12px] rounded-br-[12px]"
                  >
                    {{ t('mainStartForFree') }}
                  </span>
                </button>
              </div>
            </template>
          </TextInput>
        </div>
        <div
          v-show="previewStatusInfo.isSingleInput"
          class="flex-1 bg-white rounded-[20px] shadow-[0px_10px_20px_0px_rgba(30,61,36,0.08)] relative min-h-[80vh] transition-all duration-300"
        >
          <Settings/>
        </div>
        <div
          class="flex-center absolute top-0 left-0 w-full h-full z-[99] bg-white rounded-[20px]"
          v-show="multiInfo.isLoading"
        >
          <TheLoading :progress="multiInfo.progress">
            {{ t('smartMatchLoadingTitle') }}
          </TheLoading>
        </div>
      </div>

      <div v-if="previewStatusInfo.isMultiDetail">
        <MultiAudioDetail
          :task-id="previewStatusInfo.detailTaskId"
        />
      </div>
      <KeepAlive>
        <UseElementBounding
          v-if="previewStatusInfo.isHistoryList"
          class="relative"
          v-slot="{top}"
        >
          <!--固定定位解决左右不贴边的问题-->
          <HistoryList
            class="fixed w-full h-[calc(80vh-20px)] left-0 right-0"
            :style="{top:`${top}px`}"
          />

        </UseElementBounding>
      </KeepAlive>


    </main>
  </div>
</template>

<style scoped>

</style>

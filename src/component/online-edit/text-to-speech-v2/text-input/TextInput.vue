<script setup lang="ts">
import RandomIcon from '../icons/RandomIcon.vue';
import UploadIcon from '../icons/UploadIcon.vue';
import AiHelpMe from './ai-options/AiHelpMe.vue';
import {useI18n} from '@/locales';
import useTextToSpeech from '@/component/online-edit/text-to-speech-v2/useTextToSpeech';
import useTextInput from '@/component/online-edit/text-to-speech-v2/text-input/useTextInput';
import {isClient} from '@vueuse/core';
import {openFileDialog} from '@central/shared';
import TextTranslate from '@/component/online-edit/text-to-speech-v2/text-input/translate/TextTranslate.vue';
import {getLang} from '@central/i18n';
import showReportDialog from '@/render/showReportDialog';
import {ThemeMode} from '@/interface/Global';
import ModifySuccess from './ModifySuccess.vue';
import {computed, nextTick, reactive, watch} from 'vue';
import TextToSpeechTrack from '@/track/TextToSpeechTrack';
import InfoIcon from '@/component/online-edit/text-to-speech-v2/icons/InfoIcon.vue';
import TranslateSelectIcon from '~icons/speech-to-text/translate-select-icon.svg';
import ArrowIcon from '~icons/text-to-speech/arrow.svg'
import useSettings from '@/component/online-edit/text-to-speech-v2/settings/useSettings';
import useLoginService from '@/services/useLoginService';
import errorTipIcon from '@images/ai-translate/error-tip.svg'
import searchIcon from '@images/ai-subtitle/search-icon-v2.svg';
import {vOnClickOutside} from '@vueuse/components';

const {t} = useI18n()
const lang = getLang()

const {textInputInfo, invalidInfo, textInputElRef, previewStatusInfo, multiEquityInfo} = useTextToSpeech()
const {aiHelpMeInfo, handleRandomStory, readFile, translateInfo, tipsInfo} = useTextInput()
const {singleEquityInfo} = useSettings()
const {isLoginRef} = useLoginService()

if (isClient) {
  translateInfo.fetchTranslateLangList().then()
}

const handleClickRandomStory = () => {
  TextToSpeechTrack.clickSample()
  const enable = aiHelpMeInfo.checkEnableInputPrompt() && translateInfo.checkEnableInputPrompt()
  if (enable === false) {
    return
  }
  tipsInfo.isShowReport = false
  handleRandomStory()
}

const handleClickSelectFile = async () => {
  TextToSpeechTrack.clickUploadTextFile().then()
  const enable = aiHelpMeInfo.checkEnableInputPrompt() && translateInfo.checkEnableInputPrompt()
  if (enable === false) {
    return
  }
  const files = await openFileDialog('.txt')
  if (files.length > 0) {
    const file = files[0]
    if (file.name.toString().endsWith('.txt') === false) {
      alert(t('notSupportFormat'))
      return
    }
    textInputInfo.text = ''
    await readFile(file, maxInputLengthRef.value)
  }
  tipsInfo.isShowReport = false
}

function handleClickReport() {
  return showReportDialog({
    theme: ThemeMode.light,
    onClickSubmit() {
      tipsInfo.isSuccessReport = true
    },
    onClickClose() {

    },
  })
}

// 定位到*号的位置
function handleChangeToInvalidIndex() {
  const start = invalidInfo.invalidStarIndexArray[invalidInfo.invalidStarCurrentIndex]
  const el = textInputElRef.value!

  //选中滚动到视口中
  const fullText = el.value
  el.value = fullText.substring(0, start + 3)
  el.scrollTop = el.scrollHeight
  el.value = fullText

  el.setSelectionRange(start, start + 3)
  el.focus()
  if (invalidInfo.invalidStarCurrentIndex >= invalidInfo.invalidStarIndexArray.length - 1) {
    invalidInfo.invalidStarCurrentIndex = 0
  } else {
    invalidInfo.invalidStarCurrentIndex++
  }
}

const maxInputLengthRef = computed(() => {
  if (previewStatusInfo.isSingleInput) {
    return 30000
  } else if (previewStatusInfo.isMultiInput) {
    return 10000
  }
  return 30000
})


watch(() => previewStatusInfo.status, () => {
  if (previewStatusInfo.isMultiInput) {
    //切换到多人配音输入框 需要截取字数最大到上限
    textInputInfo.text = textInputInfo.text.substring(0, maxInputLengthRef.value)
  }
})

// 不同配音下的限制免费字数
const maxFreeLengthRef = computed(() => {
  return previewStatusInfo.isSingleInput ? singleEquityInfo.limitWordCount : multiEquityInfo.limitWordCount
})

const isFreeSingleEquityLimitLength = computed(() => {
  return isLoginRef && singleEquityInfo.existFreeCount && textInputInfo.textLength <= maxFreeLengthRef.value

})

const isFreeMultiEquityLimitLength = computed(() => {
  return isLoginRef && multiEquityInfo.existFreeCount && textInputInfo.textLength <= maxFreeLengthRef.value

})

type DetectListType = typeof translateInfo.translateList

const detectingInfo = reactive({
  enableDialog: false,
  searchWord: '',
  activeLang: 'auto',
  get list(): DetectListType {
    return [
      {lang: 'auto', title: t('textToSpeechDetectingTitleText')},
      ...translateInfo.translateList
    ]
  },
  get uiList(): DetectListType {
    return this.list.filter(i => i.title.includes(this.searchWord))
  },
  get activeTitle(): string {
    const item = this.list.find(i => i.lang === this.activeLang)!
    if (this.activeLang === 'auto') {
      const l = translateInfo.translateList.find(i => i.lang === textInputInfo.textLang)?.title
      return l ? `${item.title}(${l})` : item.title
    }
    return item.title
  },
  handleClickItem(item: DetectListType[number], index: number) {
    this.activeLang = item.lang
    if (item.lang === 'auto') {
      textInputInfo.autoSetTextLang()
      textInputInfo.updateTextLangOnInput()
    } else {
      textInputInfo.manualSetTextLang(item.lang)
    }
  },
  handleSetAutoActiveLang() {
    detectingInfo.activeLang = 'auto'
    textInputInfo.autoSetTextLang()
    textInputInfo.updateTextLangOnInput()
  },

  openDialog() {
    this.enableDialog = true
    nextTick(() => {
      document.querySelector('li.detecting-active')?.scrollIntoView({block: 'center'})
    })
  },
  closeDialog() {
    this.enableDialog = false
    this.searchWord = ''
  },
})
</script>

<template>
  <div
    class="w-full h-full flex justify-center items-center bg-white rounded-[20px] shadow-[0px_10px_20px_0px_rgba(30,61,36,0.08)]"
  >
    <div class="w-full h-full p-[30px] relative flex flex-col">
      <ul class="flex justify-start items-center text-[14px] gap-[16px]">

        <AiHelpMe
          class="flex group items-center relative cursor-pointer px-[18px] py-[12px] bg-[#EEEEF0] rounded-[40px] hover:bg-gradient-light-primary text-primary-color hover:text-white z-[99]"
        />

        <li
          class="flex group items-center relative cursor-pointer px-[18px] py-[12px] bg-[#EEEEF0] rounded-[40px] hover:bg-gradient-light-primary"
          @click="handleClickRandomStory"
        >
          <RandomIcon class="text-primary-color group-hover:text-white"/>
          <span class="font-semibold ml-[6px] text-deep-color group-hover:text-white">{{ t('randomStory') }}</span>
        </li>

        <li
          class="flex group items-center relative cursor-pointer px-[18px] py-[12px] bg-[#EEEEF0] rounded-[40px] hover:bg-gradient-light-primary"
          @click="handleClickSelectFile"
        >
          <UploadIcon class="text-primary-color group-hover:text-white"/>
          <span class="font-semibold ml-[6px] text-deep-color group-hover:text-white">
            {{ t('uploadTxt') }}
          </span>
          <div
            class="text-[12px] absolute -bottom-[10px] z-10 translate-y-full left-[50%] text-[#999] bg-white p-[2px_14px] rounded-[20px] shadow-[0_10px_10px_rgba(30,61,36,.1)] -translate-x-1/2 whitespace-nowrap hidden group-hover:block"
          >
            {{ t('onlyTXT') }}
          </div>
        </li>
        <TextTranslate
          @setAutoActiveLang="detectingInfo.handleSetAutoActiveLang"
        />

        <slot name="menu-item"/>
      </ul>
      <div class="w-full relative mt-[24px] flex-1 flex flex-col bg-[#F5F5F5] rounded-[16px] px-[4px]">
        <div class="relative flex-1">
          <textarea
            ref="textInputElRef"
            class="size-full relative z-90 mt-[24px] resize-none scroll-bar overflow-y-auto bg-transparent px-[20px] pb-[24px] text-[14px] placeholder:text-[#2D2D33]/[0.4]"
            :placeholder="t('aiTextToSpeechPlaceholder')"
            v-model.trim="textInputInfo.text"
            :maxlength="maxInputLengthRef"
          ></textarea>
        </div>
        <div class="relative mt-[28px] text-[14px] py-[24px] px-[20px] flex gap-[28px] z-[91]">
          <div>
            <span class="text-[#2D332E] font-medium">{{ textInputInfo.textLength }}</span>
            <span class="text-[#9E9D9D]" v-if="previewStatusInfo.isSingleInput">
              /{{ maxInputLengthRef }}
            </span>
            <span class="text-[#9E9D9D]" v-else>
              /{{ maxInputLengthRef }}
            </span>
          </div>
          <p v-if="tipsInfo.isShowReport&&lang==='zh'"
             class="flex items-center text-[#9E9D9D] text-[12px] gap-[8px] hover:text-primary-color cursor-pointer w-fit"
             :class="[tipsInfo.isSuccessReport?'pointer-events-none':'']"
             @click="handleClickReport"
          >
            <span v-show="tipsInfo.isSuccessReport===false">该内容由AI生成，仅供参考</span>
            <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g>
                <path
                  d="M0.799654 10.5237L5.653 1.5637C6.02446 0.877921 7.00308 0.862431 7.39607 1.53611L12.6227 10.4961C13.0116 11.1628 12.5308 12 11.759 12H1.67895C0.921176 12 0.438741 11.19 0.799654 10.5237Z"
                  stroke="currentColor"/>
                <path d="M6.5 5V8.5" stroke="currentColor" stroke-linecap="round"/>
                <circle cx="6.5498" cy="10.25" r="0.75" fill="currentColor"/>
              </g>
            </svg>
            <span v-show="tipsInfo.isSuccessReport===true">内容已被举报，请重新生成</span>
          </p>
          <div v-if="invalidInfo.isShowInvalidStar&&lang==='zh'&&tipsInfo.isShowReport===false">
            <div v-if="invalidInfo.invalidStarIndexArray.length" class="flex-center gap-2 text-[#F06464]">
              <img :src="errorTipIcon" alt="">
              共有 {{ invalidInfo.invalidStarIndexArray.length }} 处敏感内容
              <button class="underline" id="next-btn-id" @click.stop="handleChangeToInvalidIndex">
                下一个
              </button>
            </div>
            <ModifySuccess v-else/>
          </div>
          <div
            v-if="invalidInfo.enableDisableMatchWords&&tipsInfo.isShowReport===false"
            class="flex-center gap-2 text-[#F06464]"
          >
            <img :src="errorTipIcon" alt="">
            <span
              class="block"
              :class="[previewStatusInfo.isMultiInput?'max-w-[60em]':'max-w-[30em]']"
            >
              敏感内容:{{ invalidInfo.disableDisableMatchWordsList.join(', ') }}
            </span>

          </div>


          <div class="ml-auto">
            <div
              v-show="textInputInfo.text.length>0"
              class="flex-center gap-1 cursor-pointer text-dark hover:*:text-theme transition"
              @click="detectingInfo.openDialog"
              v-on-click-outside="()=>detectingInfo.closeDialog()"
            >
              <div class="relative group/info z-30">
                <InfoIcon/>
                <div
                  class="absolute bottom-[calc(100%+10px)] left-1/2 w-[230px] text-[12px] transition scale-0 group-hover/info:scale-100 origin-bottom -translate-x-1/2 bg-white shadow-lg p-[8px_16px] rounded-[10px] text-dark"
                >
                  {{ t('textToSpeechDetectingTipTitle') }}
                </div>
              </div>
              <div
                class="relative z-10 group/dialog flex-center gap-1"
                :class="[detectingInfo.enableDialog?'active':'']"
              >
                <span>
                  {{ t('textToSpeechDetectingTitle').replace('#', detectingInfo.activeTitle) }}
                </span>

                <div class="group-[.active]/dialog:rotate-180 flex-center transition">
                  <ArrowIcon/>
                </div>
                <div
                  class="hidden group-[.active]/dialog:block absolute bottom-full left-1/2 -translate-x-1/2 bg-white shadow-[0px_6px_12px_0px_rgba(17,88,23,0.1)] rounded-3 overflow-hidden"
                >
                  <!--搜索框-->
                  <div class="w-full flex-center px-[10px] py-[10px]">
                    <div
                      class="w-full h-[32px] flex items-center bg-[#F5F5F5] rounded-[100px] text-dark"
                    >
                      <input
                        type="text"
                        class="w-full px-[14px] text-[14px] placeholder:text-[#ccc] bg-transparent"
                        :placeholder="t('003105')"
                        v-model="detectingInfo.searchWord"
                      />
                      <img class="mr-[12px]" :src="searchIcon" alt="">
                    </div>
                  </div>

                  <ul
                    class="min-w-[200px] max-h-[300px] overflow-y-auto scroll-bar text-dark"
                    @click.stop
                  >
                    <li
                      class="h-[36px] text-[16px] flex items-center hover:bg-primary-color/[0.1] px-5 group"
                      :class="[detectingInfo.activeLang===item.lang?'detecting-active':'']"
                      v-for="(item,index) in detectingInfo.uiList"
                      @click="detectingInfo.handleClickItem(item,index)"
                    >
                      <span class="text-[14px]">{{ item.title }}</span>
                      <TranslateSelectIcon
                        v-show="detectingInfo.activeLang===item.lang"
                        class="ml-auto"
                      />
                    </li>
                  </ul>
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>

    </div>
  </div>

</template>

<style>

</style>

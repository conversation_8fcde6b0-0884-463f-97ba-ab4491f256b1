<script setup lang="ts">

import AiHelpMeIcon from '../../icons/AiHelpMeIcon.vue'
import {useI18n} from '@/locales'
import RandomIcon from '../../icons/RandomIcon.vue'
import GradientButton from '@/component/base/button/GradientButton.vue'
import {vOnClickOutside} from '@vueuse/components'
import VipIcon from '../../icons/VipIcon.vue'
import useTextInput from '../useTextInput'
import useLoginService from '@/services/useLoginService';
import TextToSpeechTrack from '@/track/TextToSpeechTrack';

const {t} = useI18n()
const {aiHelpMeInfo} = useTextInput()
const {isVipRef} = useLoginService()

const handleClick = () => {
  TextToSpeechTrack.clickAIWrite()
  const enable = aiHelpMeInfo.checkEnableInputPrompt()
  if (enable === false) {
    return
  }
  aiHelpMeInfo.openDialog()
}
</script>

<template>
  <li class="relative" @click="handleClick" v-on-click-outside="aiHelpMeInfo.closeDialog">
    <AiHelpMeIcon/>
    <span class="font-semibold ml-[6px] text-deep-color group-hover:text-white">{{ t('AIHelpMeWrite') }}</span>
    <div
      v-if="aiHelpMeInfo.enableDialog"
      class="absolute z-99 top-full translate-y-[10px] p-[1.5px] color-bg flex flex-col cursor-auto w-[520px] h-[132px] bg-white left-0 z-10 drop-shadow-[0_3px_30px_rgba(0,0,0,0.16)] rounded-[10px]"
      @click.stop
    >
      <textarea
        class="w-full bg-white flex-1 resize-none p-[16px] rounded-t-[10px] text-deep-color"
        :placeholder="t('aiTextPlaceholder')"
        v-model.trim="aiHelpMeInfo.inputValue"
      >
      </textarea>
      <div class="flex items-center justify-between px-[16px] py-[12px] bg-white rounded-b-[10px]">
        <div
          class="flex items-center group/btn bg-[#8B9990]/[0.1] h-[33px] w-fit px-[16px] rounded-[20px] cursor-pointer hover:bg-gradient-light-primary"
          @click="aiHelpMeInfo.handleRandom"
        >
          <RandomIcon class="group-hover/btn:text-white text-primary-color"/>
          <span class="font-semibold ml-[6px] group-hover/btn:text-white text-deep-color">{{ t('002228') }}</span>
        </div>
        <GradientButton
          class="min-w-[118px] h-[40px]"
          :disable="!Boolean(aiHelpMeInfo.inputValue)"
          @click.stop="aiHelpMeInfo.handleGeneration"
        >
          <div class="flex justify-center items-center gap-[8px] text-white">
            <span class="flex items-center justify-center gap-0.5">
              <AiHelpMeIcon/>{{ t('testToVideoGenerate') }}
            </span>

            <span class="flex items-center justify-center gap-0.5" v-if="!isVipRef">
              <VipIcon class="w-[16px] h-[14px]"/>1
            </span>

          </div>
        </GradientButton>
      </div>
    </div>
  </li>
</template>

<style scoped>
textarea {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.color-bg {
  background: linear-gradient(90deg, #41D74F 0%, #82ACFF 43.5%, #E57EFF 100%);
}
</style>

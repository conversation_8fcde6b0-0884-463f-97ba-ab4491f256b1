<script setup lang="ts">
import {ref, onMounted} from 'vue';

const isVisible = ref(true);

onMounted(() => {
  setTimeout(() => {
    isVisible.value = false;
  }, 2000);
});
</script>

<template>
  <div v-if="isVisible" class="flex-center gap-2 text-theme">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="8" cy="8" r="8" fill="#18AD25"/>
      <path d="M5 8.12174L7.50526 10.6672L11.3333 5.58386" stroke="white" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    修改完成，开始转换吧~
  </div>
</template>

<style>

</style>

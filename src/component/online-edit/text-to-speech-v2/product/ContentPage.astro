---
import ItemAdvantage from "./ItemAdvantage.astro";
import TextOpacity from './TextOpacity.vue';
import TextOperate from './TextOperate.vue';
import ShowComments from '@/component/home/<USER>';
import MobileDownloadAppBtn from '@/component/online-edit/MobileDownloadAppBtn.vue';
import {useI18n} from '@/locales';
import logoIcon from '@images/favicon.png';
import textEfficiency from '@images/text-to-speech/text-efficiency.svg';
import textConsistency from '@images/text-to-speech/text-consistency.svg';
import texInternational from '@images/text-to-speech/text-international.svg';
import textPrice from '@images/text-to-speech/text-price.svg';
import textOneStep from '@images/text-to-speech/text-one-step.svg';
import textOneStepHover from '@images/text-to-speech/text-one-step-hover.svg';
import ttsRight from '@images/text-to-speech/tts-right.png'
import quickListen from '@images/text-to-speech/quick-listen.svg'
import quickListenHover from '@images/text-to-speech/quick-listen-hover.svg';
import textTranslate from '@images/text-to-speech/text-translate.svg'
import textTranslateHover from '@images/text-to-speech/text-translate-hover.svg';
import useSceneFirst from '@images/text-to-speech/use-scene-first.png';
import useSceneSecond from '@images/text-to-speech/use-scene-second.png';
import useSceneThird from '@images/text-to-speech/use-scene-third.png';
import useSceneFour from '@images/text-to-speech/use-scene-four.png';
import user1Icon from '@images/home/<USER>/img1.png';
import user2Icon from '@images/home/<USER>/img2.png';
import user3Icon from '@images/home/<USER>/img3.png';
import user4Icon from '@images/home/<USER>/img4.png';
import StartLogo from '~icons/speech-to-text/star.svg'
import {getLang} from '@central/i18n';

const {t} = useI18n()

const lang = getLang()
const isZh = lang === 'zh'
const isTw = lang === 'tw'
const isZhTw = isZh || isTw

const advantageList = [
  {
    icon: textEfficiency,
    title: t('textBoostTitle'),
    desc: t('textBoostDesc'),
  },
  {
    icon: textConsistency,
    title: t('textConsistencyTitle'),
    desc: t('textConsistencyDesc')
  },
  {
    icon: texInternational,
    title: t('textServiceTitle'),
    desc: t('textServiceDesc'),
  },
  {
    icon: textPrice,
    title: t('textCostTitle'),
    desc: t('textCostDesc'),
  }
]
const easyList = [
  {
    img: textOneStep,
    imgHover: textOneStepHover,
    width: "38px",
    height: "48px",
    mt: "-10px",
    title: t('textOneClickTitle'),
    desc: t('textOneClickDesc')
  },
  {
    img: quickListen,
    imgHover: quickListenHover,
    width: "50px",
    height: "62px",
    mt: "-14px",
    title: t('textFastTitle'),
    desc: t('textFastDesc')
  },
  {
    img: textTranslate,
    imgHover: textTranslateHover,
    width: "42px",
    height: "50px",
    mt: "-10px",
    title: t('textAccurateTransTitle'),
    desc: t('textAccurateTransDesc')
  }
]
const imgUseScene = [useSceneFirst, useSceneSecond, useSceneThird, useSceneFour]
const imgUseSceneDesc = [t('textAudioBookTitle'), t('textLangLearnTitle'), t('textVideoDubTitle'), t('textGlobalExpanTitle')]
const useSceneTextList = [
  t('textAudioBookDesc'),
  t('textLangLearnDesc'),
  t('textVideoDubDesc'),
  t('textGlobalExpanDesc'),
]
const commentsList = [
  {
    title: t('userComment13'),
    name: t('userComment13Name'),
    img: user1Icon
  },
  {
    title: t('userComment14'),
    name: t('userComment14Name'),
    img: user2Icon
  },
  {
    title: t('userComment15'),
    name: t('userComment15Name'),
    img: user3Icon
  },
  {
    title: t('userComment16'),
    name: t('userComment16Name'),
    img: user4Icon
  },
]
interface Props {
  class?: string,
}

const {class: className} = Astro.props
---
<!-- 便捷&高效服务-->
<section class=`relative bg-[url(@/assets/images/text-to-speech/bg-second-text.png)] bg-cover ${className}`>
  <div class="pt-[100px] lg:pt-160px pb-[60px] lg:pb-100px flex flex-col items-center">
    <h2 class="text-dark text-[length:var(--title-size)] font-bold text-center pb-[10px]">
      {t('textQuickEfficient')}
    </h2>
    <div class="relative pt-[24px] lg:pt-[80px]">
      <ul
        class="w-[80%] mx-auto xl:w-[1200px] gap-[20px] lg:gap-[60px] h-auto pb-[80px] lg:pb-160px relative grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 "
      >
        {
          easyList.map((item, index) => {
            return (
              <li
                class="w-full pointer-events-none lg:pointer-events-auto group cursor-pointer bg-[rgba(255,255,255,0.60)] sm:scale-80 lg:scale-100 origin-top shadow-[0px_20px_40px_0px_rgba(30,61,36,0.05)] border-transparent rounded-[20px] overflow-hidden hover:bg-gradient-to-r from-[#56E8B3] to-[#18AD25] hover:shadow-[0px_30px_30px_rgba(24,173,37,0.20)]"
              >
                <div class="relative pt-24px pl-28px pr-24px pb-[24px] flex">
                  <img
                    src={item.img}
                    style={{width: item.width, height: item.height, marginTop: item.mt}}
                    class="object-cover block group-hover:hidden" alt=""
                  />
                  <img
                    src={item.imgHover}
                    style={{width: item.width, height: item.height, marginTop: item.mt}}
                    class="object-cover hidden group-hover:block" alt=""
                  />
                  <div class="flex gap-12px flex-col pt-width">
                    <div class="text-20px font-bold text-dark group-hover:color-#fff whitespace-nowrap">
                      {item.title}
                    </div>
                    <div class="text-16px color-#8C8B99 group-hover:color-#fff">{item.desc}</div>
                  </div>
                </div>
              </li>
            )
          })
        }
      </ul>
    </div>
    <div class="w-full">
      <ul>
        <li class="flex lg:mb-160px m-auto gap-160px justify-center">
          <div class="w-[80%] lg:w-420px flex justify-center flex-col pb-70px">
            <div class="text-dark text-28px font-bold lg:mt-60px lg:mb-20px">{t('TextIntroductionTitle')}</div>
            <div class="text-[rgba(45,45,51,0.8)] text-[16px] mt-2">
              {t('TextIntroductionDesc')}
            </div>
          </div>
          <div class="hidden lg:block">
            <img src={ttsRight} alt="" class="w-478px h-424px">
          </div>
        </li>
        <TextOperate client:media="(min-width: 1024px)"/>
        <li class="hidden lg:flex mb-100px m-auto justify-center gap-[0px] 2xl:gap-[60px]">
          <div class="flex justify-center flex-col pt-[100px] pb-[100px] pl-[24px] 2xl:pl-[120px]">
            <div class="text-dark text-28px font-bold mt-10px mb-20px">{t('textApplicationScene')}</div>
            {
              useSceneTextList.map((item, index) => {
                return (
                  <div class="text-[16px] text-[rgba(45,45,51,0.8)] leading-[40px]">
                    <div set:html={item}></div>
                  </div>
                )
              })
            }
          </div>
          <TextOpacity
            pagination={true}
            imgList={imgUseScene}
            imgDesc={imgUseSceneDesc}
            imgShadow={true}
            client:idle
          />
        </li>
      </ul>
    </div>

    <div class="hidden lg:flex justify-center items-center">
      <button
        class="try-now-btn min-w-[254px] h-[70px] gradient-button text-[28px] font-bold flex-center px-[20px]"
      >
        <span class="flex whitespace-nowrap">
          {t("mainStartForFree")}
          <i class="self-start">
            <StartLogo class="!block"/>
          </i>
        </span>
      </button>
    </div>
    <div class="block lg:hidden px-[3.125vw] w-full md:w-auto">
      <MobileDownloadAppBtn class="mx-auto w-fit" client:only="vue"/>
    </div>
  </div>
</section>
<!-- 文本转语音的优势-->
<section
  class=`relative pb-[80px] lg:py-[160px] bg-[url(@/assets/images/text-to-speech/bg-advantage.png)] bg-cover ${className}`
>
  <div class="relative">
    <h2 class="text-dark text-[length:var(--title-size)] font-bold text-center pb-[80px]">
      {t('textOutstandingAds')}
    </h2>
    <div class="2xl:w-[78%] w-[88%] mx-auto">
      <ItemAdvantage list={advantageList}/>
    </div>
  </div>
</section
>
<!-- 百万用户 -->
<section
  class=`relative py-70px lg:py-[160px] bg-[url(@/assets/images/home/<USER>/bg-big.webp)] bg-cover ${className}`
>
  <div class="relative z-10">
    <h2
      class="font-bold text-[length:var(--title-size)] text-center px-[24px] user-reccloud pb-30px lg:pb-[64px] flex-center flex-col lg:flex-row"
    >
      <span class="[&:lang(en)]:order-1">{t('speechReccloudMillons')}</span>
      <span class="section-title-light text-theme mx-[4px] relative" set:html={t('speechReccloudLove')}></span>
    </h2>
    <ShowComments list={commentsList}/>
  </div>
</section>
<!-- 下载录咖 -->
<section class=`relative py-70px lg:py-[160px] bg-[url(@/assets/images/home/<USER>
  <div class="relative z-10 flex flex-col justify-center items-center">
    <div class="flex justify-center items-center gap-[16px] flex-col lg:flex-row">
      <img class="size-[46px]" src={logoIcon} alt=""/>
      <span class="text-[length:var(--title-size)] font-bold text-center">
        {t('textLastTitle')}
      </span>
    </div>
    <p class="lg:mt-[40px] mt-[20px] mb-30px lg:mb-[64px] lg:text-[20px] text-16px px-[24px] text-center">
      {t('textLastDesc')}
    </p>
    <div class="hidden lg:flex justify-center items-center">
      <button
        class="try-now-btn min-w-[254px] h-[70px] gradient-button text-[28px] font-bold flex-center px-[20px] float-move"
      >
        <span class="flex whitespace-nowrap">
          {t("mainStartForFree")}
          <i class="self-start">
            <StartLogo class="!block"/>
          </i>
        </span>
      </button>
    </div>
    <div class="block lg:hidden px-[3.125vw] w-full md:w-auto">
      <MobileDownloadAppBtn class="mx-auto w-fit" client:only="vue"/>
    </div>
  </div>
</section>
<style>
  @keyframes like {
    0% {
      transform: translate(-100%, -100%) rotate(25deg);
    }

    100% {
      transform: translate(-100%, -100%) rotate(0deg);
    }
  }

  @media screen and (max-width: 1024px) {
    @keyframes like {
      0% {
        transform: translate(-100%, -20%) rotate(25deg);
      }

      100% {
        transform: translate(-100%, -20%) rotate(0deg);
      }
    }
  }

  @keyframes userBreath {
    0% {
      transform: translate(100%, -100%) scale(1);
    }

    100% {
      transform: translate(100%, -100%) scale(0.2);
    }
  }

  @media screen and (max-width: 1024px) {
    @keyframes userBreath {
      0% {
        transform: translate(100%, -20%) scale(1);
      }

      100% {
        transform: translate(100%, -20%) scale(0.2);
      }
    }
  }

  .section-title-light::before {
    content: '';
    position: absolute;
    left: unset;
    right: 0;
    top: 0;
    width: 14px;
    height: 14px;
    background-color: #18ad25;
    animation: userBreath 2s infinite alternate;
    border-radius: 14px 14px 14px 0;
    transform-origin: left bottom;
  }

  .section-title-light::after {
    content: '';
    width: 28px;
    height: 40px;
    position: absolute;
    left: 0;
    top: 0;
    background: url("@images/home/<USER>/user-good.svg") no-repeat center;
    animation: like 1s infinite alternate;
    transform-origin: left bottom;
  }

  @media screen and (max-width: 1024px) {
    .section-title-light::before {
      right: -10px;
    }

    .section-title-light::after {
      left: -10px;
    }
  }

  @keyframes floatMove {
    0% {
      transform: translate(0, 10px);
    }

    100% {
      transform: translate(0, -10px);
    }
  }

  .float-move {
    animation: floatMove 2s infinite alternate ease-in-out;
    animation-play-state: running;
  }

  .float-move:hover {
    animation-play-state: paused;
  }
</style>

<script setup lang="ts">

import {nextTick, reactive, ref, watch} from 'vue';
import {isClient, useEventListener} from '@vueuse/core';
import {fetchMultiAllWithNoToken} from '@/api/textToSpeechApi';
import {GenderItemType} from '@/interface/TextToSpeech';
import VoiceListItem from '@/component/online-edit/text-to-speech-v2/settings/voice/VoiceListItem.vue';
import videoPauseIcon from '@images/text-to-speech/video-paused.svg';
import videoPlayIcon from '@images/text-to-speech/video-play.svg';

const props = defineProps<{
  activeLang: string,
}>()

const forceUpdateKey = ref(0)

function forceUpdate() {
  nextTick(() => {
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        forceUpdateKey.value++
      })
    })
  })
}

const voiceInfo = reactive({
  activeIndex: -1,
  list: [] as GenderItemType[],
  async fetchVoiceList(lang: string) {
    const list = await fetchMultiAllWithNoToken(lang)
    this.list = list
    if (isClient) {
      forceUpdate()
    }
  },
  setActiveIndex(index: number) {
    this.activeIndex = index
  },
  resetActiveIndex() {
    this.setActiveIndex(-1)
  },
})
watch(() => props.activeLang, () => {
  const l = props.activeLang
  voiceInfo.fetchVoiceList(l)
  voiceInfo.resetActiveIndex()
  listenInfo.resetAudioEl()
})

await voiceInfo.fetchVoiceList(props.activeLang)


if (isClient) {
  useEventListener(window, 'text-to-speech:toFunc', () => {
    listenInfo.resetAudioEl()
  })
}

//控制音色试听列表
const listenInfo = reactive({
  playingId: '' as string,
  audioEl: null as HTMLAudioElement | null,

  resetAudioEl() {
    this.audioEl?.remove()
    this.audioEl = null
    this.playingId = ''
  },
  handleClickTogglePlay(uuid: string, url: string) {
    if (this.playingId === uuid) {
      this.resetAudioEl()
    } else {
      this.resetAudioEl()
      this.audioEl = new Audio(url)
      this.audioEl.play()

      this.audioEl.onplay = () => {
        this.playingId = uuid
      }
      this.audioEl.onended = () => {
        this.resetAudioEl()
      }
      document.body.appendChild(this.audioEl)
    }
  }
})

const handleClickTogglePlay = (index: number) => {
  voiceInfo.setActiveIndex(index)
  const item = voiceInfo.list[index]
  listenInfo.handleClickTogglePlay(item.voice, item.url)
}

</script>

<template>
  <div
    :key="forceUpdateKey"
    class="overflow-y-auto overflow-x-hidden max-h-[380px] scroll-bar !scroll-dark-gradient pl-[30px] 2xl:pl-[40px] mr-[4px] pr-[24px] 2xl:pr-[34px]"
  >
    <ul
      id="voice-list-product-id"
      class="[--i:104px] w-full text-white grid grid-cols-3 xl:grid-cols-4 grid-rows-3 gap-[20px] 2xl:gap-[30px] *:cursor-pointer *:py-[12px] p-[2px]"
    >
      <div
        class="relative h-[var(--i)] group"
        v-for="(item,index) in voiceInfo.list"
      >
        <span
          class="absolute -left-[1px] -top-[1px] size-[calc(100%+2px)] rounded-[8px] overflow-hidden bg-white/[0.3]"
        >
          <span
            class="block size-[200px] bg-gradient-to-r from-[#D6FB72] to-[#76F9B1] rounded-inherit absolute left-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 transition-transform -rotate-[180deg] origin-bottom group-hover:rotate-0 duration-[1500ms]"
            :class="[listenInfo.playingId===item.voice?'!rotate-0':'']"
          ></span>
        </span>

        <VoiceListItem
          class="group/setting size-full flex-col flex-center bg-[#2D322D] z-[1] absolute top-0 left-0 rounded-[8px]"
          :class="[voiceInfo.activeIndex===index?'bg-[#272E1F]':'']"
          :title="item.voice_tag"
          title-class="text-[15px] 2xl:text-[18px] mt-[8px]"
          :data-voice="item.voice"
          :data-title="item.voice_tag"
          :enableVipIcon="false"
          voiceType="normal"
          @click="handleClickTogglePlay(index)"
        >
          <template #avatar>
            <div class="size-[30px] 2xl:size-[40px] *:rounded-full relative">
              <img data-allow-mismatch="attribute" class="block" :src="item.icon" draggable="false" alt="user avatar">

              <div
                class="absolute inset-0 w-full h-full bg-black/[0.3] hidden group-hover/setting:flex-center"
                :class="[listenInfo.playingId===item.voice?'!flex-center':'']"
              >
                <img
                  class="size-[18px]"
                  :src="listenInfo.playingId===item.voice?videoPauseIcon:videoPlayIcon"
                  draggable="false"
                  alt="play status icon"
                >
              </div>

            </div>
          </template>

        </VoiceListItem>
      </div>
    </ul>
  </div>
</template>

<style scoped>

</style>

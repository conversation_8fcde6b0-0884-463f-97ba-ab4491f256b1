<script setup lang="ts">

import ExtendSlider from './ExtendSlider.vue';
import useSettings from '@/component/online-edit/text-to-speech-v2/settings/useSettings';
import {useI18n} from '@/locales';

const {t} = useI18n()

const {settingInfo} = useSettings()

const getList = ({min = 0.5, max = 20, step = 0.1}) => {
  const _min = min * 100
  const _max = max * 100
  const _step = step * 100
  const list = []
  let temp = _min
  while (temp <= _max) {
    list.push(temp)
    temp = temp + _step
  }
  return list.map(item => item / 100)
}

const speedList = getList({min: 0.5, max: 2, step: 0.1}).map(item => item.toFixed(1) + 'x')
const handleUpdateExtendIndex = (index: number) => {
  settingInfo.speed = parseFloat(speedList[index])
}
</script>

<template>
  <div class="h-[20px] flex items-center gap-[16px]">
    <span class="w-max">{{ t('textSpeed') }}</span>
    <ExtendSlider
      class="flex-1 size-full"
      :active-index="speedList.findIndex(i=>parseInt(i)===1)"
      :text-list="speedList"
      @updateIndex="handleUpdateExtendIndex"
    />
  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import {useI18n} from '@/locales'
import useSettings from '@/component/online-edit/text-to-speech-v2/settings/useSettings';
import {isClient} from '@vueuse/core';
import useTextToSpeech from '@/component/online-edit/text-to-speech-v2/useTextToSpeech';
import {h, nextTick, onMounted, reactive, ref, render, watch} from 'vue';
import {GenderItemType, IsFavoriteType, IsFreeType} from '@/interface/TextToSpeech';
import VoiceListItem from '@/component/online-edit/text-to-speech-v2/settings/voice/VoiceListItem.vue';
import showDialog, {showAsyncDialog} from '@/render/showDialog';
import {showDangerToast, showToast} from '@/render/showToast';
import {deleteCloneVoiceApi} from '@/api/textToSpeechApi';
import AudioCloneDialog from '@/component/online-edit/text-to-speech-v2/settings/clone/AudioCloneDialog.vue';
import useLoginService from '@/services/useLoginService';
import {
  showAgainTextToSpeechDialog,
  showTextToSpeechCloneVoiceDialog,
} from '@/render/showPointDialog';
import SpeedSetting from '@/component/online-edit/text-to-speech-v2/settings/voice/SpeedSetting.vue';
import SoundSetting from '@/component/online-edit/text-to-speech-v2/settings/voice/VolumeSetting.vue';
import BgmSetting from '@/component/online-edit/text-to-speech-v2/settings/voice/BgmSetting.vue';
import TheLoading from '@/component/online-edit/TheLoading.vue';
import {notEnoughPointCode} from '@/services/constants';
import TheAudio from '@/component/base/media/TheAudio.vue';
import DownloadIcon from '@/component/online-edit/text-to-speech-v2/icons/DownloadIcon.vue';
import {getLang} from '@central/i18n';
import TryListenButton from '@/component/online-edit/text-to-speech-v2/settings/TryListenButton.vue';
import VipIcon from '@/component/online-edit/text-to-speech-v2/icons/VipIcon.vue';
import useTextInput from '@/component/online-edit/text-to-speech-v2/text-input/useTextInput';
import TextToSpeechTrack from '@/track/TextToSpeechTrack';
import videoPauseIcon from '@images/text-to-speech/video-paused.svg';
import videoPlayIcon from '@images/text-to-speech/video-play.svg';
import {showAiServiceDialog} from '@/render/showAiServiceDialog';
import TheHeartBox from '@/component/base/checkbox/TheHeartBox.vue';
import LimitDialog from '@/render/LimitDialog';
import PricingDialog from '@/render/PricingDialog';
import onlyVipIcon from '@images/text-to-speech/only-vip.svg'

const {t} = useI18n()
const lang = getLang()
const {
  voiceCategoryInfo,
  taskInfo,
  settingInfo,
  singleEquityInfo,
} = useSettings()

const audioRef = ref<InstanceType<typeof TheAudio>>()

const {isLoginRef, doLogin, pointCountRef, checkLimitHasAvailable, isVipRef} = useLoginService()

const {
  textInputInfo,
  invalidInfo,
  previewStatusInfo,
  showCountLimitDialogWithPricing,
  showStorageDialogWithPricing,
  getSingleDeviceId,
} = useTextToSpeech()
const {tipsInfo} = useTextInput()


const audioElId = '_try_listen_audio-id'
const _stopTryListen = () => {
  document.querySelector(`#${audioElId}`)?.remove()
}

if (isClient) {
  watch([() => textInputInfo.textLang, isLoginRef], async () => {
    const l = textInputInfo.textLang
    //源语言的时候需要重新获取
    if (l) {
      await voiceCategoryInfo.fetchCategoryTitleList(l)
      await voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text)
      voiceCategoryInfo.setActiveVoiceIndex(voiceCategoryInfo.getFirstNormalVoiceIndex())
    }
  }, {
    immediate: true
  })
}
onMounted(() => {
  singleEquityInfo.fetchEquityInfo()
})
watch(isLoginRef, () => {
  singleEquityInfo.fetchEquityInfo()

})
watch(() => previewStatusInfo.isSingleInput, (value, oldValue) => {
  if (value === false && oldValue === true) {
    _stopTryListen()
    audioRef.value?.pause()
    _voiceListTryListenInfo.resetAudioEl()
  }
})

//免费试用文本长度
const freeTextLength = lang === 'zh' ? 1000 : 3000

async function handleClickCategoryTitle(index: number, ev: Event) {
  //修改标题, 重新获取分类 切换item 自动滚动到中间
  voiceCategoryInfo.setActiveIndex(index)
  scrollToCenter()
  await voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text)
  voiceCategoryInfo.setActiveVoiceIndex(voiceCategoryInfo.getFirstNormalVoiceIndex())

  function scrollToCenter() {
    const el = ev.target as HTMLElement
    el?.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest',
    })
  }
}

//点击删除克隆
function handleClickDeleteClone(item: GenderItemType) {
  TextToSpeechTrack.clickDeleteVoice()
  showDialog(t('001614'), {
    async onClickOk(close) {
      const success = await deleteCloneVoiceApi(item.voice)
      if (success) {
        showToast(t('000974'))
        voiceCategoryInfo.removeVoiceItem(item)
      } else {
        showDangerToast(t('000975'))
      }
      close()
    }
  })
}

//点击克隆编辑
function handleClickCloneEdit(item: GenderItemType) {
  TextToSpeechTrack.clickEditVoice()
  const container = document.createElement('div')
  //点击了自定义克隆选项
  render(h(AudioCloneDialog, {
    uuid: item.voice,
    onClickClose() {
      container.remove()
      setTimeout(() => {
        voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text).then()
      }, 1000)
    }
  }), container)
  document.body.appendChild(container)
}

//点击音色
async function handleClickItem(item: GenderItemType, index: number) {
  if (item.type === 'clone-add') {
    TextToSpeechTrack.clickCloneVoice().then()
    //弹出添加克隆弹窗
    if (isLoginRef.value === false) {
      await doLogin()
      await voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text)
    }
    if (voiceCategoryInfo.cloneVoiceCount >= 3) {
      TextToSpeechTrack.freeCloneLimitDialog().then()
      const enable = await showTextToSpeechCloneVoiceDialog(t('audioCloneLimitDialogTitle').replace('#', '10'), 10)
      if (enable === false) {
        return
      }
    }
    const container = document.createElement('div')
    //点击了自定义克隆选项
    render(h(AudioCloneDialog, {
      onClickClose() {
        TextToSpeechTrack.clickSaveVoice()
        container.remove()
        setTimeout(() => {
          voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text).then()
        }, 1000)
      }
    }), container)
    document.body.appendChild(container)
  } else {
    voiceCategoryInfo.setActiveVoiceIndex(index)
  }
}

//开始转换
async function handleClickStart() {
  TextToSpeechTrack.clickStartConvert({
    trans_lang: textInputInfo.textLang,
    trans_voice: voiceCategoryInfo.activeVoiceItem.voice,
    text_count: textInputInfo.textLength,
    voice_volume: settingInfo.volume,
    voice_speed: settingInfo.speed,
    audio_bgm: settingInfo.bgmMusic.title,
    bgm_volume: settingInfo.bgmMusic.volume,
    text: textInputInfo.text
  }).then()
  const enableAiService = await showAiServiceDialog()
  if (enableAiService === false) {
    //点击了不同意
    return
  }

  if (isLoginRef.value === true) {
    const enableLimit = await checkLimitHasAvailable()

    if (enableLimit.countHasAvailable === false) {
      showCountLimitDialogWithPricing().then()
      console.log('数量限制')
      return
    } else if (enableLimit.storageHasAvailable === false) {
      showStorageDialogWithPricing().then()
      console.log('大小限制')
      return
    }
  }

  //已经存在文件,提醒会被替换
  if (taskInfo.audioFileUrl) {
    const againEnable = await showAgainTextToSpeechDialog(t('aiTextToSpeechWillReplace'), {
      okText: t('001207'),
      showPoint: false
    })
    if (againEnable === false) {
      return
    }
  }
  _stopTryListen()

  let text = textInputInfo.text
  //计算消耗点数
  const cost = Math.ceil(text.length / singleEquityInfo.price)


  if (isLoginRef.value === false) {
    if (singleEquityInfo.existFreeCount) {
      if (text.length <= freeTextLength) {
        //小于1000字直接转

      } else {
        const result = await showAsyncDialog(t('textToSpeechNoLoginOnlyCharsTitle').replace('#', String(freeTextLength)), {
          cancelText: t('audioCloneFreeTip').replace('#', String(freeTextLength)),
          okText: t('dialogLoginNowTitle'),
        })
        if (result === 'ok') {
          await doLogin()
          await singleEquityInfo.fetchEquityInfo()
        } else if (result === 'cancel') {
          text = text.substring(0, freeTextLength)
        } else {
          return
        }
      }

    } else {
      const result = await LimitDialog.showNoTrialTimeDialog(cost)
      if (result === 'ok') {
        PricingDialog.showOnlyVipDialog({
          source: PricingDialog.source.pointNotEnough
        }).then()
      }
      return
    }
  }

  if (isLoginRef.value === true) {
    await singleEquityInfo.fetchEquityInfo()

    if (singleEquityInfo.existFreeCount) {

    } else {
      if (pointCountRef.value < cost) {
        //点数不足
        if (isVipRef.value) {
          const result = await LimitDialog.showNoPointDialog(cost)
          if (result === 'ok') {
            PricingDialog.showVipWithPointDialog().then()
          }
        } else {
          const result = await LimitDialog.showNoTrialTimeDialog(cost)
          if (result === 'ok') {
            PricingDialog.showOnlyVipDialog({
              source: PricingDialog.source.pointNotEnough
            }).then()
          }
        }
        return
      }
    }
  }

  try {
    tipsInfo.isShowReport = false
    invalidInfo.isShowInvalidStar = false
    invalidInfo.enableDisableMatchWords = false
    TextToSpeechTrack.realStartConvert({
      trans_lang: textInputInfo.textLang,
      trans_voice: voiceCategoryInfo.activeVoiceItem.voice,
      text_count: textInputInfo.textLength,
      voice_volume: settingInfo.volume,
      voice_speed: settingInfo.speed,
      audio_bgm: settingInfo.bgmMusic.title,
      bgm_volume: settingInfo.bgmMusic.volume,
      text: textInputInfo.text
    }).then()
    const result = await taskInfo.handleTask(text, false, getSingleDeviceId())
    singleEquityInfo.fetchEquityInfo().then()
  } catch (e) {
    if (e instanceof Error) {
      if (e.message === '19916' || e.message === '19915') {
        //违禁词列表
        const wordList = Array.from(new Set(e.cause as string[]))
        const hasMatch = wordList.some(i => textInputInfo.text.includes(i))
        if (hasMatch) {
          //能匹配到, 用户点击确定的时候将违禁词替换成***
          showDialog(t('InvalidPrompts'), {
            subtitle: '敏感内容将用***代替',
            onClickOk(close) {
              const text = wordList.reduce((previousValue, currentValue) => {
                return previousValue.replaceAll(currentValue, '***')
              }, textInputInfo.text)

              textInputInfo.text = text
              tipsInfo.isShowReport = false
              invalidInfo.invalidStarCurrentIndex = 0
              invalidInfo.countInvalidStars(text)
              nextTick(() => {
                document.querySelector<HTMLButtonElement>('#next-btn-id')?.click()
              })
              close()
            },
          })
        } else {
          //无法匹配, 则在弹窗中提示违规内容
          showDialog(t('InvalidPrompts'), {
            subtitle: `敏感内容: ${wordList.join(', ')}`,
            subtitleClass: 'text-red',
            onClickOk(close) {
              invalidInfo.enableDisableMatchWords = true
              invalidInfo.disableDisableMatchWordsList = wordList
              close()
            }
          })
        }
      } else if (e.message === notEnoughPointCode.toString()) {
        LimitDialog.showNoPointDialog(cost)
          .then(result => {
            if (result === 'ok') PricingDialog.showVipWithPointDialog().then()
          })
      } else {
        showDialog(t('002917'))
      }
    } else {
      showDialog(t('002917'))
    }
  }

  singleEquityInfo.fetchEquityInfo().then()

}

async function handleClickDownloadAudio() {
  TextToSpeechTrack.clickDownloadFile().then()

  if (isVipRef.value === false) {
    const result = await LimitDialog.showDownloadOnlyVipDialog()
    if (result === 'ok') {
      PricingDialog.showOnlyVipDialog({
        source: PricingDialog.source.exportOnlyVip,
      }).then()
    }
    return
  }
  open(taskInfo.audioFileUrl, '_self')
}

//监听音频文件url变化,滚动到结果音频位置
watch(() => taskInfo.audioFileUrl, () => {
  document.querySelector('#result-audio-container-id')?.scrollIntoView({
    behavior: 'smooth'
  })
}, {
  flush: 'post'
})

//控制音色试听列表
const _voiceListTryListenInfo = reactive({
  playingId: '',
  audioEl: null as HTMLAudioElement | null,
  _stopWatch: null as Function | null,

  resetAudioEl() {
    this.audioEl?.remove()
    this.audioEl = null
    this.playingId = ''
    this._stopWatch?.()
  },
  handleClickTogglePlay(id: string, url: string) {
    TextToSpeechTrack.clickListenVoice()
    if (this.playingId === id) {
      this.resetAudioEl()
      this.playingId = ''
    } else {
      this.resetAudioEl()
      this.audioEl = new Audio(url)
      this.audioEl.play()

      this._stopWatch = watch([() => settingInfo.volume, () => settingInfo.speed], ([v, s]) => {
        if (this.audioEl) {
          this.audioEl.volume = v / 2 / 100
          this.audioEl.playbackRate = s
        }
      }, {immediate: true})

      this.audioEl.onplay = () => {
        this.playingId = id
      }
      this.audioEl.onended = () => {
        this.playingId = ''
        this.resetAudioEl()
      }
      document.body.appendChild(this.audioEl)
    }
  }
})

async function handleClickFavorite(item: typeof voiceCategoryInfo.voiceList[number]) {
  if (isLoginRef.value === false) {
    await doLogin()
  }
  await voiceCategoryInfo.toggleVoiceWithFavorite(item)
  voiceCategoryInfo.fetchVoiceList(textInputInfo.textLang, textInputInfo.text).then()
}
</script>

<template>
  <div class="p-[30px] text-[14px] size-full relative flex flex-col">
    <div
      v-if="taskInfo.isLoading"
      class="absolute inset-0 size-full z-10 rounded-[20px] bg-white"
    >
      <div class="flex-center size-full">
        <TheLoading :progress="taskInfo.progress"/>
      </div>
    </div>

    <div class="text-[16px]">{{ t('voiceType') }}</div>
    <!-- 分类标题 -->
    <ul
      v-if="voiceCategoryInfo.titleList?.length>0"
      class="flex items-center flex-nowrap gap-[20px] mt-[24px] max-w-[500px] overflow-x-auto scroll-bar-none"
    >
      <li
        class="cursor-pointer hover:text-theme text-nowrap"
        :class="[index===voiceCategoryInfo.activeTitleIndex?'text-theme text-[16px]':'']"
        v-for="(item,index) in voiceCategoryInfo.titleList"
        :key="item"
        @click="handleClickCategoryTitle(index,$event)"
      >
        <button> {{ item }}</button>
      </li>
    </ul>

    <!-- 音色列表 -->
    <ul
      class="w-[calc(100%+16px)] 2xl:w-[calc(100%+26px)] max-h-[180px] min-[1600px]:max-h-[260px] pr-[16px] 2xl:pr-[26px] text-[14px] gap-[10px] mt-[24px] grid grid-cols-4 overflow-auto scroll-bar"
    >
      <VoiceListItem
        v-for="(item,index) in voiceCategoryInfo.voiceList"
        class="group/item border rounded-[8px] flex items-center cursor-pointer flex-col gap-[4px] hover:text-primary-color hover:border-primary-color py-[12px] relative"
        :class="[voiceCategoryInfo.activeVoiceIndex===index?'text-primary-color border-primary-color':'border-[#EEE]']"
        :title="item.voice_tag"
        :enableVipIcon="item.is_free===IsFreeType.pay"
        :voiceType="item.type"
        @click="handleClickItem(item,index)"
        @clickCloneEdit="handleClickCloneEdit(item)"
        @clickDeleteClone="handleClickDeleteClone(item)"
      >
        <template #avatar>
          <div class="size-[28px] *:rounded-full relative">
            <img class="block" :src="item.icon" draggable="false" alt="avatar">

            <template v-if="item.type!=='clone-add'">
              <div
                class="absolute inset-0 w-full h-full bg-black/[0.3] hidden group-hover/item:flex-center"
                :class="[_voiceListTryListenInfo.playingId===item.voice?'!flex-center':'']"
                @click="_voiceListTryListenInfo.handleClickTogglePlay(item.voice,item.url)"
              >
                <img
                  class="size-[12px]"
                  :src="_voiceListTryListenInfo.playingId===item.voice?videoPauseIcon:videoPlayIcon"
                  draggable="false"
                  alt="avatar">
              </div>
            </template>

          </div>
        </template>

        <template #right-top v-if="item.favorite!=='clone-add'">
          <TheHeartBox
            :class="{'!block':item.favorite===IsFavoriteType.favorite}"
            class="hidden group-hover/item:block"
            :is-active="item.favorite===IsFavoriteType.favorite"
            @click.stop="handleClickFavorite(item)"
          />
        </template>

      </VoiceListItem>
    </ul>

    <div class="mt-[24px] gap-[20px] flex flex-col w-max">
      <!-- 语速设置 -->
      <SpeedSetting/>
      <!-- 音量选项 -->
      <SoundSetting/>
    </div>

    <div class="mt-[20px]">
      <BgmSetting/>
    </div>

    <div class="mt-[30px]">
      <div class="flex justify-start items-center gap-[20px]">

        <!-- 开始转换按钮 -->
        <div class="flex flex-col justify-start items-center gap-[12px]">
          <button
            class="relative gradient-button min-w-[200px] h-[40px] text-[16px] font-bold"
            :class="[textInputInfo.text.length>0&&invalidInfo.invalidStarIndexArray.length<=0?'':'opacity-50 pointer-events-none']"
            @click="handleClickStart"
          >
            <span
              v-if="singleEquityInfo.existFreeCount"
              class="absolute text-[12px] px-[8px] py-[4px] right-0 top-0 bg-[#FE4336] -translate-y-1/2 rounded-tl-[12px] rounded-br-[12px]">
              {{ t('mainStartForFree') }}
            </span>
            {{ t('convertNow') }}
          </button>

          <div
            class="text-deep-color/[0.5] text-[12px] max-w-[200px]"
          >

            <div v-if="singleEquityInfo.existFreeCount&&isLoginRef===false" class="text-center">
              {{ t('textWordsFree').replace('#', String(freeTextLength)) }}
            </div>

            <div
              v-if="!singleEquityInfo.existFreeCount"
              class="[&:lang(fr)]:inline flex justify-center items-center flex-wrap w-max max-w-[200px]"
            >
              {{ t('noFreeNotice').replace('#', String(singleEquityInfo.price)) }}
              <span class="[&:lang(fr)]:inline-flex flex items-center"><VipIcon/>
                {{ t('bonusCreditsUnit').replace('xx', '1') }}
              </span>
            </div>

          </div>

        </div>

        <!-- 试听10秒 -->
        <TryListenButton
          :class="[textInputInfo.text.length>0&&invalidInfo.invalidStarIndexArray.length<=0?'':'opacity-50 pointer-events-none']"
          :audio-el-id="audioElId"
        />

      </div>
    </div>

    <div
      v-if="taskInfo.audioFileUrl"
      id="result-audio-container-id"
      class="flex items-center mt-auto"
    >
      <TheAudio
        ref="audioRef"
        :key="taskInfo.audioFileUrl"
        :src="taskInfo.audioFileUrl"
        :duration="taskInfo.duration"
        class="min-w-[80%]"
        play-btn-class="scale-80 -translate-x-[4px]"
      />

      <button class="ml-4 relative" @click="handleClickDownloadAudio">
        <DownloadIcon/>

        <span class="absolute right-0 top-0 translate-x-full -translate-y-full">
          <img :src="onlyVipIcon" alt="vip icon">
        </span>
      </button>
    </div>

  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import {computed, onMounted, reactive, ref} from 'vue'
import TheDialog from '@/component/dialog/TheDialog.vue'
import TheCheckbox from './TheCheckbox.vue'
import AudioWave from '@/component/base/media/AudioWave.vue'
import TheAudio from '@/component/base/media/TheAudio.vue'
import TheIcon from '@/component/base/icon/TheIcon.vue'
import {useI18n} from '@/locales';
import TrackUtil from '@/track/_TrackUtil';
import {getLang} from '@central/i18n';
import useAudioClone from '@/component/online-edit/text-to-speech-v2/settings/clone/useAudioClone';
import {queryCloneTask} from '@/api/textToSpeechApi';

const {t} = useI18n()
//音色的uuid  有就是更新 没有就是创建
const prop = defineProps<{
  uuid?: string
}>()

const emit = defineEmits<{
  clickClose: []
}>()

type Status = 'start' | 'recording' | 'analyzing' | 'stop-normal' | 'stop-error'

const {
  handleStartRecorder,
  enabled,
  stream,
  handleCreateAudioByClone,
  handleStopRecorder,
  durationInfo,
  blobRef,
  handleSaveVoice,
} = useAudioClone()

const tipInfo = reactive({
  tip: '',
})

const agreeInfo = reactive({
  isAgree: Boolean(prop.uuid) ? true : false,//编辑状态默认勾选, 创建状态默认不勾选
  enableTipDialog: false,
  _timer: undefined as NodeJS.Timeout | undefined,
  get text() {
    const origin = getLang() === 'zh' ? 'https://reccloud.cn' : 'https://reccloud.com'
    const a = `<a href="${origin}/clone-agreement" class="text-primary-color" target="_blank">${t('audioServiceAgreement')}</a>`
    return t('audioCloneAgree').replace('#', a)
  },
  showTip() {
    agreeInfo.enableTipDialog = true
    agreeInfo._timer = setTimeout(() => {
      this.enableTipDialog = false
    }, 3000)
  }
})

//创建任务成功后才有uuid
const uuidRef = ref(prop.uuid)

const recorderStatusRef = ref<Status>('start')

const dialogInfo = reactive({
  voiceName: t('voicePreview'),
  async handleStartRecorder() {
    recorderStatusRef.value = 'recording'
    durationInfo.startTimer()
    await handleStartRecorder({
      async onStop(blob: Blob) {
        try {
          recorderStatusRef.value = 'analyzing'
          //不允许超过10s 超过直接报错 小于5s也报错
          if (durationInfo.duration < 5 || durationInfo.duration > 9) {
            throw t('aiCloneErrorTitle2')
          }
          uuidRef.value = await handleCreateAudioByClone(blob, dialogInfo.voiceName, prop.uuid)
          recorderStatusRef.value = 'stop-normal'
        } catch (e) {

          if (typeof e === 'string') {
            tipInfo.tip = e
          }

          recorderStatusRef.value = 'stop-error'
          console.error(e)
        }
      }
    })
  },
  async stopRecorder() {
    if (enabled.value === true) {
      handleStopRecorder()
    }
    durationInfo.stopTimer()
  }
})

//弹窗类型, 创建或更新
const dialogTypeRef = computed(() => {
  return Boolean(prop.uuid) ? 'update' : 'create'
})

onMounted(async () => {
  if (dialogTypeRef.value === 'update' && prop.uuid) {
    const data = await queryCloneTask(prop.uuid)
    dialogInfo.voiceName = data.title
    recorderStatusRef.value = 'stop-normal'
    blobRef.value = data.url
  }
})

const handleCloseDialog = () => {
  emit('clickClose')
  dialogInfo.stopRecorder()
}

const handleClickSaveVoice = async () => {
  if (agreeInfo.isAgree === false) {
    agreeInfo.showTip()
    return
  }
  TrackUtil.trackButtonClick('click_save_voice').then()
  const uuid = prop.uuid ?? uuidRef.value
  const result = await handleSaveVoice(uuid!, dialogInfo.voiceName)
  emit('clickClose')
  dialogInfo.stopRecorder().then()
}

const enableClickSaveRef = computed(() => {
  return uuidRef && dialogInfo.voiceName && recorderStatusRef.value !== 'recording' && recorderStatusRef.value !== 'analyzing' && recorderStatusRef.value !== 'stop-error'
})
</script>

<template>
  <TheDialog @clickClose="handleCloseDialog" :enable-teleport="false">
    <div class="text-[14px] p-[30px_30px_24px] bg-[#F4F7F2]">
      <div class="flex items-center gap-[16px]">
        {{ t('voiceName') }}:
        <input
          class="h-[32px] rounded-[4px] border border-[#EEE] bg-white px-[12px]" type="text"
          v-model.trim="dialogInfo.voiceName"
          maxlength="30"
        >
      </div>

      <div class="my-[20px] p-[16px] bg-[#E9EEE5] rounded-[12px] text-start">
        <p>{{ t('audioCloneTitle') }}</p>
        <div class="p-[16px] bg-white text-start rounded-[8px] mt-[16px]">
          {{ t('audioCloneContent') }}
        </div>
        <p class="mt-[24px] text-deep-color/[0.6]">
          {{ t('audioCloneDesc') }}
        </p>
        <div class="mt-[24px]">
          <template v-if="recorderStatusRef==='start'">
            <button
              class="min-w-[130px] h-[32px] bg-primary-color text-white rounded-[32px] hover:opacity-80"
              @click="dialogInfo.handleStartRecorder"
            >
              {{ t('audioStartBtn') }}
            </button>
          </template>

          <template v-else-if="recorderStatusRef==='recording'">
            <div class="flex items-center gap-[12px]">
              <button
                class="min-w-[130px] h-[32px] bg-primary-color text-white rounded-[32px] hover:opacity-80"
                @click="dialogInfo.stopRecorder"
              >
                {{ t('audioEndBtn') }}
              </button>

              <div class="flex items-center">
                <AudioWave v-if="stream" :src="stream" width="80" height="45"/>
                <p>
                  {{ t('audioCloneRecording') }}
                  (<span class="text-primary-color">{{ durationInfo.formatTime() }}</span>)
                </p>
              </div>

            </div>
          </template>

          <template v-else-if="recorderStatusRef==='stop-error'">
            <div class="flex items-center gap-[8px]">
              <button
                class="min-w-[130px] h-[32px] bg-primary-color text-white rounded-[32px] hover:opacity-80"
                @click="dialogInfo.handleStartRecorder"
              >
                {{ t('recorder') }}
              </button>
              <span class="text-[#F06464]">{{ tipInfo.tip }}</span>
            </div>
          </template>

          <template v-else-if="recorderStatusRef==='stop-normal'">
            <div class="flex items-center gap-[8px] w-full">
              <button
                class="min-w-[130px] h-[32px] bg-primary-color text-white rounded-[32px] hover:opacity-80"
                @click="dialogInfo.handleStartRecorder"
              >
                {{ t('recorder') }}
              </button>

              <div class="flex-1">
                <TheAudio v-if="blobRef" :src="blobRef" :duration="durationInfo.duration"/>
              </div>
            </div>
          </template>

          <template v-else-if="recorderStatusRef==='analyzing'">
            <div class="flex items-center gap-[8px]">
              <TheIcon :type="'loading'"/>
              {{ t('002918') }}
            </div>
          </template>

        </div>

      </div>
      <div
        class="flex items-center gap-[8px] text-[12px] cursor-pointer"
        @click="agreeInfo.isAgree=!agreeInfo.isAgree"
      >
        <div class="relative">
          <the-checkbox :checked="agreeInfo.isAgree"/>
          <div
            class="after absolute left-0 -top-full -translate-y-[20px] transition -translate-x-[20px] w-max bg-deep-color text-white px-[10px] py-[4px] rounded-[4px]"
            :class="[agreeInfo.enableTipDialog?'visible opacity-100':'invisible opacity-0']"
          >
            {{ t('aiCloneAgree') }}
          </div>
        </div>

        <span
          @click.stop
          v-html="agreeInfo.text"
        >
        </span>

      </div>
    </div>

    <template #footer-btn>
      <div class="flex items-center justify-end mb-[30px] px-[30px]">
        <button
          class="min-w-[130px] h-[32px] bg-primary-color text-white rounded-[32px] hover:opacity-80"
          :class="[enableClickSaveRef?'':'opacity-30 pointer-events-none']"
          @click="handleClickSaveVoice"
        >
          {{ t('saveVoice') }}
        </button>
      </div>
    </template>
  </TheDialog>
</template>

<style scoped>
:deep(.audio-handle) {
  @apply size-[24px];
}

:deep(.audio-handle-icon) {
  @apply size-[12px];
}

.after {
  @apply after:content-[''] after:block after:absolute after:top-[100%] after:size-[10px] after:bg-deep-color after:left-1/2 after:-translate-x-1/2 after:rotate-45 after:-translate-y-[5px];
}

</style>

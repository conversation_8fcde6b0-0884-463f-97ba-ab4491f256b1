import {reactive, readonly} from 'vue';
import {
  createTextToSpeechTaskApi,
  fetchCategoryTitleListApi,
  fetchVoiceListApi,
  querySingleEquityApi,
  queryTextToSpeechTaskApi, updateVoiceFavoriteApi
} from '@/api/textToSpeechApi';
import {GenderItemType, IsFavoriteType} from '@/interface/TextToSpeech';
import useTextToSpeech from '@/component/online-edit/text-to-speech-v2/useTextToSpeech';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import TextToSpeechTrack from '@/track/TextToSpeechTrack';
import useTrackInfo from '@/hooks/utils/useTrackInfo';
import showDialog from '@/render/showDialog';
import {useI18n} from '@/locales';
import CreateTaskError from '@/services/exception/CreateTaskError';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {t} = useI18n();

const {textInputInfo, getSingleDeviceId} = useTextToSpeech()
const {getTaskCompleteTotalDuration, setTrackByTaskId} = useTrackInfo()

//声音类型
const voiceCategoryInfo = reactive({
  titleList: [] as string[],
  activeTitleIndex: 0,

  get activeTitle(): string {
    return voiceCategoryInfo.titleList[voiceCategoryInfo.activeTitleIndex] || ''
  },
  setActiveIndex(index: number) {
    voiceCategoryInfo.activeTitleIndex = index
  },
  async fetchCategoryTitleList(lang: string) {
    voiceCategoryInfo.activeTitleIndex = 0
    const list = await fetchCategoryTitleListApi(lang)
    voiceCategoryInfo.titleList = list
  },

  activeVoiceIndex: 0,
  voiceList: [] as GenderItemType[],

  get cloneVoiceCount(): number {
    return voiceCategoryInfo.voiceList.filter(i => i.type === 'clone').length
  },

  get activeVoiceItem(): GenderItemType {
    return voiceCategoryInfo.voiceList[voiceCategoryInfo.activeVoiceIndex]
  },

  //第一个非克隆音色的索引
  getFirstNormalVoiceIndex() {
    return voiceCategoryInfo.voiceList.findIndex(i => i.type === 'normal') ?? 0
  },
  setActiveVoiceIndex(index: number) {
    voiceCategoryInfo.activeVoiceIndex = index
  },
  async fetchVoiceList(textLang: string, textInput: string) {
    const result = await fetchVoiceListApi(voiceCategoryInfo.activeTitle, textLang, textInput)
    voiceCategoryInfo.voiceList = result.list
  },
  removeVoiceItem(item: GenderItemType) {
    voiceCategoryInfo.voiceList = voiceCategoryInfo.voiceList.filter(i => i.voice !== item.voice)
  },
  async toggleVoiceWithFavorite(item: GenderItemType) {
    const success = await updateVoiceFavoriteApi(item.id as number)
    if (success === true) {
      if (item.favorite === IsFavoriteType.noFavorite) {
        TextToSpeechTrack.clickLikeVoice(item.voice).then()
        item.favorite = IsFavoriteType.favorite
      } else {
        TextToSpeechTrack.clickUnlikeVoice(item.voice).then()
        item.favorite = IsFavoriteType.noFavorite
      }
    }
  }
})

const settingInfo = reactive({
  speed: 1,//语速
  volume: 100,//音量
  bgmMusic: {
    title: 'none',
    url: '',//这里是音频文件的oss uri 并非远程连接 无法直接播放
    volume: 50,
  }
})
// 单人配音权益
const singleEquityInfo = reactive({
  limit: 0,
  used: 0,
  price: 0,
  limitWordCount: 0,
  get existFreeCount() {
    return this.limit > this.used
  },
  async fetchEquityInfo() {
    const result = await querySingleEquityApi(getSingleDeviceId())
    singleEquityInfo.limit = result.limit
    singleEquityInfo.used = result.used
    singleEquityInfo.price = result.price
    singleEquityInfo.limitWordCount = result.limit_word_count
  }
})


export default function useSettings() {

  //按照局部变量多次使用
  const taskInfo = reactive({
    isLoading: false,
    progress: 0,
    audioFileUrl: '',
    duration: 0,
    async _createTask(text: string, isTry: boolean = false, deviceId?: string) {
      const taskId = await createTextToSpeechTaskApi({
        text: text,
        speakerSpeed: settingInfo.speed,
        volume: settingInfo.volume,
        voice: voiceCategoryInfo.activeVoiceItem.voice,
        backgroundMusic: settingInfo.bgmMusic.url && {
          url: settingInfo.bgmMusic.url,
          volume: settingInfo.bgmMusic.volume,
        } || undefined,
        truncationAt: isTry ? 10 : undefined,
        deviceId: deviceId,
      })
      return taskId
    },
    async _startQuery(taskId: string, onProgress?: (progress: number) => void) {
      const result = await new SingleTaskUtil({
        async api() {
          return await queryTextToSpeechTaskApi(taskId)
        }
      }).startQuery({
        onProgress: onProgress
      })
      return result
    },

    async handleTask(text: string, isTry: boolean = false, deviceId?: string) {
      let taskId = ''
      try {
        taskInfo.isLoading = true
        taskInfo.progress = 0
        taskId = await this._createTask(text, isTry, deviceId)
        setTrackByTaskId(taskId)
        const result = await this._startQuery(taskId, progress => {
          taskInfo.progress = progress
        })
        taskInfo.isLoading = false
        taskInfo.audioFileUrl = result.file
        taskInfo.duration = result.duration / 1000
        if (isTry === false) {
          TextToSpeechTrack.convertResult({
            result: 'success',
            text_count: textInputInfo.textLength,
            fileSize: parseFloat((result.file_size / 1024 / 1024).toFixed(2)),
            format: result.file_extension,
            time: result.duration / 1000,
            trans_lang: textInputInfo.textLang,
            trans_voice: voiceCategoryInfo.activeVoiceItem.voice,
            completeDuration: getTaskCompleteTotalDuration(taskId),
          }).then()
        }
        return result
      } catch (e) {
        if (isTry === false) {
          let errorStatus = ''

          if (e instanceof CreateTaskError) {
            const s = e.payload?.status
            if (s) {
              errorStatus = s.toString()
            }
          } else if (e instanceof QueryTaskError) {
            const s = e.payload?.state?.toString()
            if (s) {
              errorStatus = s.toString()
            }
          }
          TextToSpeechTrack.convertResult({
            result: 'fail',
            reason: TaskErrorMsg.safeToJSON(e),
            message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
            status: errorStatus,
            taskId: taskId,
          }).then()
        }
        throw e
      } finally {
        taskInfo.isLoading = false
      }
    }
  })

  return {
    voiceCategoryInfo: readonly(voiceCategoryInfo),
    settingInfo: settingInfo,
    taskInfo: readonly(taskInfo),
    singleEquityInfo: singleEquityInfo,
  }
}

<script setup lang="ts" generic="T">

const props = defineProps<{
  tabList: T[],
  activeIndex: number,
}>()

const emit = defineEmits<{
  clickItem: [index: number]
}>()

function handleClickItem(index: number) {
  emit('clickItem', index)
}


</script>

<template>
  <div class="bg-white flex justify-center items-center text-[14px] rounded-[30px]">
    <ul class="flex rounded-inherit border border-dark p-[1px] h-[42px]">
      <li
        v-for="(item,index) in tabList"
        class="flex justify-center items-center py-[8px] px-[30px] cursor-pointer font-medium rounded-[30px]"
        :class="[activeIndex===index?'bg-primary-color text-white':'']"
        @click="handleClickItem(index)"
      >
        <slot :item="item">
          <span>{{ item }}</span>
        </slot>
      </li>
    </ul>
  </div>

</template>

<style scoped>

</style>

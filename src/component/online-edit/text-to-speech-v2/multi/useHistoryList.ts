import {reactive} from 'vue';
import {HistoryResultItem} from '@/interface/TextToSpeech';
import {
  deleteMultiTTSSceneTaskApi,
  fetchMultiTaskListApi,
  updateHistoryItemTitleByTaskIdApi
} from '@/api/textToSpeechApi';

const historyListInfo = reactive({
  _page: 1,
  list: [] as HistoryResultItem[],
  searchWord: '',
  fetchState: 'fetching' as 'fetching' | 'empty' | 'hasData',
  total: 0,
  async fetchList() {
    historyListInfo._page = 1
    this.fetchState = 'fetching'
    const result = await fetchMultiTaskListApi(historyListInfo._page, historyListInfo.searchWord)
    if (result?.total_count === 0) {
      this.fetchState = 'empty'
    } else {
      this.fetchState = 'hasData'
    }
    historyListInfo.list = result?.items ?? []
    historyListInfo.total = result?.total_count ?? 0
  },
  async loadMore() {
    historyListInfo._page += 1
    const list = await fetchMultiTaskListApi(historyListInfo._page, historyListInfo.searchWord)
    if (list && list.items && list.items.length > 0) {
      historyListInfo.list.push(...list.items)
    }
  },
  async deleteItemById(taskIds: string[]) {
    const success = await deleteMultiTTSSceneTaskApi(taskIds)
    if (success) {
      historyListInfo.list = historyListInfo.list.filter(item => !taskIds.includes(item.task_id))
      historyListInfo.total -= taskIds.length
      historyListInfo.list.length === 0 && (historyListInfo.fetchState = 'empty')
    }
  },
  async updateItemTitle(title: string, index: number) {
    const item = historyListInfo.list[index]
    item.title = title
    await updateHistoryItemTitleByTaskIdApi(item.task_id, title)
  },
  clearSearchWord() {
    historyListInfo.searchWord = ''
  },
})


export default function useHistoryList() {
  return {
    historyListInfo: historyListInfo,
  }
}

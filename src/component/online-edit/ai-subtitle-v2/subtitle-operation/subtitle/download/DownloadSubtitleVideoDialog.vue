<script setup lang="ts">
import {useI18n} from '@/locales'
import TheDialog from '@/component/dialog/TheDialog.vue'
import {computed, onMounted, reactive} from 'vue'
import {getFullLangMap} from '@/assets/data/ai-subtitle-data'
import {startMergeTask, queryMergeTask} from '@/api/aiSubtitleV2Api';
import TheLoading from '@/component/online-edit/TheLoading.vue';
import showDialog from '@/render/showDialog';
import {showToast} from '@/render/showToast';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import {downloadFile} from '@central/shared';

const {t} = useI18n()

const props = defineProps<{
  //字幕语言名称列表 [origin,zh,en]
  langCodeList: string[],
  uniqueId: string
}>()
const emit = defineEmits<{
  close: [],
  clickOk: [langList: string[]],
  success: [taskId: string],
  error: [e: unknown]
}>()

const selectInfo = reactive({
  selectMulti: false,
  singleTitle: '',
  multiTitle: '',
  isLoading: false,
  loadingProgress: 0
})
const uiListRef = computed(() => {
  if (selectInfo.selectMulti === true) {

    //双语字幕->源语言+翻译语言
    const list = props.langCodeList.filter(item => item !== 'origin').map(item => {
      return {
        value: ['origin', item],
        title: ['origin', item].map(i => getFullLangMap().get(i)).join(' + ')
      }
    })
    if (!selectInfo.multiTitle) {
      selectInfo.multiTitle = list[0].title ?? ''
    }

    return list
  } else {
    //单语字幕->语言
    const list = props.langCodeList.map(item => {
      return {
        title: getFullLangMap().get(item),
        value: [item]
      }
    })
    if (!selectInfo.singleTitle) {
      selectInfo.singleTitle = list[0].title ?? ''
    }
    return list
  }
})

//是否显示多选框
const enableMultiCheckBoxRef = computed(() => {
  return props.langCodeList.length > 1 && props.langCodeList.includes('origin')
})

const handleClickItem = (item: typeof uiListRef.value[number]) => {
  if (selectInfo.selectMulti === true) {
    selectInfo.multiTitle = item.title ?? ''
  } else {
    selectInfo.singleTitle = item.title ?? ''
  }
}

const closeDialog = () => {
  emit('close')
}
const handleClose = () => {
  closeDialog()
}
const handleOk = async () => {
  const item = getSelectInfo(selectInfo.selectMulti ? selectInfo.multiTitle : selectInfo.singleTitle)
  emit('clickOk', item!.value)
  if (item) {
    // TrackUtil.trackButtonClick('download-subtitle-dialog-ok', {
    //   first: item.value[0] ?? '',
    //   second: item.value[1] ?? '',
    // }).then()
    selectInfo.isLoading = true
    selectInfo.loadingProgress = 0
    try {
      const taskId = await startMergeTask(item.value, props.uniqueId)
      const taskQuery = new SingleTaskUtil({
        api() {
          return queryMergeTask(taskId)
        }
      })
      const data = await taskQuery.startQuery({
        onProgress(i) {
          selectInfo.loadingProgress = i
        }
      })
      emit('success', taskId)
      const filename = data.filename.replace(/\.[^/.]+$/, "")
      await downloadFile(data.file, filename)

      // window.open(data.file, '_self')
      showDialog(t('textBrowserDownload'), {
        cancelBtnClass: '!hidden'
      })
    } catch (e) {
      emit('error', e)
      showDialog(t('002917'))
    }

    closeDialog()
    selectInfo.isLoading = false
    selectInfo.loadingProgress = 0
  }
}

function getSelectInfo(title: string) {
  return uiListRef.value.find(item => item.title === title)
}

onMounted(() => {
  //如果只有一种语言就直接下载
  if (props.langCodeList.length === 1) {
    handleOk()
  }
})
</script>

<template>
  <TheDialog @click-cancel="handleClose" @click-close="handleClose" @click-ok="handleOk">
    <div class="p-40px flex justify-center items-start flex-col" v-if="selectInfo.isLoading===false">
      <h2 class="text-left pb-[30px] text-[16px]">{{ t('downloadSubtitleVideoTitle') }}</h2>
      <ul
        class="grid grid-cols-[repeat(3,1fr)] w-full gap-x-[22px] gap-y-[10px] text-[14px] text-deep-color"
      >
        <li class="flex gap-[8px] cursor-pointer" v-for="item in uiListRef" @click="handleClickItem(item)">
          <template
            v-if="selectInfo.selectMulti?item.title===selectInfo.multiTitle:item.title===selectInfo.singleTitle"
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="0.5" y="0.5" width="17" height="17" rx="8.5" fill="white" stroke="#18AD25"/>
              <rect x="3" y="3" width="12" height="12" rx="6" fill="#18AD25"/>
            </svg>
          </template>

          <template v-else>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="0.5" y="0.5" width="17" height="17" rx="8.5" fill="white" stroke="#CCCCCC"/>
            </svg>
          </template>
          <span>{{ item.title }}</span>
        </li>
      </ul>
    </div>

    <template v-else>
      <TheLoading class="py-[40px]" :progress="selectInfo.loadingProgress">
        {{ t('mergeVideoWaiting') }}
      </TheLoading>
    </template>
    <template #btn-left v-if="selectInfo.isLoading===false">
      <div
        class="flex pl-[40px] gap-[8px] w-full justify-start items-center cursor-pointer"
        v-if="enableMultiCheckBoxRef"
        @click="selectInfo.selectMulti=!selectInfo.selectMulti"
      >
        <svg v-show="selectInfo.selectMulti===false" width="18" height="18" viewBox="0 0 18 18" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <rect x="0.5" y="0.5" width="17" height="17" rx="3.5" fill="white" stroke="#CCCCCC"/>
        </svg>

        <svg v-show="selectInfo.selectMulti===true" width="18" height="18" viewBox="0 0 18 18" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <rect x="1.33789" y="0.796875" width="15" height="15" rx="2.5" fill="#18AD25" stroke="#18AD25"/>
          <path d="M4.83789 9.29688L7.43789 12.2969L12.8379 5.29688" stroke="white" stroke-width="2"
                stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        {{ t('003104') }}
      </div>
    </template>
    <template #footer-btn v-if="selectInfo.isLoading===true">
      <span></span>
    </template>
  </TheDialog>
</template>

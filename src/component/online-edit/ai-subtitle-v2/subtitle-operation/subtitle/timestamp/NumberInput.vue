<script setup lang="ts">

const props = withDefaults(defineProps<{
  value: number,
  min?: number,
  max?: number,
  onChange?: (value: number) => void
}>(), {
  min: 0,
  max: Infinity
})

function handleKeyDown(ev: KeyboardEvent) {
  if (ev.key === 'e') {
    ev.preventDefault()
  }
}

function handleInput(ev: Event) {
  const inputEl = ev.target as HTMLInputElement
  const value = inputEl.value.trim()
  if (/^\d*$/.test(value)) {
    const num = Number(value)
    if (num < props.min) {
      props.onChange?.(props.min)
    } else if (num > props.max) {
      props.onChange?.(props.max)
    } else {
      props.onChange?.(num)
    }
  }
  inputEl.value = toSafeString(props.value)
}

function toSafeString(value: number) {
  let maxLength = 2
  if (Number.isSafeInteger(props.max)) {
    maxLength = props.max.toString().length
  }
  return value.toString().padStart(maxLength, '0')
}

</script>

<template>
  <input
    type="number"
    :value="toSafeString(value)"
    @change="handleInput"
    @keydown="handleKeyDown"
    @click="ev=>(ev.target as HTMLInputElement).select()"
  >
</template>

<style scoped>
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>

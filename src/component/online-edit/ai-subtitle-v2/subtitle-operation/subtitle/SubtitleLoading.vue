<script lang="ts" setup>

import TheLoading from '@/component/online-edit/TheLoading.vue';
import {useI18n} from '@/locales';
import {Ref, unref} from 'vue';

const {t} = useI18n();


defineProps<{
  progress: Ref<number>
}>()
</script>

<template>
  <div class="size-full flex-center flex-col">
    <TheLoading :progress="unref(progress)"></TheLoading>
    <p>{{ t('aiSubtitleSubtitleGenerating') + '...' }}</p>
  </div>
</template>

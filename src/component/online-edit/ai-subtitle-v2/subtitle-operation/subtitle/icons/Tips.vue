<script setup lang="ts">
import {UseElementBounding} from '@vueuse/components'

defineProps<{
  title: string,
}>()
</script>

<template>
  <UseElementBounding class="relative group/icon" v-slot="{top,left,height,width}">
    <div
      class="hidden group-hover/icon:block fixed text-[12px] bg-white text-[rgba(30,61,36,.6)] px-[14px] py-[4px] rounded-[12px] whitespace-nowrap shadow-[0px_10px_10px_rgba(30,61,36,.1)]"
      :style="{
        left:left-width+'px',
        top:top+height+2+'px'
      }"
    >
      {{ title }}
    </div>
    <slot/>
  </UseElementBounding>
</template>

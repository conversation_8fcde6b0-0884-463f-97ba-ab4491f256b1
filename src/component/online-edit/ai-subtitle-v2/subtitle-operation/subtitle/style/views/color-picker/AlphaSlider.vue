<script setup lang="ts">
import {computed, onMounted, reactive, ref} from 'vue'
import {hex2rgb} from './composible';

const props = defineProps<{
  alpha: number,
  bgLinearHexColor: string,
}>()

const emit = defineEmits<{
  'update:alpha': [number]
}>()

const rgbRef = computed(() => {
  return hex2rgb(props.bgLinearHexColor)
})

const sliderElRef = ref<HTMLElement>()
const handleElRef = ref<HTMLElement>()
const trackElRef = ref<HTMLElement>()
const positionInfo = reactive({
  trackWidth: '',
})

onMounted(() => {
  positionInfo.trackWidth = sliderElRef.value!.clientWidth - 12 + 'px'
})

const handleSeek = (ev: PointerEvent) => {
  if (ev.target !== trackElRef.value!) {
    return
  }
  const present = ev.offsetX / parseFloat(positionInfo.trackWidth)
  const alpha = parseInt(present * 100 + '')
  emit('update:alpha', alpha)
}

const moveHandler = (ev: MouseEvent) => {
  const el = ev.target as HTMLElement

  const pageX = ev.pageX
  const style = getComputedStyle(el)
  const left = parseFloat(style.left)

  document.onmousemove = function (ev) {
    let x: number = left + ev.pageX - pageX
    if (x <= 0) {
      x = 0
    } else if (x > parseFloat(positionInfo.trackWidth)) {
      x = parseFloat(positionInfo.trackWidth)
    }
    const present = x / parseInt(positionInfo.trackWidth)
    const alpha = parseInt(present * 100 + '')
    emit('update:alpha', alpha)
  }

  document.onmouseup = function () {
    document.onmousemove = document.onmouseup = null
  }
}

const leftRef = computed(() => {
  return props.alpha + '%'
})

const trackWidthRef = computed(() => {
  return parseInt(positionInfo.trackWidth) - 2 + 'px'
})
</script>

<template>
  <div
    class="w-full h-full rounded-[8px] bg relative"
    ref="sliderElRef"
  >
    <div class="absolute inset-0 bg-alpha rounded-[8px] pointer-events-none"></div>
    <div class="absolute inset-0 bg rounded-[8px] pointer-events-none"></div>
    <div
      class="relative h-full"
      ref="trackElRef"
      :style="{width:trackWidthRef}"
      @click="handleSeek"
    >
      <div
        ref="handleElRef"
        class="w-[14px] h-[14px] bg-transparent rounded-full absolute shadow-handle border-2 border-white top-[-2px]"
        :style="{left: leftRef}"
        @mousedown.stop.prevent="moveHandler"
      >
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.bg {
  --r: v-bind('rgbRef.r');
  --g: v-bind('rgbRef.g');
  --b: v-bind('rgbRef.b');
  background: linear-gradient(to right, rgba(var(--r), var(--g), var(--b), 0) 0%, rgb(var(--r), var(--g), var(--b)) 100%);
  box-shadow: inset 0 0 0 1px #dbdbdb;
}

.bg-alpha {
  background: url("@/assets/images/ai-subtitle/alpha-bg.svg") repeat-x;
}

.shadow-handle {
  filter: drop-shadow(1px 1px 4px rgba(0, 0, 0, 0.50));
}
</style>

<script setup lang="ts" generic="T">

import TheFilterDropdown from '@/component/base/dropdown/TheFilterDropdown.vue';

const props = defineProps<{
  list: Array<T>,
  selectTitle: string,
  containerClass?: string,
}>()

const emit = defineEmits<{
  openDialog: [],
}>()

const openDialog = () => {
  emit('openDialog')
}

</script>

<template>
  <TheFilterDropdown
    class="!border-[#EEE] !rounded-[8px] !h-[40px]"
    :select-title="selectTitle"
    list-class="!w-full max-h-[200px]"
    :list-container-class="`${containerClass} w-full`"
    title-class="text-[14px] line-clamp-1"
    :select-list="list"
    @openDialog="openDialog"
  >
    <template #title>
      <slot name="title"></slot>
    </template>
    <template #item="{item}">
      <slot :item="item"></slot>
    </template>
  </TheFilterDropdown>
</template>

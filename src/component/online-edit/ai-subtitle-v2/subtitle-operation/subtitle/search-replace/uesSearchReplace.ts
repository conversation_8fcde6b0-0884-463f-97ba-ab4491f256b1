import {nextTick, reactive} from 'vue';
import {debounce} from '@/utils/debounce';
import useAiSubtitleV2 from '@/component/online-edit/ai-subtitle-v2/useAiSubtitleV2';
import AiSubtitleTrack from '@/track/AiSubtitleTrack';
import {ReplaceType} from '@/interface/AiTranslate';

const {langInfo, scrollRef} = useAiSubtitleV2();
const searchInfo = reactive({
  searchWord: '',
  _searchIndex: -1,
  get searchCount() {
    if (this.searchWord.length > 0) {
      return _getSearchResultList(this.searchWord).length
    }

    return 0
  },
  get searchIndex() {
    return this._searchIndex
  },
  set searchIndex(value) {
    this._searchIndex = value
    this.doHighlightMarkEl()
  },
  doHighlightMarkEl() {
    const list = _getSearchResultList(this.searchWord)
    const item = list[this.searchIndex]
    if (item) {
      const reg = new RegExp(this.searchWord, 'g')
      const l = item.type === ReplaceType.origin ? langInfo.selectedLangList[0] : langInfo.selectedLangList[1]
      //行数index
      const lineIndex = l.subtitles.findIndex(i => i.start === item.instance.start && i.end === item.instance.end)
      //滚动到当前行
      scrollRef.value.scrollToItem(lineIndex)
      //滚动需要花点时间, 这里给点延迟
      setTimeout(() => {
        //删除active样式, 用于下一个标记
        document.querySelector('mark.active')?.classList.remove('active')
        if (item.type === ReplaceType.origin) {
          const i = Array.from(item.instance.text.toString().matchAll(reg)).findIndex(i => i.index === item.index)
          if (i !== -1) {
            const els = document.querySelectorAll<HTMLElement>(`#subtitle-list-container-id [data-index="${lineIndex}"] .origin mark`)
            els[i]?.classList.add('active')
          }
        } else {
          const i = Array.from(item.instance.text.toString().matchAll(reg)).findIndex(i => i.index === item.index)
          if (i !== -1) {
            const els = document.querySelectorAll<HTMLElement>(`#subtitle-list-container-id [data-index="${lineIndex}"] .translate mark`)
            els[i]?.classList.add('active')
          }
        }
      }, 100)
    }
  },
  scrollPosition(offset: number, type: 'auto' | 'smooth') {
    const el = document.querySelector('#subtitle-list-container-id')
    el?.scrollTo({
      top: offset,
      behavior: type
    })
  },
  preSearchWord() {
    if (this.searchIndex > 0) {
      this.searchIndex--
    }
  },
  nextSearchWord() {
    if (this.searchIndex < this.searchCount - 1) {
      this.searchIndex++
    }
  },
  clearSearch() {
    searchInfo.searchWord = ''
    searchInfo.searchIndex = -1
    // searchInfo.scrollPosition(0, 'auto')
    // searchInfo.searchCount = 0
    // replaceInfo.replaceWord = ''
  },
  highlightSearchWord(text: string) {
    if (!this.searchWord.trim()) {
      return text + '\n'
    }
    return text.replaceAll(this.searchWord, `<mark>${this.searchWord}</mark>`) + '\n'
  },
  handleInput: debounce((ev) => {
    searchInfo.searchWord = (ev.target as HTMLInputElement).value
    searchInfo.searchWord = (ev.target as HTMLInputElement).value
    nextTick(() => {
      if (searchInfo.searchCount > 0) {
        searchInfo.searchIndex = 0
      }
    }).then()
  }, 500),
  replaceReset() {
    // const marks = document.querySelectorAll('#subtitle-list-container-id mark')
    // searchInfo.searchCount = marks.length
  },
})

const replaceInfo = reactive({
  replaceWord: '',
  handleReplaceSingle() {
    AiSubtitleTrack.clickReplace().then()
    //单个替换, 首先找到所有的匹配到的文案,组成一个{subtitle,index}, 点击替换的时候根据索引进行替换
    const originText = searchInfo.searchWord
    const targetText = replaceInfo.replaceWord
    const item = _getSearchResultList(originText)[searchInfo._searchIndex]
    if (item.instance.text) {
      const textList = Array.from(item.instance.text)
      textList.splice(item.index + 1, originText.length - 1)
      textList.splice(item.index, 1, ...targetText)
      item.instance.text = textList.join('')
    }

    searchInfo.searchIndex++
    nextTick(() => {
      //替换一个 索引-1
      searchInfo.searchIndex--
    }).then()

    if (searchInfo.searchIndex >= searchInfo.searchCount + 1) {
      searchInfo.searchIndex = 1
    }

    langInfo.submitAllSubtitle().then()

    // function _getSearchResultList(searchText: string) {
    //   const resultList = []
    //   const [origin, trans] = langInfo.selectedLangList
    //   const l = origin || trans
    //   for (let i = 0; i < l.subtitles.length; i++) {
    //     if (origin?.subtitles?.[i]) {
    //       if (origin.subtitles[i].text.includes(searchText)) {
    //         const text = origin.subtitles[i].text
    //         const matchList = _getMatchIndex(text, searchText)
    //         for (let matchIndex of matchList) {
    //           resultList.push({
    //             instance: origin.subtitles[i],
    //             index: matchIndex,
    //           })
    //         }
    //       }
    //     }
    //     if (trans?.subtitles?.[i]) {
    //       if (trans.subtitles[i].text.includes(searchText)) {
    //         const text = trans.subtitles[i].text
    //         const matchList = _getMatchIndex(text, searchText)
    //         for (let matchIndex of matchList) {
    //           resultList.push({
    //             instance: trans.subtitles[i],
    //             index: matchIndex,
    //           })
    //         }
    //       }
    //     }
    //   }
    //
    //   return resultList
    // }
    //
    // function _getMatchIndex(fullText: string, searchText: string): number[] {
    //   const list = []
    //   let index = -1
    //   for (let i = 0; i < fullText.length; i++) {
    //     index = fullText.indexOf(searchText, index + 1)
    //     if (index === -1) {
    //       break
    //     } else {
    //       list.push(index)
    //     }
    //   }
    //   return list
    // }

  },
  handleReplaceAll() {
    AiSubtitleTrack.clickReplaceAll().then()
    const originText = searchInfo.searchWord
    const targetText = replaceInfo.replaceWord
    const list = langInfo.selectedLangList;
    list.forEach(value => {
      value.subtitles.forEach(i => {
        i.text = i.text.replaceAll(originText, targetText)
      })
    })
    nextTick(() => {
      searchInfo.replaceReset()
    }).then()
    langInfo.submitAllSubtitle().then()
  },
})

function _getSearchResultList(searchText: string) {
  const resultList = []
  const [originItem, transItem] = langInfo.selectedLangList
  const list = getMaxLengthList(originItem?.subtitles, transItem?.subtitles);
  for (let i = 0; i < list.length; i++) {
    const origin = originItem?.subtitles?.[i]
    const trans = transItem?.subtitles?.[i]
    if (origin) {
      if (origin.text.includes(searchText)) {
        const text = origin.text
        const matchList = _getMatchIndex(text, searchText)
        for (let matchIndex of matchList) {
          resultList.push({
            instance: origin,
            index: matchIndex,
            type: ReplaceType.origin,
          })
        }
      }
    }
    if (trans) {
      if (trans.text.includes(searchText)) {
        const text = trans.text
        const matchList = _getMatchIndex(text, searchText)
        for (let matchIndex of matchList) {
          resultList.push({
            instance: trans,
            index: matchIndex,
            type: ReplaceType.translate,
          })
        }
      }
    }
  }

  return resultList
}

function _getMatchIndex(fullText: string, searchText: string): number[] {
  const list = []
  let index = -1
  for (let i = 0; i < fullText.length; i++) {
    index = fullText.indexOf(searchText, index + 1)
    if (index === -1) {
      break
    } else {
      list.push(index)
    }
  }
  return list
}

function getMaxLengthList(a?: Array<any>, b?: Array<any>) {
  if (a && b) {
    return a.length > b.length ? a : b
  }
  return a || b || []
}

export default function useSearchReplace() {

  return {
    searchInfo: searchInfo,
    replaceInfo: replaceInfo,
  }
}

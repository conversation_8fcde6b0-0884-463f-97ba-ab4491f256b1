---
import chinaIcon from "@/assets/images/ai-subtitle/china-icon.jpg";
import englishIcon1 from "@/assets/images/ai-subtitle/english-icon-1.jpg";
import englishIcon2 from "@/assets/images/ai-subtitle/english-icon-2.jpg";
import englishIcon3 from "@/assets/images/ai-subtitle/english-icon-3.jpg";
import englishIcon4 from "@/assets/images/ai-subtitle/english-icon-4.jpg";
import japIcon from "@/assets/images/ai-subtitle/jap-icon.jpg";
import frIcon from "@/assets/images/ai-subtitle/fr-icon.jpg";
import ptIcon1 from "@/assets/images/ai-subtitle/pt-icon-1.jpg";
import ptIcon2 from "@/assets/images/ai-subtitle/pt-icon-2.jpg";
import esIcon1 from "@/assets/images/ai-subtitle/es-icon-1.jpg";
import esIcon2 from "@/assets/images/ai-subtitle/es-icon-2.jpg";
import esIcon3 from "@/assets/images/ai-subtitle/es-icon-3.jpg";
import deIcon1 from "@/assets/images/ai-subtitle/de-icon-1.jpg";
import deIcon2 from "@/assets/images/ai-subtitle/de-icon-2.jpg";
import moreIcon from "@/assets/images/ai-subtitle/more-icon.svg";

import {useI18n} from "@/locales";
import {getLang} from "@central/i18n";
const {t} = useI18n();
const list = [
  {
    title: t("formalChina"),
    icon: [chinaIcon],
  },
  {
    title: t("formalEnglish"),
    icon: [englishIcon1, englishIcon2, englishIcon3, englishIcon4],
  },
  {
    title: t("Japanese"),
    icon: [japIcon],
  },
  {
    title: t("French"),
    icon: [frIcon, englishIcon3],
  },
  {
    title: t("Portuguese"),
    icon: [ptIcon1, ptIcon2],
  },
  {
    title: t("Spanish"),
    icon: [esIcon1, esIcon2, esIcon3],
  },
  {
    title: t("German"),
    icon: [deIcon1, deIcon2],
  },
  {
    title: t("moreMore"),
    icon: [moreIcon],
  },
];
---

<div class="relative pt-[24px] md:pt-[40px] pb-40px">
  <ul
    class="relative lg:w-[1100px] h-auto flex flex-wrap gap-20px md:grid sm:justify-center sm:items-center sm:gap-[0] lg:gap-[40px] mx-auto sm:grid-cols-4 sm:grid-rows-none overflow-visible px-20px lg:px-40px py-10px lg:py-40px"
  >
    {
      list.map((item, index) => {
        return (
          <li
            class="bg-#FFF cursor-pointer sm:scale-80 lg:scale-100 md:w-[220px] md:h-[152px] group shadow-[0px_20px_40px_0px_rgba(30,61,36,0.05)] border-transparent
              rounded-[20px] overflow-hidden border-3 hover:border-3px hover:border-solid hover:border-[rgba(24,173,37)] hover:scale-[1.15] duration-400 flex-item-three py-16px lg:py-0"
          >
            <div class="relative">
              <div class="flex justify-center mt-16px md:mt-40px text-[12px] md:text-18px will-change-transform">
                {item.title}
              </div>
              <div class="flex justify-center items-center mt-4px lg:mt-10px md:mt-26px gap-6px md:gap-14px">
                {item.icon.map((i, index) => {
                  return (
                    <img
                      class="w-13px h-10px md:w-32px md:h-24px"
                      src={i}
                      alt={item.title}
                      draggable="false"
                      loading="lazy"
                    />
                  );
                })}
              </div>
            </div>
          </li>
        );
      })
    }
  </ul>
</div>

<style lang="scss">
  @media screen and (min-width: 1024px) {
    .feat-list {
      padding: 40px;
    }
  }

  @media screen and (max-width: 1023px) {
    .feat-list {
      pointer-events: none;
    }
  }

  @media screen and (max-width: 639px) {
    .feat-list {
      width: 100%;
      justify-content: center;
      align-items: flex-start;
      gap: 18px;
      grid-template-columns: repeat(3, 96px);
      grid-template-rows: repeat(3, 96px);

      li {
        transform: scale(0.5);
        transform-origin: left top;
      }

      .desc {
        font-size: 12px;
      }
    }
  }

  @media screen and (max-width: 424px) {
    .feat-list {
      width: calc(100px * 3 + 18px * 2) !important;
      justify-content: center !important;

      li {
        transform-origin: left top;
      }
    }
  }

  @media screen and (max-width: 425px) {
    .flex-item-three {
      flex: 0 0 calc((100% - 40px) / 3);
    }
  }
</style>

<script setup lang="ts">

import {MediaType} from '@/interface/SpeechToText';
import {computed, ref, watch} from 'vue';
import useHistoryList from '@/component/online-edit/speech-to-text/history-list/useHistoryList';
import LoadingIcon from '@/component/online-edit/speech-to-text/history-list/history-item/LoadingIcon.vue';
import useSpeechToText from '@/component/online-edit/speech-to-text/useSpeechToText';
import {useI18n} from '@/locales';
import retryIcon from '@images/text-to-video/function-icon/retry-icon.svg';
import retryHoverIcon from '@images/text-to-video/function-icon/retry-hover-icon.svg';
import deleteIcon from '@images/text-to-video/function-icon/del-icon.svg';
import deleteHoverIcon from '@images/text-to-video/function-icon/del-hover-icon.svg';

import Delete from '@/component/online-edit/speech-to-text/history-list/history-item/DeleteIcon.vue';
import ExportIcon from '@/component/online-edit/speech-to-text/history-list/history-item/ExportIcon.vue';
import {vTextClamp} from '@/component/online-edit/speech-to-text/history-list/history-item/directives/text';
import SpeechToTextTrack from '@/track/SpeechToTextTrack';
import editIcon from '@images/online-edit/edit-icon.svg';
import {vOnClickOutside} from '@vueuse/components';
import {debounce} from '@/utils/debounce';
import {vFocus} from '@/directives/input';
import {showCountLimitDialog, showStorageLimitDialog} from '@/render/showLimitDialog';
import useLoginService from '@/services/useLoginService';
import SquareCheckBox from '@/component/base/batch-process/SquareCheckBox.vue';

const {t} = useI18n()
const {checkItemStatus} = useHistoryList()

const isCheckedRef = ref<boolean>(false)
const props = defineProps<{
  title: string,
  mediaType: MediaType,
  cover: string,
  content: string,
  timestamp: string,
  durationText: string,
  state: number,
  progress: number,
  taskId: string,
  batchState: boolean
}>()

const emit = defineEmits<{
  clickDelete: [],
  clickRetry: [],
  clickExport: [ev: Event],
  clickReplace: [],
  updateTitle: [title: string],
  clickBatch: [],
  clickChecked: [isBatchChecked: boolean],
  clickToDetail: [],
}>()

watch(() => props.batchState, (newValue, oldValue) => {
  if (newValue === false && oldValue === true) {
    isCheckedRef.value = false
  }
})

const stateRef = computed(() => {
  const status = checkItemStatus(props.state)
  return status
})

function handleExport(ev: Event) {
  emit('clickExport', ev)
}

function handleDelete() {
  emit('clickDelete')
}

function handleRetry() {
  emit('clickRetry')
}

function handleReplace() {
  emit('clickReplace')
}

async function handleClickToDetail() {
  if (isEditingRef.value) {
    handleBlur()
  } else {
    if (stateRef.value === 'success') {
      emit('clickToDetail')
    }
  }
}

const isEditingRef = ref(false)

const handleEnableEdit = (ev: Event) => {
  isEditingRef.value = true
}

const handleBlur = () => {
  isEditingRef.value = false
}

const handleUpdateTitle = debounce((ev: Event) => {
  const el = ev.target as HTMLInputElement
  if (el.value?.trim()) {
    emit('updateTitle', el.value!)
  } else {
  }
}, 0)

const handleFocus = (ev: FocusEvent) => {
  (ev.target as HTMLInputElement).select()
}

function handleClickBatch() {
  isCheckedRef.value = !isCheckedRef.value
  if (props.batchState === false) {
    emit('clickBatch')
  }
  emit('clickChecked', isCheckedRef.value)

}
</script>

<template>
  <div
    class="relative flex flex-col text-[#2D2D3] p-[20px] bg-white shadow-[0_10px_20px_0_rgba(30,61,36,0.08)] text-[14px]"
    :class="[
      (batchState === true) ? '' :(stateRef==='success' ? 'success group/success' : 'group/fail'),
      (stateRef === 'loading' || batchState === true) ? '' : 'success'
      ]"
    @click="handleClickToDetail"
    v-on-click-outside="handleBlur"
  >
    <div
      :class="{'!block z-11 size-full bg-black/[0.5] rounded-inherit cursor-pointer':batchState,'!hidden':stateRef==='loading'}"
      class="hidden group-hover/success:block group-hover/fail:block absolute left-0 top-0"
      @click.stop="handleClickBatch"
    >

      <svg
        v-if="batchState===false"
        class="absolute left-[10px] top-[10px] z-11"
        width="20" height="20" viewBox="0 0 20 20" fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect x="0.5" y="0.5" width="19" height="19" rx="3.5" fill="white" fill-opacity="0.2" stroke="#2D2D33"/>
      </svg>

      <SquareCheckBox
        v-else
        class="absolute left-[10px] top-[10px] z-11" :is-checked="isCheckedRef"
        @click.stop="handleClickBatch"/>
    </div>

    <h3
      class="font-bold text-[16px] flex-center text-dark hover:text-theme z-10 group/title"
      :class="[stateRef==='loading'?'pointer-events-none':'']"
      @click.stop="handleEnableEdit"
    >
      <span
        v-if="!isEditingRef"
        class="peer w-full text-center break-words ml-[24px] pr-[1em] line-clamp-1 outline-none focus:border-b-1 border-primary-color focus:cursor-text cursor-pointer"
      >
        {{ title }}
      </span>
      <input
        v-else
        class="peer w-full text-center bg-transparent ml-[24px] pr-[1em] line-clamp-1 outline-none focus:border-b-1 border-primary-color focus:cursor-text cursor-pointer"
        v-focus
        :value="title"
        @change="handleUpdateTitle"
        @focus="handleFocus"
        @keyup.enter="handleBlur"
      />
      <img
        :class="{'!invisible':batchState}"
        class="size-[20px] opacity-60 invisible cursor-pointer group-hover/title:visible peer-focus:invisible"
        :src="editIcon"
        alt=""
      />
    </h3>

    <template v-if="stateRef==='loading'">
      <div class="flex-center h-full flex-col gap-[34px]">
        <LoadingIcon class="animate-spin"/>
        <p>{{ t('speechConverting') }} {{ progress }}%</p>
      </div>
    </template>

    <template v-else-if="stateRef==='fail'">
      <div class="bg-[#1C1E1C]/[0.8] size-full mt-[16px] rounded-[8px] flex-center flex-col flex-1 z-10">

        <span class="text-[18px] text-[#F06464] font-semibold text-center">{{ t('generationFailed') }}</span>
        <div class="flex gap-[24px] mt-[12px] [&:lang(pt)]:flex-col [&:lang(de)]:flex-col">


          <div
            v-if="state===-11"
            class="flex gap-[8px] items-center justify-center group cursor-pointer"
            @click="handleReplace"
          >
            <img class="block group-hover:hidden" :src="retryIcon" :alt="t('dialogTitleReplace')">
            <img class="hidden group-hover:block" :src="retryHoverIcon" :alt="t('dialogTitleReplace')">
            <span class="text-[16px] font-bold text-white group-hover:text-primary-color">
                {{ t('dialogTitleReplace') }}
            </span>
          </div>

          <div
            v-else
            class="flex gap-[8px] items-center justify-center group cursor-pointer"
            @click="handleRetry"
          >
            <img class="block group-hover:hidden" :src="retryIcon" :alt="t('textToVideoRetry')">
            <img class="hidden group-hover:block" :src="retryHoverIcon" :alt="t('textToVideoRetry')">
            <span class="text-[16px] font-bold text-white group-hover:text-primary-color">
                {{ t('textToVideoRetry') }}
            </span>
          </div>


          <div class="flex gap-[8px] items-center justify-center group cursor-pointer" @click="handleDelete">
            <img class="block group-hover:hidden size-[20px]" :src="deleteIcon" :alt="t('002572')">
            <img class="hidden group-hover:block size-[20px]" :src="deleteHoverIcon" :alt="t('002572')">
            <span class="text-[16px] font-bold text-white group-hover:text-primary-color">
                {{ t('002572') }}
              </span>
          </div>
        </div>

      </div>
    </template>

    <template v-else-if="stateRef==='success'">
      <template v-if="mediaType===MediaType.video">
        <img
          v-if="mediaType===MediaType.video"
          :src="cover"
          draggable="false"
          loading="lazy"
          class="w-full 4xl:max-w-[400px] mx-auto aspect-video rounded-[10px] mt-[16px] object-cover"
          alt="cover img"
        >
      </template>

      <p class="mt-[16px] flex-1 overflow-hidden text-dark">
        <span v-text-clamp>{{ content }}</span>
      </p>

    </template>

    <div class="flex-center mt-3 text-[14px] z-10">
      <span class="block opacity-50 group-hover/success:hidden">
        {{ timestamp }}
      </span>
      <span
        class="group-hover/success:block hidden pr-[20px] group"
        @click.stop="handleDelete"
      >
        <Delete
          class="size-[20px] opacity-60 group-hover:opacity-100 group-hover:text-primary-color"
        />
      </span>

      <span class="ml-auto opacity-50 block group-hover/success:hidden">
        {{ durationText }}
      </span>

      <span
        class="ml-auto group-hover/success:block hidden group pl-[20px]"
        @click.stop="handleExport"
      >
        <ExportIcon
          class="opacity-60 group-hover:opacity-100 size-[18px] group-hover:text-primary-color"
        />
      </span>
    </div>
  </div>
</template>

<style scoped>
.success {
  @apply relative hover:outline outline-primary-color outline-2 cursor-pointer hover:bg-[#EFF6F0] overflow-hidden
  hover:before:block hover:after:block transition hover:shadow-[0_4px_40px_0_rgba(24,173,37,0.1)] hover:translate-y-[4px];
}

.success::before {
  display: none;
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100px;
  height: 100px;
  background: url("@/assets/images/speech-to-text/bg-left.svg") no-repeat;
  background-size: cover;
  transform: translate(-28%, -28%);
  opacity: 0.5;
  z-index: 1;
}

.success::after {
  display: none;
  content: '';
  opacity: 0.5;
  position: absolute;
  right: 0;
  bottom: 0;
  width: 94px;
  height: 94px;
  background: url("@/assets/images/speech-to-text/bg-right.svg") no-repeat;
  background-size: 100%;
  transform: translate(40%, 40%);
  z-index: 1;
}
</style>

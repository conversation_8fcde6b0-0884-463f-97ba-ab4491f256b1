//录制音频弹框
import {computed, h, ref, render} from 'vue';
import NoMicPermissionTip from '@/component/speech-recognition/tips/NoMicPermissionTip.vue';
import {useUserMedia} from '@vueuse/core';
import RecorderUtil from '@/utils/RecorderUtil';
import {showDangerToast} from '@/render/showToast';
import {useI18n} from '@/locales';
import useStartSelectFile from '@/component/online-edit/speech-to-text/start-select-file/useStartSelectFile';
import {CheckFileItem} from '@/interface/SpeechToText';
import useLoginService from '@/services/useLoginService';
import SpeechToTextTrack from '@/track/SpeechToTextTrack';
import {showAiServiceDialog} from '@/render/showAiServiceDialog';
import useSpeechToText from '@/component/online-edit/speech-to-text/useSpeechToText';
import LimitDialog from '@/render/LimitDialog';
import PricingDialog from '@/render/PricingDialog';

const {t} = useI18n()
const {fileToCheckFileItem, handleStorageLimit, handleCountLimit} = useStartSelectFile()

const {equityInfo} = useSpeechToText()
const {isLoginRef, pointCountRef, doLogin, checkLimitHasAvailable, isVipRef} = useLoginService()

const isShowAudioRef = ref<boolean>(false)
const recorder = new RecorderUtil()
const {start, stop, stream: streamRef} = useUserMedia({
  constraints: {
    audio: true
  }
})
const durationRef = computed(() => {
  return recorder.duration
})

//检查是否有麦克风权限
async function checkEnableMicPermission() {
  try {
    const result = await navigator.mediaDevices.getUserMedia({audio: true})
    result.getTracks().forEach(track => track.stop())
    return true
  } catch (e) {
    return false
  }
}

// 没有麦克风权限提示
function showNoMicPermissionTip() {
  const container = document.createElement('div')
  document.documentElement.appendChild(container)
  render(h(NoMicPermissionTip, {
    onClickOk() {
      container.remove()
    },
  }), container)
}

function formatMilliseconds(ms: number) {
  const seconds = Math.ceil(ms / 1000)
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  const hourStr = String(hours).padStart(2, '0')
  const minuteStr = String(minutes).padStart(2, '0')
  const secondStr = String(remainingSeconds).padStart(2, '0')
  return `${hourStr}:${minuteStr}:${secondStr}`
}

async function recorderAudio() {
  SpeechToTextTrack.clickAudioRecord().then()
  if (isLoginRef.value === false) {
    await doLogin()
  }
  const enableAiService = await showAiServiceDialog()
  if (enableAiService === false) {
    //点击了不同意
    return
  }
  const enableLimit = await checkLimitHasAvailable()

  if (enableLimit.countHasAvailable === false) {
    handleCountLimit().then()
    console.log('数量限制')
    return
  } else if (enableLimit.storageHasAvailable === false) {
    handleStorageLimit().then()
    console.log('大小限制')
    return
  }

  if (equityInfo.used >= equityInfo.limit && pointCountRef.value <= 1) {
    //@ts-ignore
    const result = await LimitDialog.showNoPointDialog(undefined)
    const source = PricingDialog.source.recordVoiceNotEnough
    if (result === 'ok') {
      if (isVipRef.value) {
        PricingDialog.showVipWithPointDialog({source: source}).then()
      } else {
        PricingDialog.showOnlyVipDialog({
          source: source,
        }).then()
      }
    }
    return
  }

  const enable = await checkEnableMicPermission()
  if (enable === false) {
    showNoMicPermissionTip()
    return
  }

  const stream = await start()
  if (stream) {
    isShowAudioRef.value = true
    recorder.start(stream)
  } else {
    showDangerToast(t('002778'))
  }

}

function _handleRecorderStop() {
  stop()
  isShowAudioRef.value = false
}

function handleRecordEnd() {
  _handleRecorderStop()
  recorderStop().then()
}

async function handleRecordEndWithFile() {
  _handleRecorderStop()
  const file = await recorderStop()
  return file
}

function handleRecordPause() {
  SpeechToTextTrack.clickAudioPause().then()
  recorder.pause()
  streamRef.value?.getTracks().forEach(item => {
    item.enabled = false
  })
}

function handleRecordResume() {
  recorder.resume()
  streamRef.value?.getTracks().forEach(item => {
    item.enabled = true
  })
}

function getVideoFilename() {
  let padTo2Digits = function (num: number) {
    return num.toString().padStart(2, '0');
  }

  let date = new Date();
  let time = [
    date.getFullYear(),
    padTo2Digits(date.getMonth() + 1),
    padTo2Digits(date.getDate()),
    padTo2Digits(date.getHours()),
    padTo2Digits(date.getMinutes()),
    padTo2Digits(date.getSeconds()),
  ].join('-');
  return "recording-" + time
}

function recorderStop(): Promise<CheckFileItem> {
  return new Promise((resolve) => {
    recorder.onStop = async (blob) => {
      const audioRecordFile = new File([blob], `${getVideoFilename()}.mp3`, {
        type: 'audio/mp3'
      })
      const items = await fileToCheckFileItem([audioRecordFile], 'record')
      resolve(items[0])
    }
    recorder.stop()
  })
}


export default function useAudioRecordFile() {

  return {
    streamRef,
    durationRef,
    isShowAudioRef,
    formatMilliseconds,
    recorderAudio,
    handleRecordEnd,
    handleRecordEndWithFile,
    handleRecordPause,
    handleRecordResume,
  }
}

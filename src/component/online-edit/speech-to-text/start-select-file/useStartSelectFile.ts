/**
 * @unocss-include
 */
import {h, readonly, ref, render} from 'vue';
import AddVideoDialog from '@/component/online-edit/select-vIdeo/AddVideoDialog.vue';
import {VideoItemInfo} from '@/interface/OnlineEdit';
import {AddFileErrorMsg, CheckFileItem, CreateTaskApiParams, RealStartFileItemType} from '@/interface/SpeechToText';
import showDialog from '@/render/showDialog';
import useLoginService from '@/services/useLoginService';
import StartUploadDialog from '@/component/online-edit/speech-to-text/start-select-file/StartUploadDialog.vue';
import {UploadResultType} from '@/api/apOSSApi';
import UploadApi from '@/api/UploadApi';
import {getVipPageUrl} from '@/utils/open';
import {createSpeechToTextTaskApi, querySpeechToTextTaskApi} from '@/api/speechToTextApi';
import {putVideoInfo} from '@/api/recOSSApi';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import {useI18n} from '@/locales';
import {showPointNotEnoughDialog} from '@/render/showPointDialog';
import SpeechToTextTrack from '@/track/SpeechToTextTrack';
import useTrackInfo from '@/hooks/utils/useTrackInfo';
import showAiSatisfyDialog from '@/render/showAiSatisfyDialog';
import FailedDialog from '@/component/online-edit/speech-to-text/start-select-file/FailedDialog.vue';
import AudioUtil from '@/utils/AudioUtil';
import LimitDialog from '@/render/LimitDialog';
import PricingDialog from '@/render/PricingDialog';
import {PriceTabType} from '@/interface/Vip';
import {getRegionPath} from '@/utils/util';
import CreateTaskError from '@/services/exception/CreateTaskError';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {t} = useI18n()
const {getTaskCompleteTotalDuration, setTrackByTaskId} = useTrackInfo()
const {doLogin, isLoginRef, isVipRef} = useLoginService()
const supportVideoList = ['.mp4', '.3GP', '.flv', '.ASF', '.WMV', '.MKV', '.mov', '.WebM', '.MTS', '.avi', '.ts', '.mpeg', '.m4v', '.mpg'] as const
const supportAudioList = ['.mp3', '.m4a', '.flac', '.wav'] as const

const supportFile = {
  //页面显示支持的格式, 实际上支持上面所有的
  text: 'mp4/m4v/mov/mkv/webm/3gp/mp3/m4a/flac/wav',
  //可以检查音轨的格式
  get checkAbleExt(): string[] {
    return ['mp4', '3gp', 'm4a', 'flac', 'wav', 'm4v', 'mov', 'mkv', 'webm', 'mp3'].map(i => i.toLowerCase())
  },
  get video() {
    return supportVideoList.map(i => i.toLowerCase())
  },
  get audio() {
    return supportAudioList.map(i => i.toLowerCase())
  },
  get all() {
    return [...new Set([...supportFile.video, ...supportFile.audio])]
  },
  getMediaType(ext: string) {
    ext = ext.toLowerCase()
    ext = ext.startsWith('.') ? ext : `.${ext}`
    if (supportFile.video.includes(ext as any)) {
      return 'video'
    } else if (supportFile.audio.includes(ext as any)) {
      return 'audio'
    }
  }
} as const


export default function useStartSelectFile() {

  function showMultipleFileDialog() {
    return new Promise<VideoItemInfo[]>(resolve => {
      const dom = document.createElement('div');
      document.body.appendChild(dom);
      const closeDialog = () => {
        dom.remove()
        resolve([])
      }
      render(h(AddVideoDialog, {
        multiple: true,
        enableTeleport: false,
        onClickCancel: closeDialog,
        onClickClose: closeDialog,
        onClickAdd(items: VideoItemInfo[]) {
          resolve(items)
          closeDialog()
        }
      }), dom)
    })
  }

  //筛选出符合要求的视频
  //视频≤4G，音频≤500M，时长3小时内
  function checkFilterFiles(files: CheckFileItem[]) {
    interface MapResult {
      enable: boolean,
      reason?: AddFileErrorMsg,
      self: CheckFileItem,
    }

    //过滤为self+错误信息
    const list = files.map(file => {
      const type = supportFile.getMediaType(file.ext)
      const map = {
        audio: {
          enable: file.size < 500,
          reason: AddFileErrorMsg.sizeLimit,
        },
        video: {
          enable: file.size < 4 * 1024,
          reason: AddFileErrorMsg.sizeLimit,
        },
        undefined: {
          enable: false,
          reason: AddFileErrorMsg.formatLimit,
        },
      }

      const item: typeof map['video'] = Reflect.get(map, String(type))
      if (item.enable === false) {
        return {
          enable: item.enable,
          reason: item.reason,
          self: file,
        } satisfies MapResult
      } else {

        if (file.duration > 60 * 60 * 5) {
          return {
            enable: false,
            reason: AddFileErrorMsg.durationLimit,
            self: file,
          } satisfies MapResult
        }
      }
      if (file.existAudioTrack === false) {
        return {
          enable: false,
          reason: AddFileErrorMsg.emptyAudioTrack,
          self: file,
        } satisfies MapResult
      }

      return {
        enable: item.enable,
        reason: item.reason,
        self: file,
      } satisfies MapResult
    })

    const enableList = list.filter(i => i.enable === true)

    return {
      list: enableList.map(i => i.self),
      reason: enableList.length === 0 ? list[0].reason : undefined,
    }

  }

  //弹窗创建转文字任务并展示进度
  function showCreateSpeechToTextProgressDialog(params: RealStartFileItemType, freeDuration: number, deviceId: string): Promise<{
    taskId: string
  }> {
    return new Promise(async (resolve, reject) => {
      const dom = document.createElement('div')
      document.body.appendChild(dom)
      const progressRef = ref(0)
      render(h(StartUploadDialog, {
        progress: progressRef,
      }, {
        default: () => t('speechExtractText')
      }), dom)
      try {
        SpeechToTextTrack.realStartConvert({
          filesize: params.filesize.toFixed(2),
          format: params.format,
          time: params.time.toFixed(),
          upload_method: params.from,
        }).then()
        const {taskId} = await createToTextTask({
          uniqid: params.uniqid,
          from: params.from,
          freeDuration: freeDuration,
          deviceId: deviceId,
          onProgress(progress) {
            progressRef.value = progress
          }
        })
        resolve({
          taskId,
        })
      } catch (e) {
        reject(e)
        console.error(e)
      }
      dom.remove()
    })
  }

  function showStartUploadDialog(files: CheckFileItem[]): Promise<RealStartFileItemType[]> {
    //只有本地文件需要上传
    files = files.filter(file => file.from !== 'cloud')
    return new Promise(async (resolve, reject) => {
      const dom = document.createElement('div')
      document.body.appendChild(dom)

      try {
        const progressRef = ref(0)
        render(h(StartUploadDialog, {
          progress: progressRef,
        }, {
          default: () => t('speechUploading')
        }), dom)
        const progressList = new Array(files.length).fill(0)

        const uploadFile = isLoginRef.value === false ? UploadApi.uploadFileWithNoToken : UploadApi.uploadFileToRecCloud
        const resultList: UploadResultType[] = await Promise.all(files.map((file, i) => {
          return uploadFile.call(UploadApi, file.self as File, {
            onProgress(progress) {
              progressList[i] = progress
              const sum = progressList.reduce((a, b) => a + b) / files.length
              progressRef.value = Number.parseInt(String(sum * 100))
            }
          })
        }))

        await Promise.all(resultList.map(i => {
          return putVideoInfo(i.uniqid!, i.filename!, {
            isOpen: isLoginRef.value === false,
          })
        }))

        const data = resultList.map((item, index) => {
          return {
            uniqid: item.uniqid!,
            filesize: parseFloat((item.size! / 1024 / 1024).toFixed(2)),
            format: files[index].ext,
            time: parseInt((item.duration! / 1000).toString())!,
            from: files[index].from,
            filename: item.filename ?? files[index].filename,
          } satisfies RealStartFileItemType
        })
        resolve(data)
      } catch (e) {
        reject(e)
      } finally {
        dom.remove()
      }
    })
  }

  function showBusinessDialog(): Promise<void> {
    SpeechToTextTrack.proVipLimitDialog().then()
    return new Promise((resolve, reject) => {
      showDialog(t('speechBatchProcess'), {
        okText: t('speechUpgradeVip'),
        cancelText: t('speechLogIn'),
        cancelBtnClass: `!border-primary-color !text-primary-color hover:!bg-transparent ${isLoginRef.value === true ? '!hidden' : ''}`,
        onClickClose(close) {
          close()
          reject('close')
        },
        async onClickCancel(close) {
          close()
          await doLogin()
          resolve()
        },
        onClickOk(close) {
          open(getVipPageUrl('speech-to-text-online-drag-multiple-file', {tab: PriceTabType.tab1}), '_blank')
          reject('open-vip')
          close()
        },
      })
    })
  }

  /**
   * 处理成统一对象
   */
  async function fileToCheckFileItem(files: File[], from: CheckFileItem['from']): Promise<CheckFileItem[]> {
    const list = [] as CheckFileItem[]
    for (const file of files) {
      const duration = await AudioUtil.getMediaDuration(file)
      const ext = file.name.split('.').pop() || ''

      //只有部分格式需要检查音轨, 不检查的就当有音轨
      let existAudioTrack = true
      if (supportFile.checkAbleExt.includes(ext)) {
        existAudioTrack = await AudioUtil.checkHasAudioTrack(URL.createObjectURL(file))
      }

      list.push({
        ext: ext as string,
        size: file.size / 1024 / 1024,
        from: from,
        self: file,
        duration: duration,
        filename: file.name,
        existAudioTrack: existAudioTrack,
      })
    }
    return list
  }

  async function createToTextTask(options: CreateTaskApiParams) {
    let taskId = ''

    try {
      taskId = await createSpeechToTextTaskApi({
        uniqid: options.uniqid,
        freeDuration: options.freeDuration,
        deviceId: options.deviceId,
      })
      setTrackByTaskId(taskId)

      const instance = new SingleTaskUtil({
        async api() {
          const {items: [data]} = await querySpeechToTextTaskApi([taskId])
          return data
        },
      })
      const data = await instance.startQuery({
        onProgress(progress) {
          options.onProgress?.(progress)
        },
      })

      SpeechToTextTrack.convertResult({
        result: 'success',
        taskId: taskId,
        time: data.duration,
        completeDuration: getTaskCompleteTotalDuration(taskId),
        upload_method: options.from,
        text: data.content.substring(0, 1000),
        text_count: data.content.length,
      }).then()

      showAiSatisfyDialog(true)

      return {
        taskId: taskId,
      }
    } catch (e) {

      let errorStatus = ''

      if (e instanceof CreateTaskError) {
        const s = e.payload?.status
        if (s) {
          errorStatus = s.toString()
        }
      } else if (e instanceof QueryTaskError) {
        const s = e.payload?.state.toString()
        if (s) {
          errorStatus = s.toString()
        }
      }

      if (e instanceof Error && e.message === 'point_limit') {
        showPointNotEnoughDialog('single-file-to-text-task')
      } else {
        SpeechToTextTrack.convertResult({
          result: 'fail',
          reason: TaskErrorMsg.safeToJSON(e),
          message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
          status: errorStatus,
          taskId: taskId,
          completeDuration: getTaskCompleteTotalDuration(taskId)
        }).then()
      }

      throw e
    }
  }

  function showFailedDialog() {
    return new Promise(async (resolve, reject) => {
      const handleClickClose = () => {
        dom.remove()
        resolve(false)
      }
      const handleClickCancel = () => {
        dom.remove()
        resolve(false)
      }
      const handleClickRetry = () => {
        dom.remove()
        resolve(true)
      }
      const dom = document.createElement('div')
      document.body.appendChild(dom)
      render(h(FailedDialog, {
        title: t('speechAudioFailedRetry'),
        okText: t('speechAudioRetry'),
        cancelText: t('speechAudioCancel'),
        onClickClose: handleClickClose,
        onClickCancel: handleClickCancel,
        onClickRetry: handleClickRetry,
      }), dom)
    })
  }

  interface FailedDialogOptions {
    title: string,
    cancelText: string,
    okText: string,
  }

  function showFailedDialogWithOptions(options: FailedDialogOptions) {
    return new Promise(async (resolve, reject) => {
      const handleClickClose = () => {
        dom.remove()
        resolve(false)
      }
      const handleClickCancel = () => {
        dom.remove()
        resolve(false)
      }
      const handleClickRetry = () => {
        dom.remove()
        resolve(true)
      }
      const dom = document.createElement('div')
      document.body.appendChild(dom)
      render(h(FailedDialog, {
        title: options.title,
        okText: options.okText,
        cancelText: options.cancelText,
        onClickClose: handleClickClose,
        onClickCancel: handleClickCancel,
        onClickRetry: handleClickRetry,
      }), dom)
    })
  }

  function showErrorDialog(reason?: AddFileErrorMsg): Promise<'ok' | 'cancel' | 'close'> {
    return new Promise(resolve => {
      const onClickOk = (close: () => void) => {
        close()
        resolve('ok')
      }
      const onClickCancel = (close: () => void) => {
        close()
        resolve('cancel')
      }
      const onClickClose = (close: () => void) => {
        close()
        resolve('close')
      }

      if (reason === AddFileErrorMsg.formatLimit) {
        showDialog(t('dialogAddFormatError'), {
          subtitle: `${t('dialogTitleSupportExt')}: ${supportFile.text}`,
          subtitleClass: 'break-all',
          onClickOk,
          onClickCancel,
          onClickClose,
        })
      } else if (reason === AddFileErrorMsg.durationLimit || reason === AddFileErrorMsg.sizeLimit) {
        showDialog(t('dialogAddSizeDurationError'), {
          subtitle: t('dialogTitleFileSize').replace('#', '4G').replace('$', '500M').replace('@', '5'),
          onClickOk,
          onClickCancel,
          onClickClose,
        })
      } else if (reason === AddFileErrorMsg.emptyAudioTrack) {
        showDialog(t('dialogAddEmptyAudioTrackError'), {
          onClickOk,
          onClickCancel,
          onClickClose,
        })
      } else if (reason === AddFileErrorMsg.networkError) {
        showDialog(t('dialogAddNetworkError'), {
          okText: t('speechAudioRetry'),
          onClickOk,
          onClickCancel,
          onClickClose,
        })
      } else {
        showDialog(t('dialogAddFileError'), {
          onClickOk,
          onClickCancel,
          onClickClose,
        })
      }
    })
  }


  async function handleCountLimit() {
    const result = await LimitDialog.showCountLimitDialog()
    if (result === 'ok') {
      PricingDialog.showOnlyVipDialog({
        source: PricingDialog.source.numLimit,
      }).then()
    } else if (result === 'cancel') {
      open(getRegionPath() + "/user/", '_blank')
    }
  }

  async function handleStorageLimit() {
    const result = await LimitDialog.showStorageLimitDialog()
    if (result === 'ok') {
      if (isVipRef.value) {
        PricingDialog.showOnlyStorageDialog().then()
      } else {
        PricingDialog.showOnlyVipDialog({
          source: PricingDialog.source.storageLimit
        }).then()
      }
    } else if (result === 'cancel') {
      open(getRegionPath() + "/user/", '_blank')
    }
  }

  return {
    supportFile: readonly(supportFile),
    showMultipleFileDialog: showMultipleFileDialog,
    checkFilterFiles: checkFilterFiles,
    showStartUploadDialog: showStartUploadDialog,
    showBusinessDialog: showBusinessDialog,
    fileToCheckFileItem: fileToCheckFileItem,
    showCreateSpeechToTextProgressDialog: showCreateSpeechToTextProgressDialog,
    showFailedDialog: showFailedDialog,
    showErrorDialog: showErrorDialog,
    showFailedDialogWithOptions: showFailedDialogWithOptions,
    handleCountLimit: handleCountLimit,
    handleStorageLimit: handleStorageLimit
  }
}

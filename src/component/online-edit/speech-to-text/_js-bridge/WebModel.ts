import BasePlatformModel from '@/component/online-edit/speech-to-text/_js-bridge/BasePlatformModel';
import showDialog from '@/render/showDialog';
import {useI18n} from '@/locales';
import {copyText} from '@/utils/browser';
import {showDangerToast, showToast} from '@/render/showToast';
import showReportDialog from '@/render/showReportDialog';
import {showPointNotEnoughDialog} from '@/render/showPointDialog';
import useSingleFileOperation
  from '@/component/online-edit/speech-to-text/single-file-operation/useSingleFileOperation';

const {t} = useI18n()

class WebModel extends BasePlatformModel {

  async clickCopy(text: string) {
    await copyText(text)
    showToast(t('Copied successfully'))
    return true
  }

  //确认删除弹窗
  showDeleteConfirmDialog(): Promise<boolean> {
    return new Promise(resolve => {
      showDialog(t('001614'), {
        onClickOk(close) {
          close()
          resolve(true)
        },
        onClickClose(close) {
          close()
          resolve(false)
        }
      })
    })
  }

  //点击AI举报
  clickAiReport(): Promise<boolean> {
    return new Promise(resolve => {
      showReportDialog({
        onClickSubmit() {
          resolve(true)
        },
        onClickClose() {
          resolve(false)
        }
      })
    })
  }

  showPointNotEnoughDialog() {
    showPointNotEnoughDialog('speech-to-text-ai-chat')
    return Promise.resolve()
  }

  async checkSaveToSpace() {
    const {saveFileConnectUser} = useSingleFileOperation()
    await saveFileConnectUser()
    return true
  }

  showStreamingTip() {
    showDangerToast(t('aiLoading'))
    return Promise.resolve(true)
  }
}


const webModel = new WebModel()
export default webModel

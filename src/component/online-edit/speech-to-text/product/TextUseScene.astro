---
import meetingMinutes from "../../../../assets/images/speech-to-text/meeting-minutes.webp"
import meetingMinutesIcon from "../../../../assets/images/speech-to-text/meeting-minutes-icon.svg"
import meetingMinutesHoverIcon from "../../../../assets/images/speech-to-text/meeting-minutes-hoverIcon.svg"
import blogManuscript from "../../../../assets/images/speech-to-text/blog-manuscript.webp"
import blogManuscriptIcon from "../../../../assets/images/speech-to-text/blog-manuscript-icon.svg"
import blogManuscriptHoverIcon from "../../../../assets/images/speech-to-text/blog-manuscript-hoverIcon.svg"
import videoToText from "../../../../assets/images/speech-to-text/video-to-text.webp"
import videoToTextIcon from "../../../../assets/images/speech-to-text/video-to-text-icon.svg"
import videoToTextHoverIcon from "../../../../assets/images/speech-to-text/video-to-text-hoverIcon.svg"
import audioToText from "../../../../assets/images/speech-to-text/audio-to-text.webp"
import audioToTextIcon from "../../../../assets/images/speech-to-text/audio-to-text-icon.svg"
import audioToTextHoverIcon from "../../../../assets/images/speech-to-text/audio-to-text-hoverIcon.svg"
import interviewLectures from "../../../../assets/images/speech-to-text/interview-lectures.webp"
import interviewLecturesIcon from "../../../../assets/images/speech-to-text/interview-lectures-icon.svg"
import interviewLecturesHoverIcon from "../../../../assets/images/speech-to-text/interview-lectures-hoverIcon.svg"
import {useI18n} from "../../../../locales"

const {t} = useI18n();

const tabs = [
  {
    icon: meetingMinutesIcon,
    hoverIcon: meetingMinutesHoverIcon,
    title: t("speechMeetingSummary"),
    img: meetingMinutes,
  },
  {
    icon: blogManuscriptIcon,
    hoverIcon: blogManuscriptHoverIcon,
    title: t("speechBroadcastScript"),
    img: blogManuscript,
  },
  {
    icon: videoToTextIcon,
    hoverIcon: videoToTextHoverIcon,
    title: t("speechVideoText"),
    img: videoToText,
  },
  {
    icon: audioToTextIcon,
    hoverIcon: audioToTextHoverIcon,
    title: t("speechAudioText"),
    img: audioToText,
  },
  {
    icon: interviewLecturesIcon,
    hoverIcon: interviewLecturesHoverIcon,
    title: t("speechInterviewScript"),
    img: interviewLectures,
  },
];
---

<div class="max-w-full mx-auto textuse-scene">
  <div class="w-full flex justify-center items-center">
    {
      tabs.map((tab, index) => {
        return (
          <div class="flex flex-col justify-between items-center cursor-pointer mx-[30px] mb-[20px]">
            <div class={"tab-item flex justify-between group"}>
              <img
                src={tab.icon}
                class="normal mr-2 lg:scale-100 scale-80 group-hover:hidden"
                alt={tab.title}
              />
              <img
                src={tab.hoverIcon}
                class="hover mr-2 lg:scale-100 scale-80 group-hover:inline-block"
                alt={tab.title}
              />
              <span class="text-[18px] font-bold leading-[28px]">
                {tab.title}
              </span>
            </div>
            <div class="w-[20px] h-[4px] bg-primary-color rounded-4"/>
          </div>
        );
      })
    }
  </div>
  <div
    class="tab-imgs w-full lg:h-[576px] lg:max-w-[970px] mt-6 lg:mt-0 rounded-7.5 overflow-visible mx-auto relative"
  >
    {
      tabs.map((tab, index) => {
        return (
          <img
            loading="lazy"
            alt={tab.title}
            class={
              "absolute left-1/2 top-0 -translate-x-1/2 transition duration-800 opacity-0 img-index-" +
              index
            }
            src={tab.img}
          />
        );
      })
    }
  </div>
</div>

<script>
  let timer: any;
  let tabs = [0, 1, 2, 3, 4];
  let index: number = 0;
  const startInterval = () => {
    timer = setInterval(() => {
      index = (index + 1) % tabs.length;
      showTabImg(index);
      hightlightTab(index);
    }, 3000);
  };
  const hightlightTab = (index: number) => {
    const tabItems = document.querySelectorAll(".textuse-scene .tab-item");
    tabItems.forEach((item, tabIndex) => {
      item.classList.remove("active");
      if (tabIndex == index) {
        item.classList.add("active");
      }
    });
  };

  const showTabImg = (index: number) => {
    const imgs = document.querySelectorAll(".tab-imgs img");
    imgs.forEach((img) => {
      img.classList.remove("active");
    });
    const img = document.querySelector(".img-index-" + index);
    img?.classList.add("active");
  };

  function initEvent() {
    const tabItems = document.querySelectorAll(".textuse-scene .tab-item");
    tabItems.forEach((item, tabIndex) => {
      item.addEventListener("mouseenter", () => {
        clearInterval(timer);
        index = tabIndex;
        showTabImg(index);
        hightlightTab(index);
      });
      item.addEventListener("mouseleave", () => {
        startInterval();
      });
    });
  }

  showTabImg(index);
  hightlightTab(index);
  startInterval();
  initEvent();
</script>
<style>
  .tab-item .normal {
    display: block;
  }

  .tab-item .hover {
    display: none;
  }

  .tab-item:hover .normal {
    display: none;
  }

  .tab-item:hover .hover {
    display: inline-block;
  }

  .tab-item.active .normal {
    display: none;
  }

  .tab-item.active .hover {
    display: inline-block;
  }

  .tab-item:hover span {
    @apply text-primary-color;
  }

  .tab-item.active span {
    @apply text-primary-color;
  }

  .tab-imgs img.active {
    opacity: 1;
  }

  .tab-imgs img {
    border: 8px solid rgba(255, 255, 255, 1);
    filter: drop-shadow(0px 27px 55px rgba(43, 135, 52, 0.2));
    border-radius: 30px;
  }

  @media screen and (max-width: 1024px) {
    .tab-imgs {
      height: calc((100vw - 3rem) * 603 / 972);
    }
  }
</style>

<script setup lang="ts">
import FileSummary from './file-operation/FileSummary.vue'
import FileAiChat from './file-operation/FileAiChat.vue'
import useSingleFileOperation from '../useSingleFileOperation'

const {fileOperationInfo, translateFileInfo} = useSingleFileOperation()

function handleRegenerateQuestionList(list: string[]) {
  translateFileInfo.questionList = list
}
</script>

<template>
  <div
    class="size-full rounded-5 bg-white/[0.7] backdrop-blur-[6px] shadow-[0px_10px_20px_0px_rgba(30,61,36,0.08)] flex-center flex-col overflow-hidden"
  >
    <div class="w-full flex flex-col flex-1 scroll-bar overflow-y-auto mt-[30px]" id="ai-chat-scroll-container-id">
      <FileSummary/>
      <FileAiChat
        class="flex-1"
        :task-id="fileOperationInfo.taskId"
        :question-list="translateFileInfo.questionList??[]"
        :on-regenerate-question-list="handleRegenerateQuestionList"
      />
    </div>

    <!--用于输入框的container这里功能特殊 其他功能请参照chat-video-->
    <div id="chat-input-container-id" class="w-full"></div>
  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import TheAudio from '@/component/base/media/TheAudio.vue'
import useSingleFileOperation from '../useSingleFileOperation'
import {showDangerToast, showToast} from '@/render/showToast'
import useLoginService from '@/services/useLoginService'
import SpeechToTextTrack from '@/track/SpeechToTextTrack'
import {useI18n} from '@/locales'
import EditorIcon from '~icons/speech-to-text/edit-icon.svg'

import CopyIcon from '~icons/speech-to-text/copy-icon.svg'
import OkIcon from '~icons/speech-to-text/ok-icon.svg'
import {copyText} from '@/utils/browser'
import {nextTick, reactive, ref} from 'vue'
import {Directive} from '@vue/runtime-core';
import SearchReplaceDialog
  from '@/component/online-edit/speech-to-text/single-file-operation/main-file-operation/file-preview/SearchReplaceDialog.vue';
import FindReplaceIcon
  from "@/component/online-edit/speech-to-text/single-file-operation/main-file-operation/file-preview/FindReplaceIcon.vue"
import useSearchReplace
  from '@/component/online-edit/speech-to-text/single-file-operation/main-file-operation/file-preview/uesSearchReplace';
import {useEventListener} from '@vueuse/core';
import {FileContentType} from '@/interface/SpeechToText';

const {t} = useI18n()

const {isLoginRef} = useLoginService()

const {
  translateFileInfo,
  tabActiveInfo,
  audioElRef,
  isTransLoadingRef
} = useSingleFileOperation()
const {searchInfo} = useSearchReplace()

const handleUpdateFileParagraph = (e: Event) => {
  translateFileInfo.content = (e.target as HTMLTextAreaElement).value
  if (tabActiveInfo.activeTab === FileContentType.polishContent) {
    translateFileInfo.polish_content = translateFileInfo.content
  } else if (tabActiveInfo.activeTab === FileContentType.originContent) {
    translateFileInfo.origin_content = translateFileInfo.content
  }
}
const handlePutFileParagraph = async () => {
  if (info.enableEdit) {
    info.disableEdit()
    SpeechToTextTrack.clickEdit().then()
    const isSuccess = tabActiveInfo.activeTab === FileContentType.polishContent ? await translateFileInfo.saveTranslateFileInfo() : await translateFileInfo.saveOriginFileInfo()
    if (isLoginRef.value) {
      if (isSuccess) {
        showToast(t('speechSaveSuccess'))
      } else {
        showDangerToast(t('speechSaveFailed'))
      }
    }
  }
}

function handleClickCopyOriginText() {
  SpeechToTextTrack.clickCopyBtn({source: 'original'})
  copyText(translateFileInfo.content)
  showToast(t('Copied successfully'))
}

const textareaElRef = ref<HTMLTextAreaElement>()
const contentContainerRef = ref<HTMLDivElement>()

function handleClickContentTab(tab: FileContentType) {
  tabActiveInfo.setActiveTab(tab)
  translateFileInfo.content = tab === FileContentType.polishContent ? translateFileInfo.polish_content : translateFileInfo.origin_content
}


const info = reactive({
  enableEdit: false,
  enableToggleSearchReplace: false,
  openEdit() {
    info.enableEdit = true
    textareaElRef.value?.focus()
  },
  disableEdit() {
    info.enableEdit = false
  },
  openToggleSearchReplace() {
    info.enableToggleSearchReplace = true
  },
  closeToggleSearchReplace() {
    searchInfo.clearSearch()
    info.enableToggleSearchReplace = false
  },
})

function handleDisableEdit() {
  info.disableEdit()
}

function handleOpenEdit() {
  if (isTransLoadingRef.value) {
    return
  }
  info.openEdit()
}

const vTextAreaUpdate: Directive = {
  updated() {
    setTextAreaHeight()
  }
}

// textArea文本域高度
const setTextAreaHeight = () => {
  const textarea = textareaElRef.value!;
  const scrollTop = contentContainerRef.value?.scrollTop
  textarea.style.minHeight = 'auto';
  nextTick(() => {
    const height = textarea.scrollHeight;
    textarea.style.height = height + 'px';
    textarea.style.minHeight = height + 'px';
    // 查找替换不记录滚动
    if (scrollTop != null && searchInfo._searchIndex !== 0) {
      contentContainerRef.value!.scrollTop = scrollTop
    }
  })

}

useEventListener('keydown', (ev: KeyboardEvent) => {
  if (ev.key.toUpperCase() === 'F' && (ev.ctrlKey || ev.metaKey)) {
    ev.preventDefault()
    info.openToggleSearchReplace()
  }
  //点击esc, 关闭查找弹窗
  if (ev.key.toUpperCase() === 'Escape'.toUpperCase()) {
    info.closeToggleSearchReplace()
    info.disableEdit()
  }
})

</script>

<template>
  <div
    class="size-full rounded-5 bg-white/[0.7] backdrop-blur-[6px] shadow-[0px_10px_20px_0px_rgba(30,61,36,0.08)] flex-center flex-col"
  >
    <div
      ref="contentContainerRef"
      class="size-full relative flex-1 flex flex-col my-[30px] px-[20px] overflow-y-scroll scroll-bar"
    >
      <div class="flex items-center justify-between px-[10px] sticky top-0 bg-[#fdfdfd] pb-[12px]">
        <div class="flex-center gap-[16px] *:cursor-pointer">
          <h5
            :class="[tabActiveInfo.activeTab===FileContentType.polishContent?'text-theme text-[18px]':'dark:text-white/[0.5] text-dark text-[16px]']"
            @click="handleClickContentTab(FileContentType.polishContent)"
            class="font-bold"
          >
            {{ t('speechAiPolish') }}
          </h5>
          <h5
            :class="[tabActiveInfo.activeTab===FileContentType.originContent?'text-theme text-[18px]':'dark:text-white/[0.5] text-dark text-[16px]']"
            @click="handleClickContentTab(FileContentType.originContent)"
            class="font-bold"
          >
            {{ t('speechOriginText') }}
          </h5>
        </div>

        <span class="flex-center gap-[20px] *:cursor-pointer">
          <template v-if="!info.enableEdit">
            <div class="relative speech-search-replace">
              <div
                class="hover:text-theme relative group"
                :class="[isTransLoadingRef?'pointer-events-none opacity-30':'']"
                @click="info.openToggleSearchReplace"
              >
                <FindReplaceIcon/>
                <div
                  v-if="!info.enableToggleSearchReplace"
                  class="hidden group-hover:block absolute top-[100%] left-[50%] -translate-x-1/2 text-[12px] bg-white text-[rgba(30,61,36,.6)] px-[14px] py-[4px] rounded-[12px] whitespace-nowrap shadow-[0px_10px_10px_rgba(30,61,36,.1)]">
                  {{ t('speechNewFindReplace') }}
                </div>
              </div>
              <SearchReplaceDialog
                v-if="info.enableToggleSearchReplace"
                @clickClose="info.closeToggleSearchReplace"
              />
            </div>
            <div class="group relative">
              <EditorIcon
                class="text-[#2D2D33] group-hover:text-primary-color flex-center"
                :class="[isTransLoadingRef?'pointer-events-none opacity-30':'']"
                @click="handleOpenEdit"
              />
              <div
                class="hidden group-hover:block absolute top-[100%] left-[50%] -translate-x-1/2 text-[12px] bg-white text-[rgba(30,61,36,.6)] px-[14px] py-[4px] rounded-[12px] whitespace-nowrap shadow-[0px_10px_10px_rgba(30,61,36,.1)]">
                {{ t('001584') }}
              </div>
            </div>
            <div class="group relative">
              <CopyIcon class="text-[#2D2D33] group-hover:text-primary-color" @click="handleClickCopyOriginText"/>
              <div
                class="hidden group-hover:block absolute top-[100%] left-[50%] -translate-x-1/2 text-[12px] bg-white text-[rgba(30,61,36,.6)] px-[14px] py-[4px] rounded-[12px] whitespace-nowrap shadow-[0px_10px_10px_rgba(30,61,36,.1)]">
                {{ t('speechCopy') }}
              </div>
            </div>
          </template>

          <template v-else>
            <OkIcon @click="handleDisableEdit"/>
          </template>
        </span>

      </div>
      <textarea
        v-text-area-update
        v-show="!info.enableToggleSearchReplace"
        ref="textareaElRef"
        class="size-full p-[10px] rounded-[10px] resize-none border border-primary-color overflow-hidden bg-transparent
         read-only:border-transparent placeholder:text-[#2D2D33]/[0.4] transition"
        :readonly="!info.enableEdit"
        :placeholder="t('speechAiPolish')"
        :value="translateFileInfo.content"
        maxlength="30000"
        @dblclick="handleOpenEdit"
        @input="handleUpdateFileParagraph"
        @blur="handlePutFileParagraph"
      ></textarea>
      <div
        v-show="info.enableToggleSearchReplace"
        id="speech-content-container-id"
        class="[&_mark]:bg-transparent [&_mark.active]:text-theme [&_mark.active]:font-bold size-full whitespace-pre-line p-[10px] rounded-[10px] resize-none border border-primary-color bg-transparent
         read-only:border-transparent placeholder:text-[#2D2D33]/[0.4] transition"
        v-html="searchInfo.highlightSearchWord(translateFileInfo.content)"
      >
      </div>

    </div>
    <div class="w-full px-[30px] py-9 border-t-1 border-[#EEEEEE]">
      <TheAudio
        v-if="translateFileInfo.url"
        ref="audioElRef"
        :src="translateFileInfo.url"
        :key="translateFileInfo.url"
        :duration="translateFileInfo.duration"
        :enable-keyboard="true"
        :showAdjustSpeed="true"
        class="w-full"
      />
    </div>
  </div>
</template>

<style scoped>

</style>

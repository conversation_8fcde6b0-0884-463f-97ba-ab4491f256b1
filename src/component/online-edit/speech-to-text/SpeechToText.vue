<script setup lang="ts">

import BackBtn from '@/component/online-edit/BackBtn.vue'
import {useI18n} from '@/locales'
import {computed, onMounted, ref} from 'vue'
import useSpeechToText from '@/component/online-edit/speech-to-text/useSpeechToText'
import {PreviewStatus} from '@/interface/SpeechToText'
import StartSelectFile from '@/component/online-edit/speech-to-text/StartSelectFile.vue'
import SingleFileOperation from '@/component/online-edit/speech-to-text/single-file-operation/SingleFileOperation.vue'
import HistoryList from '@/component/online-edit/speech-to-text/history-list/HistoryList.vue'
import SingleFileHeader from '@/component/online-edit/speech-to-text/single-file-operation/SingleFileHeader.vue';
import useLoginService from '@/services/useLoginService';
import bgGreen from "@/assets/images/online-edit/bg-green.svg"
import bgLine from "@/assets/images/online-edit/bg-line.svg"
import SpeechToTextTrack from '@/track/SpeechToTextTrack';
import searchIcon from '@images/ai-subtitle/search-icon-bg.svg';
import useHistoryList from '@/component/online-edit/speech-to-text/history-list/useHistoryList';
import {debounce} from '@/utils/debounce';
import NoVipCacheClearNotice from '@/component/global/NoVipCacheClearNotice.vue';

const {t} = useI18n()
const {previewStatusInfo} = useSpeechToText()
const {isLoginRef, doLogin} = useLoginService()
const {historyListInfo} = useHistoryList();
const searchInputElRef = ref<HTMLInputElement>()

onMounted(() => {
  const hasQuery = (v: string | null | undefined) => {
    return !!v && !!Reflect.get(PreviewStatus, v)
  }
  //一进页面需要如果地址栏没有v参数, 需要主动设置一次
  const searchMap = new URL(location.toString()).searchParams
  const v = searchMap.get('v')

  //如果地址栏带有task-id, 直接跳转到详情
  if (searchMap.get('task-id')) {
    previewStatusInfo.toSingleFileOperation(searchMap.get('task-id')!, {writeMode: 'replace'})
    return
  }

  if (hasQuery(v) === false) {
    previewStatusInfo.toPage(PreviewStatus.startSelectFile, {writeMode: 'replace'})
  } else {
    previewStatusInfo.toPage(v as PreviewStatus, {writeMode: 'replace'})
  }
})

const PreviewRef = computed(() => {
  if (previewStatusInfo.status === PreviewStatus.startSelectFile) {
    return StartSelectFile
  } else if (previewStatusInfo.status === PreviewStatus.singleFileOperation) {
    return SingleFileOperation
  } else if (previewStatusInfo.status === PreviewStatus.historyList) {
    return HistoryList
  }
})

const optionsBtnDetailRef = computed(() => {
  if (previewStatusInfo.status === PreviewStatus.startSelectFile) {
    return {
      title: t('speechToHistory'),
      class: 'border border-deep-color/[0.5] text-[#2D2D33] text-[14px] font-semibold',
      async click() {
        SpeechToTextTrack.clickViewList().then()
        if (isLoginRef.value === false) {
          await doLogin()
        }
        previewStatusInfo.toHistoryList()
      }
    }
  } else if (previewStatusInfo.status === PreviewStatus.historyList) {
    return {
      title: t('speechNewCreate'),
      class: 'bg-primary-color text-white',
      click() {
        SpeechToTextTrack.clickToStartSelectFileBtn()
        previewStatusInfo.toStartSelectFile()
      }
    }
  }

})

function handleClickBack() {
  history.back()
}
const enableSearchRef = ref(true)

const doSearch = debounce((value) => {
  if (!enableSearchRef.value) return
  historyListInfo.searchWord = value
  historyListInfo.fetchList()
}, 300)

</script>

<template>
  <div class="size-full bg-[#FAFAFA] pt-header-offset flex flex-col overflow-hidden">

    <div class="absolute right-0 top-0 z-0">
      <img :src="bgGreen" alt="bg icon" draggable="false">
    </div>
    <div class="absolute top-0 left-1/2 -translate-x-1/2 z-0">
      <img :src="bgLine" alt="bg icon" draggable="false">
    </div>

    <NoVipCacheClearNotice
      v-if="previewStatusInfo.status===PreviewStatus.historyList"
      class="relative z-10"
    />

    <SingleFileHeader v-if="previewStatusInfo.status===PreviewStatus.singleFileOperation"/>
    <div class="px-[60px] mt-[34px] flex items-center z-10" v-else>

      <BackBtn @click="handleClickBack">
        <span>{{ t('001293') }}</span>
      </BackBtn>

      <div class="ml-auto">
        <form
          v-show="previewStatusInfo.status === PreviewStatus.historyList"
          @submit.prevent="historyListInfo.fetchList"
          class="w-[268px] h-[32px] mr-[15px] rounded-[100px] flex-center bg-white text-[14px] has-[:focus]:outline-primary-color outline outline-1 outline-transparent"
        >
          <input
            ref="searchInputElRef"
            type="text"
            class="w-full h-full bg-transparent pl-[20px] pr-[14px]"
            :placeholder="t('historyListSearchText')"
            :value="historyListInfo.searchWord"
            @compositionstart="enableSearchRef = false"
            @compositionend="enableSearchRef = true"
            @input="(ev)=>doSearch((ev.target as HTMLInputElement).value)"
          />
          <button class="pr-[4px]">
            <img class="size-[26px] block object-cover" :src="searchIcon" alt="">
          </button>
        </form>
      </div>

      <button
        class="p-[6px_30px] rounded-[32px] text-[14px] hover:translate-y-[4px] transition duration-500"
        :class="[optionsBtnDetailRef?.class]"
        @click="optionsBtnDetailRef?.click"
      >
        {{ optionsBtnDetailRef?.title }}
      </button>
    </div>

    <div
      class="w-full h-full z-10"
      :class="[previewStatusInfo.status===PreviewStatus.historyList?'':'px-[60px] py-[40px]']"
    >
      <Component :is="PreviewRef"></Component>
    </div>
  </div>
</template>

<style scoped>

</style>

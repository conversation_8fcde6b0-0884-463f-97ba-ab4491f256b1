<script setup lang="ts">

import backIcon from '@images/online-screen-recorder/recordscreenonline/back.svg';
import backHoverIcon from '@images/online-screen-recorder/recordscreenonline/back-hover.svg';

</script>

<template>
  <div class="flex w-fit items-center group cursor-pointer text-deep-color hover:translate-y-[4px] duration-400 transition hover:text-primary-color">
    <img class="w-[32px] h-[22px] block group-hover:hidden" :src="backIcon" alt="">
    <img class="w-[32px] h-[22px] hidden group-hover:block" :src="backHoverIcon" alt="">
    <span class="ml-[12px]">
      <slot></slot>
    </span>
  </div>
</template>

---
import AppleIcon from '~icons/home/<USER>'
import GoogleIcon from '~icons/home/<USER>'
import RobotIcon from '~icons/home/<USER>'
import {getMobileClientDownloadUrl} from '../../services/constants';
import {getLang} from '@central/i18n';
import {useI18n} from '../../locales';
const {class: classname} = Astro.props
const lang = getLang()

const androidDownloadUrl = `${getMobileClientDownloadUrl()}&os=android` + (lang === 'zh' ? '' : '&lang=en')
const {t} = useI18n()
---
<div
  class='flex-center lg:hidden h-[60px] text-white dark:text-black bg-dark dark:bg-white rounded-[12px]'
  class:list={[classname]}
>
  <a
    class="hidden apple-download-container"
    href={getMobileClientDownloadUrl() + `&os=ios`}
    rel="nofollow"
    target="_blank"
  >
    <div class="flex-center gap-[8px] h-[60px] px-[30px]">
      <AppleIcon/>
      <div class="flex flex-col">
        <span class=`text-[12px] [&:lang(jp)]:order-1 ${[['zh', 'tw'].includes(lang) ? 'hidden' : '']}`>
          {t('iosTitle')}
        </span>
        <span class=`text-[16px] ${[['zh', 'tw'].includes(lang) ? '' : 'font-bold']}`>
          {t('iosDownload')}
        </span>
      </div>
    </div>
  </a>
  <a
    class="hidden android-download-container"
    href={androidDownloadUrl}
    rel="nofollow"
    target="_blank"
  >
    <div class="flex-center gap-[8px] h-[60px] px-[40px]">
      {
        lang === 'zh' ? (
          <RobotIcon/>
        ) : (
          <GoogleIcon/>
        )
      }
      <div class="flex flex-col">
        <span class=`text-[12px] [&:lang(jp)]:order-1 ${[['zh', 'tw'].includes(lang) ? 'hidden' : '']}`>
          {t('androidTitle')}
        </span>
        <span class=`text-[16px] ${[['zh', 'tw'].includes(lang) ? '' : 'font-bold']}`>
          {t('androidDownload')}
        </span>
      </div>
    </div>
  </a>

</div>
<script>
  import ClientInfo from '../../utils/ClientInfo';
  import TrackUtil from '../../track/_TrackUtil';

  init()

  function init() {
    const appleDownloadEl = document.querySelectorAll('.apple-download-container');
    const androidDownloadEl = document.querySelectorAll('.android-download-container');

    function handleClickDownload() {
      TrackUtil.trackButtonClick('click_download_app');
    }

    function setupDownloadButton(elements: NodeListOf<Element>) {
      elements.forEach(el => {
        el.classList.remove('hidden');
        el.addEventListener('click', handleClickDownload);
      });
    }

    // 根据设备类型显示对应下载按钮
    setupDownloadButton(ClientInfo.isIOS ? appleDownloadEl : androidDownloadEl);
  }
</script>


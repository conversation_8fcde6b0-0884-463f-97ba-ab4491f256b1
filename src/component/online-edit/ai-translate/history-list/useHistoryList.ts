import {reactive} from 'vue';
import historyTaskQuery from '@/component/online-edit/ai-translate/history-list/HistoryTaskQuery';
import {
  deleteAiTranslateTaskApi,
  getHistoryList<PERSON>pi,
  retryAiTranslateTask<PERSON>pi,
  updateAiTranslateTaskTitle<PERSON>pi, updateOldDetail<PERSON>pi, updateOldHistory<PERSON><PERSON>
} from '@/api/aiTranslateApi'
import {AiTranslateTaskItemType} from '@/interface/AiTranslate';
import AiTranslateTrack from '@/track/AiTranslateTrack';
import {DeleteRecordType} from '@/interface/OnlineEdit';
import showDialog from '@/render/showDialog';
import {useI18n} from '@/locales';

const {t} = useI18n()


const historyListInfo = reactive({
  list: [] as AiTranslateTaskItemType[],
  _page: 1,
  searchWord: '',
  fetchState: 'fetching' as 'fetching' | 'empty' | 'hasData',
  total: 0,
  async _rawFetchList(page: number, searchWord: string) {
    const result = await getHistoryList<PERSON><PERSON>(page, searchWord)

    const getLoadingTaskIds = (list: AiTranslateTaskItemType[]) => {
      return Array.from(list)
        .filter(item => checkItemStatus(item.state) === 'loading')
        .map(item => item.task_id)
    }

    // 过滤出所有需要轮询的taskId(状态处于loading中的)
    historyTaskQuery.addTaskByIds(getLoadingTaskIds(result.list))
    historyTaskQuery.onProgressChange = models => {
      models.forEach(async (model) => {
          const itemIndex = historyListInfo.list.findIndex(item => item.task_id === model.task_id)
          if (itemIndex !== -1) {
            const item = historyListInfo.list[itemIndex]
            historyListInfo.list[itemIndex] = {
              ...item,
              ...model
            }
            if (checkItemStatus(model.state) === 'success') {
              AiTranslateTrack.convertResult({
                result: 'success',
                title: item.title,
                taskId: item.task_id,
                duration: item.duration,
                source: 'history-list',
              }).then()
              const detail = await updateOldDetailApi(model.task_id)
              if (detail) {
                historyListInfo.list[itemIndex] = {
                  ...item,
                  title: detail.title,
                  duration: detail.duration,
                  progress: detail.progress,
                  state: detail.state,
                  uniqid: detail.uniqid,
                  version: detail.version,
                  speaker_identification: detail.speaker_identification,
                }
              }

            } else if (checkItemStatus(model.state) === 'fail') {
              AiTranslateTrack.convertResult({
                result: 'fail',
                source: 'history-list',
                reason: JSON.stringify(model),
              }).then()
            }

          }
        }
      )
    }
    return result
  },
  async fetchList() {
    historyListInfo.fetchState = 'fetching'
    historyListInfo._page = 1
    const {list, count} = await historyListInfo._rawFetchList(historyListInfo._page, historyListInfo.searchWord)
    historyListInfo.list = list
    historyListInfo.total = count
    if (list.length === 0) {
      historyListInfo.fetchState = 'empty'
    } else {
      historyListInfo.fetchState = 'hasData'
    }
  },
  async loadMore() {
    historyListInfo._page++
    const {list} = await historyListInfo._rawFetchList(historyListInfo._page, historyListInfo.searchWord)
    historyListInfo.list.push(...list)
  },
  async deleteItemById(taskIds: string[], type: DeleteRecordType) {
    const success = await deleteAiTranslateTaskApi(taskIds, type)
    if (success) {

      historyListInfo.list = historyListInfo.list.filter(item => !taskIds.includes(item.task_id))
      historyListInfo.total -= taskIds.length

      if (historyListInfo.list.length === 0) {
        historyListInfo.fetchState = 'empty'
      } else {
        historyListInfo.fetchList()
      }
    }
    return success
  },
  async retryItemByTaskId(taskId: string) {
    const success = await retryAiTranslateTaskApi(taskId)
    if (success) {
      historyListInfo._updateTaskStatus(taskId, {state: 0})
    }
  },
  async updateOldHistoryByTaskId(taskId: string) {
    const success = await updateOldHistoryApi(taskId)
    if (success) {
      historyListInfo._updateTaskStatus(taskId, {
        progress: 0,
        state: 0
      })
    }
  },
  // 抽取更新任务状态的公共方法
  _updateTaskStatus(taskId: string, updates: Partial<AiTranslateTaskItemType>) {
    const index = historyListInfo.list.findIndex(item => item.task_id === taskId)
    if (index !== -1) {
      historyListInfo.list[index] = {
        ...historyListInfo.list[index],
        ...updates
      }
      // 添加到轮询队列
      historyTaskQuery.addTaskByIds([taskId])
    }
  },
  async updateTitleByTaskId(title: string, index: number) {
    const item = historyListInfo.list[index]
    item.title = title
    await updateAiTranslateTaskTitleApi(item.task_id, title)
  },
  clearSearchWord() {
    historyListInfo.searchWord = ''
  },
  resetInfo() {
    historyListInfo.list = []
    historyListInfo._page = 1
    historyTaskQuery.stopQuery()
  }
})

function checkItemStatus(state: number) {
  if (state < 0) {
    //失败状态
    return 'fail'
  } else if (state === 1) {
    return 'success'
  } else {
    return 'loading'
  }
}

async function showHistoryUpdateDialog(): Promise<'ok' | 'cancel' | 'close'> {
  return new Promise((resolve) => {
    showDialog('', {
      subtitle: t('aiTranslateKey43'),
      subtitleClass: 'whitespace-pre-line text-[16px] leading-[20px] !text-dark mt-0',
      okText: t('aiTranslateKey44'),
      cancelBtnClass: '!border-primary-color !text-primary-color hover:!text-white',
      cancelText: t('002606'),
      onClickOk(close) {
        close()
        resolve('ok')
      },
      onClickCancel(close) {
        //点击试用
        close()
        resolve('cancel')
      },
      onClickClose(close) {
        close()
        resolve('close')
      },
    })
  })
}


export default function useHistoryList() {

  //格式化时长 单位秒 格式化为 分:秒
  function formatDuration(duration: number) {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration - hours * 3600) / 60)
    const seconds = duration % 60
    const hoursText = hours > 0 ? `${String(hours).padStart(2, '0')}:` : ''

    return `${hoursText}${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
  }

  return {
    historyListInfo: historyListInfo,
    formatDuration,
    checkItemStatus,
    showHistoryUpdateDialog
  }
}

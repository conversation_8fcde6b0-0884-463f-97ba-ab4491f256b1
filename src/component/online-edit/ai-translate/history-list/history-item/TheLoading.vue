<script lang="ts" setup>
import loadingImg from '@/assets/images/online-edit/loading.svg'

defineProps<{
  progress: number
  imgClass?: string
}>()

</script>
<template>
  <div class=" w-[300px] text-center mx-auto">
    <div class="loading-bg relative flex justify-center items-center flex-col">
      <img class="loading-animation" :class="imgClass" :src="loadingImg" alt="loading icon" draggable="false">
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex justify-center items-baseline text-[#fff]">
        <span class="text-[14px]">{{ progress }}</span>
        <span class="text-[14px]">%</span>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<style scoped>

.loading-animation {
  animation: loading-keyframes 2s ease-in-out infinite alternate;
}

@keyframes loading-keyframes {
  0% {
    transform: rotate(60deg);
  }
  25% {
    transform: rotate(100deg);
  }
  50% {
    transform: rotate(300deg);
  }
  75% {
    transform: rotate(100deg);
  }
  100% {
    transform: rotate(60deg);
  }
}
</style>

---
import StartLogo from '~icons/home/<USER>'
import {useI18n} from "@/locales";
import MobileDownloadAppBtn from '@/component/online-edit/MobileDownloadAppBtn.vue';
import {getLang} from '@central/i18n';
import {getCdnUrl} from '@/utils/util';
import {Picture} from '@central/assets/imagetools';
import CacheVideo from '@/component/global/CacheVideo.vue';
const {t} = useI18n();
const lang = getLang()
const videoUrl = lang === 'zh' ? getCdnUrl('/video/reccloud/ai-subtitle-translator/first-video-zh.mp4') : getCdnUrl('/video/reccloud/ai-subtitle-translator/first-video-com.mp4')
---

<section class="relative pt-[40px] lg:pt-[56px] lg:pb-[100px] lg:h-90vh mt-header-offset overflow-hidden bg-black">
  <div class="relative size-full z-10">
    <div class="flex-center size-full flex-col lg:flex-row">
      <!--文字-->
      <div class="w-90% lg:w-34% max-w-[520px] lg:animation-left relative z-10">
        <div
          class="blue-gradient-circle-light rotate-40 w-[264px] h-[264px] lg:w-[524px] lg:h-[524px] rounded-full blur-[130px] lg:blur-[470px] absolute top-60% -left-1/3 lg:-top-1/4 lg:-left-1/4 z-11"
        ></div>
        <h1
          class="relative z-12 text-[28px] lg:text-[52px] text-center lg:text-start text-white"
          set:html={t('subtitleTranslatorKey1').replace('<i>', '<i class="gradient-text line-tilde not-italic relative whitespace-nowrap">')}
        >
        </h1>

        <div
          class="relative z-12 text-[14px] lg:text-16px text-center lg:text-start mt-[16px] lg:mt-[36px] mb-[24px] lg:mb-[48px] lg:leading-[28px] text-white/80">
          {t('subtitleTranslatorKey2')}
        </div>
        <button
          class="hidden lg:flex-center relative z-12 try-now-btn w-max mt-[24px] lg:mt-[50px] min-w-[270px] h-[64px] gradient-button from-[#76F9B1] via-[#D6FB72] to-[#76F9B1] text-[22px] text-black font-semibold px-[20px]"
        >
          <span class="flex whitespace-nowrap">
            {t("subtitleTranslatorKey3")}
            <i class="self-start">
              <StartLogo class="!block"/>
            </i>
          </span>
        </button>
        <div class="block lg:hidden mt-[24px] w-max mx-auto relative z-12">
          <MobileDownloadAppBtn class="mx-auto" client:only="vue"/>
        </div>
        <div
          class="block lg:hidden yellow-gradient-circle-light rotate-60 w-[226px] h-[226px] rounded-full blur-[140px] absolute -bottom-1/3 -right-1/4"
        ></div>
      </div>

      <!--图片-->
      <div class="hidden lg:block w-90% lg:max-w-[48vw] 2xl:max-w-[44vw] 2xl:[&:lang(zh)]:max-w-[42vw]">
        <CacheVideo
          client:load
          class="lg:absolute lg:-top-[56px] lg:right-0 lg:[&:lang(zh)]:-right-[2vw] lg:h-90vh"
          poster={videoUrl}
          preload="metadata"
          src={videoUrl} autoplay loop muted playsinline
        />
      </div>
      <div class="block lg:hidden w-full mt-[40px]">
        <Picture
          src={getLang() === 'zh' ? 'src/assets/images/ai-subtitle-translator/mobile-cn-subtitle-translator.png' : 'src/assets/images/ai-subtitle-translator/mobile-com-subtitle-translator.png'}
          alt="first-background"
          loading="eager"
          layout="fill"
          quality={65}
        />
      </div>
    </div>
  </div>


</section>

<style>
  .blue-gradient-circle-light {
    background: radial-gradient(
            217.68% 217.68% at 27.97% 49.05%,
            rgba(90, 233, 253, 0.6) 50%,
            rgba(90, 233, 253, 0) 100%
    )
  }

  .yellow-gradient-circle-light {
    background: radial-gradient(65.67% 65.67% at 41.2% 34.33%, #FFD84B 100%, rgba(255, 216, 75, 0) 100%)
  }
</style>

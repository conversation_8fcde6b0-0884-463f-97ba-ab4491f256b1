---
import SpeechToTextIcon from '~icons/ai-translate/ai-tools-icon/speech-to-text.svg';
import TextToSpeechIcon from '~icons/ai-translate/ai-tools-icon/text-to-speech.svg';
import AiSubtitleIcon from '~icons/ai-translate/ai-tools-icon/ai-subtitle.svg';
import ChatVideoIcon from '~icons/ai-translate/ai-tools-icon/chat-video.svg';
import ExtractAudioIcon from '~icons/ai-translate/ai-tools-icon/extract-audio.svg';
import TextToVideoIcon from '~icons/ai-translate/ai-tools-icon/text-to-video.svg';
import {getRegionPath} from '@/utils/util';
import {useI18n} from '@/locales';
import {Picture} from '@central/assets/imagetools';
import {getLang} from '@central/i18n';
const {t} = useI18n()
const lang = getLang();

const aiVideoList = [
  {
    icon: SpeechToTextIcon,
    title: t('onlineSpeechToText'),
    description: t('speechToTextAi'),
    href: getRegionPath() + '/speech-to-text-online',
  },
  {
    icon: TextToVideoIcon,
    title: t('aiTranslateToolsVideoGenerate'),
    description: t('aiTranslateToolsVideoGenerateDesc'),
    href: getRegionPath() + '/text-to-video',
  },
  {
    icon: AiSubtitleIcon,
    title: t('headerAiSubtitleTitle'),
    description: t('handleFileItemDesc3'),
    href: getRegionPath() + '/ai-subtitle',
  },
  {
    icon: ChatVideoIcon,
    title: t('chatVideoTitle'),
    description: t('handleFileItemDesc2'),
    href: getRegionPath() + '/chat-video',
  },
  {
    icon: TextToSpeechIcon,
    title: lang === 'en' ? t('headerToolsVoiceTitle') : t('aiTextToSpeech'),
    description: t('aiTextToSpeechDesc'),
    href: getRegionPath() + '/text-to-speech-online',
  },
  {
    icon: ExtractAudioIcon,
    title: t('ExtractAudio'),
    description: t('itemDesc5'),
    href: getRegionPath() + '/extract-audio-online',
  },
]
---
<div class="relative z-10 mt-[40px] lg:mt-[3.6vw]">
  <ul
    class="grid grid-rows-[204px_204px] lg:grid-rows-[254px_254px] grid-cols-[91.2vw] lg:grid-cols-[372px_372px_372px] justify-center gap-[20px] lg:gap-[42px]">
    {
      aiVideoList.map((item, index) => (
        <li
          class="relative group bg-[rgba(185,236,255,0.12)] rounded-[12px] border-[1px] border-transparent hover:border-white/20 border-solid p-[12px] lg:p-[30px] transition"
        >
          <a href={item.href} class="w-full h-full">
            <div class="hidden group-hover:block absolute inset-0 object-cover">
              <Picture
                src='src/assets/images/ai-translate/translate-tools-hover.png'
                alt="background"
                layout="fill"
              />
            </div>
            <div class="relative z-10">
              <item.icon class="size-[48px] lg:scale-100 scale-85"/>
              <h3 class="text-[16px] lg:text-[20px] font-medium mt-[8px] lg:mt-[16px]">{item.title}</h3>
              <p class="text-[14px] lg:text-[16px] mt-[8px] text-white/60">{item.description}</p>
              <button
                class="min-w-[120px] mt-[24px] lg:mt-[30px] w-fit h-[38px] bg-white/20 text-[14px] text-white rounded-[24px]
                flex-center group-hover:bg-gradient-to-r from-[#D6FB72] to-[#76F9B1] group-hover:text-[#232A26] transition"
              >
                {t('textAiOptionsBtnText')}
              </button>
            </div>
          </a>
        </li>
      ))
    }
  </ul>
</div>

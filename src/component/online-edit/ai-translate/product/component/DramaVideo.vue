<script setup lang="ts">
import {ref} from 'vue';
import {getCdnUrl} from '@/utils/util';
import CacheVideo from '@/component/global/CacheVideo.vue';

const isShowVideoRef = ref<boolean>(false)
const currentVideoIndexRef = ref<number>(0)
const imgList = [
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_1_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_1_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_1_en.mp4'),
    originDesc: '中文',
    transDesc: '英文'
  },
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_2_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_2_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_2_ja.mp4'),
    originDesc: '中文',
    transDesc: '日语'
  },
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_3_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_3_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_3_pt.mp4'),
    originDesc: '中文',
    transDesc: '葡萄牙语'
  },
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_4_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_4_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_4_es.mp4'),
    originDesc: '中文',
    transDesc: '西班牙语'
  },
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_5_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_5_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_5_th.mp4'),
    originDesc: '中文',
    transDesc: '泰语'
  },
  {
    imgUrl: getCdnUrl('/video/reccloud/ai-drama-translation/poster_6_img.webp'),
    originVideo: getCdnUrl('/video/reccloud/ai-drama-translation/origin_6_zh.mp4'),
    transVideo: getCdnUrl('/video/reccloud/ai-drama-translation/translate_6_id.mp4'),
    originDesc: '中文',
    transDesc: '印尼语'
  },
]

const mobileListRef = ref<typeof imgList[0]>()
const currentMobileTabRef = ref<'originDesc' | 'transDesc'>('transDesc')

const handleClickVideoItem = (item: typeof imgList[0]) => {
  currentVideoIndexRef.value = imgList.findIndex(i => i === item)
  currentMobileTabRef.value = 'transDesc'
  mobileListRef.value = item
  isShowVideoRef.value = true
}
const handleClickCloseVideoItem = () => {
  isShowVideoRef.value = false
}
const handleClickMobile = (item: 'originDesc' | 'transDesc') => {
  document.querySelectorAll('video').forEach(video => {
    video.pause()
  })
  currentMobileTabRef.value = item
}
</script>

<template>
  <div
    class="w-[90%] lg:w-[100%] mx-auto overflow-auto 2xl:overflow-hidden [&::-webkit-scrollbar-thumb]:cursor-pointer [&::-webkit-scrollbar-thumb]:bg-[#FFF]/40 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar]:h-[4px] [@supports_(-moz-appearance:none)]:[scrollbar-width:none]">
    <ul
      class="w-max lg:w-unset xl:w-max mx-auto grid grid-cols-6 p-[50px_0px_24px] lg:mx-auto lg:px-[10px] xl:px-[50px] lg:py-[30px] gap-[30px]">
      <li
        v-for="item in imgList"
        class="relative w-max border-[2px] cursor-pointer border-[rgba(51,51,51,1)] rounded-[30px] overflow-hidden hover:scale-110 transition-all duration-300"
        @click="handleClickVideoItem(item)"
      >
        <img :src="item.imgUrl" alt="video" class="w-[230px] h-[410px]">
        <div class="absolute left-[10px] top-[10px] py-[4px] px-[14px] text-white bg-black/80 rounded-[20px]">
          {{ item.transDesc }}
        </div>
      </li>
    </ul>
    <Teleport to="body">
      <div
        v-if="isShowVideoRef"
        class="fixed top-0 left-0 w-100vw h-100vh backdrop-blur-[20px] flex z-1000"
      >
        <div class="relative mx-auto flex-center h-auto w-full max-w-[1200px] flex-shrink-0 flex-col">
          <div class="hidden lg:flex w-fit max-w-full gap-[30px] 2xl:gap-[50px]">
            <div class="relative max-w-[24vw] border-[rgba(51,51,51,1)] border-[2px] rounded-[20px] overflow-hidden">
              <CacheVideo
                class="size-full"
                :src="imgList[currentVideoIndexRef].originVideo" loop playsinline controls
                controlslist="nodownload noplaybackrate" disablePictureInPicture
              ></CacheVideo>
              <span
                class="absolute text-[20px] left-[30px] top-[30px] py-[6px] px-[24px] text-white bg-black/80 rounded-[20px]"
              >
                {{ imgList[currentVideoIndexRef].originDesc }}
              </span>
            </div>
            <div class="relative max-w-[24vw] border-[rgba(51,51,51,1)] border-[2px] rounded-[20px] overflow-hidden">
              <CacheVideo
                class="size-full"
                :src="imgList[currentVideoIndexRef].transVideo" autoplay loop playsinline controls
                controlslist="nodownload noplaybackrate" disablePictureInPicture
              ></CacheVideo>
              <span
                class="absolute text-[20px] left-[30px] top-[30px] py-[6px] px-[24px] bg-gradient-to-r from-[#D6FB72] to-[#76F9B1] text-dark bg-black/80 rounded-[20px]"
              >
                {{ imgList[currentVideoIndexRef].transDesc }}
              </span>
            </div>
          </div>
          <div class="flex lg:hidden w-fit max-w-full">
            <div class="relative max-w-[60vw] border-[rgba(51,51,51,1)] border-[2px] rounded-[20px] overflow-hidden">
              <CacheVideo
                class="size-full"
                loading="lazy"
                :src="currentMobileTabRef==='originDesc'?mobileListRef!.originVideo:mobileListRef!.transVideo"
                autoplay loop playsinline controls
                controlslist="nodownload noplaybackrate" disablePictureInPicture
              ></CacheVideo>
              <div
                class="absolute flex-center text-[20px] left-[10px] top-[10px] text-white bg-[rgba(24,22,30,.4)] rounded-[20px]"
              >
                <div
                  class="px-[20px] py-[6px] text-[14px] rounded-full"
                  :class="[currentMobileTabRef==='originDesc'?'bg-black':'']"
                  @click.stop="handleClickMobile('originDesc')"
                >
                  {{ mobileListRef!.originDesc }}
                </div>
                <div
                  class="px-[20px] py-[6px] text-[14px] rounded-full"
                  :class="[currentMobileTabRef==='transDesc'?'bg-gradient-to-r from-[#D6FB72] to-[#76F9B1] text-dark':'']"
                  @click.stop="handleClickMobile('transDesc')"
                >
                  {{ mobileListRef!.transDesc }}
                </div>

              </div>
            </div>
          </div>

          <div class="mt-[5.6vh] text-[16px] 2xl:text-[20px] text-white">版权归内容方所有</div>
          <div
            class="absolute top-[20vh] lg:top-[10vh] right-[20px] lg:right-0 text-white hover:text-theme cursor-pointer"
            @click.stop="handleClickCloseVideoItem"
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.6">
                <path d="M19 19L0.999999 1.48649" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <path d="M1 19L18.5135 1" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </g>
            </svg>

          </div>
        </div>
      </div>
    </Teleport>
  </div>

</template>

<style scoped>

</style>

---
import EnglishIcon from "@/assets/images/ai-translate/national-flag/english.png"
import ChineseIcon from "@/assets/images/ai-translate/national-flag/chinese.png"
import FrenchIcon from "@/assets/images/ai-translate/national-flag/french.png"
import GermanIcon from "@/assets/images/ai-translate/national-flag/german.png"
import JapaneseIcon from "@/assets/images/ai-translate/national-flag/japanese.png"
import BrazilIcon from "@/assets/images/ai-translate/national-flag/Brazil.png"
import PortugueseIcon from "@/assets/images/ai-translate/national-flag/portuguese.png"
import ItalianIcon from "@/assets/images/ai-translate/national-flag/italian.png"
import SpanishIcon from "@/assets/images/ai-translate/national-flag/spanish.png"
import ArabicIcon from "@/assets/images/ai-translate/national-flag/arabic.png"
import HindiIcon from "@/assets/images/ai-translate/national-flag/hindi.png"
import KoreanIcon from "@/assets/images/ai-translate/national-flag/korean.png"
import DutchIcon from "@/assets/images/ai-translate/national-flag/dutch.png"
import RussianIcon from "@/assets/images/ai-translate/national-flag/russian.png"
import ThaiIcon from "@/assets/images/ai-translate/national-flag/thai.png"
import VietnameseIcon from "@/assets/images/ai-translate/national-flag/vietnamese.png"
import MalaysianIcon from "@/assets/images/ai-translate/national-flag/malaysian.png"
import {useI18n} from "@/locales";
import {getLang} from '@central/i18n';
const {t} = useI18n();
const lang = getLang()
const list = [
  {
    title: t("aiTranslateEnglish"),
    icon: EnglishIcon,
  },
  {
    title: t("aiTranslateChinese"),
    icon: ChineseIcon,
  },
  {
    title: t("aiTranslateFrench"),
    icon: FrenchIcon,
  },
  {
    title: t("aiTranslateGerman"),
    icon: GermanIcon,
  },
  {
    title: t("aiTranslateJapanese"),
    icon: JapaneseIcon,
  },
  {
    title: t("aiTranslatePortuguese"),
    icon: lang === 'pt' ? BrazilIcon : PortugueseIcon,
  },
  {
    title: t("aiTranslateItalian"),
    icon: ItalianIcon,
  },
  {
    title: t("aiTranslateSpanish"),
    icon: SpanishIcon,
  },
  {
    title: t("aiTranslateArabic"),
    icon: ArabicIcon,
  },
  {
    title: t("aiTranslateHindi"),
    icon: HindiIcon,
  },
  {
    title: t("aiTranslateKorean"),
    icon: KoreanIcon,
  },
  {
    title: t("aiTranslateDutch"),
    icon: DutchIcon,
  },
  {
    title: t("aiTranslateRussian"),
    icon: RussianIcon,
  },
  {
    title: t("aiTranslateThai"),
    icon: ThaiIcon,
  },
  {
    title: t("aiTranslateVietnamese"),
    icon: VietnameseIcon,
  },
  {
    title: t("aiTranslateMalaysian"),
    icon: MalaysianIcon,
  },
];
---

<div class="relative pt-[30px] lg:pt-[60px]">
  <ul
    class="relative grid mx-auto grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-[5.3vw] lg:gap-[30px] text-white"
  >
    {
      list.map((item) => {
        return (
          <li
            class="w-[42.6vw] h-[58px] lg:w-[310px] lg:h-[70px] flex items-center gap-[10px] lg:gap-[16px] bg-white/10 border-[1px] border-solid border-white/20 shadow-[0px_20px_40px_0px_rgba(30,61,36,0.05)] rounded-[12px] overflow-hidden"
          >
            <img src={item.icon} alt="icon" class="size-[26px] lg:size-[40px] ml-[6.4vw] lg:ml-[50px]"/>
            <div class="text-[14px] lg:text-[20px]">
              {item.title}
            </div>
          </li>
        );
      })
    }
  </ul>
</div>

<style lang="scss">
  @media screen and (min-width: 1024px) {
    .feat-list {
      padding: 40px;
    }
  }

  @media screen and (max-width: 1023px) {
    .feat-list {
      pointer-events: none;
    }
  }

  @media screen and (max-width: 639px) {
    .feat-list {
      width: 100%;
      justify-content: center;
      align-items: flex-start;
      gap: 18px;
      grid-template-columns: repeat(3, 96px);
      grid-template-rows: repeat(3, 96px);

      li {
        transform: scale(0.5);
        transform-origin: left top;
      }

      .desc {
        font-size: 12px;
      }
    }
  }

  @media screen and (max-width: 424px) {
    .feat-list {
      width: calc(100px * 3 + 18px * 2) !important;
      justify-content: center !important;

      li {
        transform-origin: left top;
      }
    }
  }

  @media screen and (max-width: 425px) {
    .flex-item-three {
      flex: 0 0 calc((100% - 40px) / 3);
    }
  }
</style>

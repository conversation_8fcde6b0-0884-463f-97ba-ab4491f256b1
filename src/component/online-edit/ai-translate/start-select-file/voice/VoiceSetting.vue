<script setup lang="ts">

import {computed, reactive, ref, watch} from 'vue';
import {VoiceItemType} from '@/interface/AiTranslate';
import videoPauseIcon from '@images/text-to-speech/video-paused.svg';
import videoPlayIcon from '@images/text-to-speech/video-play.svg';
import VoiceListItem from '@/component/online-edit/text-to-speech-v2/settings/voice/VoiceListItem.vue';
import SpeedSetting from '@/component/online-edit/text-to-speech-v2/multi/views/setting/SpeedSetting.vue';
import VolumeSetting from '@/component/online-edit/text-to-speech-v2/multi/views/setting/VolumeSetting.vue';
import {useElementVisibility} from '@vueuse/core';
import {useI18n} from '@/locales';
import TheHeartBox from '@/component/base/checkbox/TheHeartBox.vue';
import {updateVoiceFavoriteApi} from '@/api/textToSpeechApi';
import {IsFavoriteType} from '@/interface/TextToSpeech';
import useLoginService from '@/services/useLoginService';
import AiTranslateTrack from '@/track/AiTranslateTrack';

const {t} = useI18n()
const {isLoginRef, doLogin} = useLoginService()


const props = defineProps<{
  //选中的音色
  voice: string,
  speed: number,
  volume: number,
  categoryList: string[],
  defaultVoiceList: VoiceItemType[],
  fetchVoiceListByCategory: (category: string) => Promise<VoiceItemType[]>,
  disableAdjustSpeed?: boolean,
  disableAdjustVolume?: boolean,
}>()

const emits = defineEmits<{
  updateSpeed: [speed: number],
  updateVolume: [volume: number],
  updateVoice: [voice: string],
  startTryListenAudio: [],
}>()

const info = reactive({
  categoryIndex: 0,
  voiceList: [] as VoiceItemType[],
  setCategoryIndex(index: number) {
    info.categoryIndex = index
    updateList()
  },
  handleClickItem(item: VoiceItemType, index: number) {
    emits('updateVoice', item.voice)
  }
})

//控制音色试听列表
const _voiceListTryListenInfo = reactive({
  uniqid: null as any,
  audioEl: null as HTMLAudioElement | null,
  _stopWatch: null as Function | null,

  resetAudioEl() {
    this.audioEl?.remove()
    this.audioEl = null
    this.uniqid = ''
    this._stopWatch?.()
  },
  handleClickTogglePlay(id: any, url: string) {
    if (this.uniqid === id) {
      this.resetAudioEl()
    } else {
      this.resetAudioEl()
      this.audioEl = new Audio(url)
      this.audioEl.play()
      emits('startTryListenAudio')

      this._stopWatch = watch([() => props.volume, () => props.speed], ([v, s]) => {
        if (this.audioEl) {
          this.audioEl.volume = v / 2 / 100
          this.audioEl.playbackRate = s
        }
      }, {immediate: true})

      this.audioEl.onplay = () => {
        this.uniqid = id
      }
      this.audioEl.onended = () => {
        this.resetAudioEl()
      }
      // this.audioEl.onpause = () => {
      //   this.resetAudioEl()
      // }
      document.body.appendChild(this.audioEl)
    }
  }
})

const listRef = computed(() => {
  if (info.voiceList.length > 0) {
    return info.voiceList
  }
  return props.defaultVoiceList
})

const containerElRef = ref<HTMLElement>()

const isVisibilityRef = useElementVisibility(containerElRef)

function handleUpdateCategory(index: number, ev: Event) {
  info.setCategoryIndex(index)
  const el = ev.target as HTMLElement
  el.scrollIntoView({behavior: 'smooth', inline: 'center', block: 'nearest'})
}

watch(isVisibilityRef, () => {
  if (isVisibilityRef.value === false) {
    _voiceListTryListenInfo.resetAudioEl()
  }
  if (isVisibilityRef.value === true) {
    updateList().then(() => {
      containerElRef.value?.querySelector('.active')?.scrollIntoView({
        block: 'center',
      })
    })
  }
})

async function updateList() {
  const list = await props.fetchVoiceListByCategory(props.categoryList[info.categoryIndex])
  info.voiceList = list
}

async function handleClickFavorite(item: typeof listRef.value[number]) {
  if (isLoginRef.value === false) {
    await doLogin()
  }
  const success = await updateVoiceFavoriteApi(item.id as number)
  if (success === true) {
    if (item.favorite === IsFavoriteType.noFavorite) {
      AiTranslateTrack.clickLikeVoice(item.voice).then()
      item.favorite = IsFavoriteType.favorite
    } else {
      AiTranslateTrack.clickUnlikeVoice(item.voice).then()
      item.favorite = IsFavoriteType.noFavorite
    }
    updateList().then()
  }
}
</script>

<template>
  <div
    ref="containerElRef"
    class="rounded-[20px] w-[442px] bg-white shadow-[0_10px_20px_0_rgba(30,61,36,0.08)] z-10 py-[14px] 2xl:py-[30px]"
  >
    <slot name="top-area"/>
    <!--分类列表-->
    <div class="px-[30px]">
      <ul class="flex items-center gap-[20px] overflow-x-auto scroll-bar-none">
        <li
          class="text-nowrap whitespace-nowrap hover:text-theme transition"
          v-for="(item,index) in categoryList"
          :class="[index===info.categoryIndex?'text-[16px] font-semibold text-theme':'text-[14px]']"
          @click="handleUpdateCategory(index,$event)"
        >
          <button>{{ item }}</button>
        </li>
      </ul>
    </div>

    <!--音色列表-->
    <div class="flex flex-col">
      <ul
        class="pl-[30px] pr-[26px] mr-[4px] mt-[10px] 2xl:mt-[20px] grid grid-cols-3 gap-[10px] 2xl:gap-[20px] h-[200px] overflow-y-auto scroll-bar">
        <VoiceListItem
          v-for="(item,index) in listRef"
          :key="item.voice_tag"
          class="group/setting h-[80px] border rounded-[8px] flex items-center cursor-pointer flex-col gap-[4px] hover:text-theme hover:border-theme py-[12px] relative border-[#EEE]"
          :class="[item.voice===props.voice?'text-theme border-theme active':'']"
          :title="item.voice_tag"
          :data-voice="item.voice"
          :enableVipIcon="false"
          voiceType="normal"
          @click="info.handleClickItem(item,index)"
        >
          <template #avatar>
            <div class="size-[28px] *:rounded-full relative">
              <img class="block" :src="item.icon" draggable="false" alt="avatar">

              <div
                class="absolute inset-0 w-full h-full bg-black/[0.3] hidden group-hover/setting:flex-center"
                :class="[_voiceListTryListenInfo.uniqid===item.voice?'!flex-center':'']"
                @click.stop="_voiceListTryListenInfo.handleClickTogglePlay(item.voice,item.url)"
              >
                <img
                  class="size-[12px]"
                  :src="_voiceListTryListenInfo.uniqid===item.voice?videoPauseIcon:videoPlayIcon"
                  draggable="false"
                  alt="avatar">
              </div>

            </div>
          </template>
          <template #right-top>
            <TheHeartBox
              :class="{'!block':item.favorite===IsFavoriteType.favorite}"
              class="hidden group-hover/setting:block"
              :is-active="item.favorite===IsFavoriteType.favorite"
              @click.stop="handleClickFavorite(item)"
            />
          </template>

        </VoiceListItem>
      </ul>

      <div
        class="px-[30px] mt-[24px] text-[14px] w-full flex-col gap-[20px_16px] grid grid-cols-[auto_1fr]"
        :class="[disableAdjustSpeed||disableAdjustVolume?'grid-rows-1':'grid-rows-2']"
      >
        <span :class="[disableAdjustSpeed?'hidden':'']">{{ t('textSpeed') }}</span>
        <!-- 语速设置 -->
        <SpeedSetting
          :class="[disableAdjustSpeed?'!hidden':'']"
          :speed="speed"
          @updateSpeed="(speed)=>$emit('updateSpeed',speed)"
        />
        <span :class="[disableAdjustVolume?'hidden':'']">{{ t('textVolume') }}</span>
        <!-- 音量选项 -->
        <VolumeSetting
          :class="[disableAdjustVolume?'!hidden':'']"
          :volume="volume"
          @updateVolume="(volume)=>$emit('updateVolume',volume)"
        />
      </div>

    </div>

    <slot name="bottom-area"></slot>
  </div>
</template>

<style scoped>

</style>

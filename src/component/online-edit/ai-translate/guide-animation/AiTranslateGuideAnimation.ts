import {driver, DriveStep} from 'driver.js'
import "driver.js/dist/driver.css"
import {useI18n} from '@/locales'
import './guide-style.css'

const {t} = useI18n()

interface OptionsType {
  nextButtonClassList?: string[],
  steps?: DriveStep[],
  popoverOffset?: number,
  stagePadding?: number,
}

class AiTranslateGuideAnimation {

  private static _buildDriver(options: OptionsType) {
    return driver({
      overlayColor: 'blank',
      overlayOpacity: 0.6,
      stageRadius: 8,
      popoverOffset: options.popoverOffset ?? 80,
      allowClose: false,
      stagePadding: options?.stagePadding ?? 10,
      showButtons: ['next'],
      steps: options.steps,
      onHighlightStarted() {
        document.querySelector<HTMLElement>('#start-ai-translate-task-id button')
          ?.style.setProperty('box-shadow', 'none')

        document.querySelector<HTMLElement>('#start-ai-translate-task-tag')
          ?.style.setProperty('opacity', '0')
      },
      onDeselected() {
        document.querySelector<HTMLElement>('#start-ai-translate-task-id button')
          ?.style.removeProperty('box-shadow')

        //tag会被引导动画裁剪, 这里先隐藏在显示
        const tagEl = document.querySelector<HTMLElement>('#start-ai-translate-task-tag')
        tagEl?.style.setProperty('opacity', '1')
        requestAnimationFrame(() => {
          tagEl?.style.removeProperty('opacity')
        })

      },
    })
  }


  public static startAiTranslateAnimation() {
    const key = '_ai-translate-guide-animation'
    const desc = `
      <div class="text-[14px] text-dark mt-[8px]">
        <div class="text-[16px] whitespace-nowrap">${t('aiTranslateGuideAnimationTitle')}</div>
        <div class="mt-[16px] flex flex-col gap-[8px]">
          <div class="flex items-center justify-between whitespace-nowrap">
            <p>${t('aiTranslateGuideAnimationDesc1')}</p>
            <span class="flex-center text-[rgba(45,45,51,0.6)]">
              ${t('aiTranslateGuideAnimationDesc3')}
              <svg width="17" height="14" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.7302 0C11.9365 8.57777e-06 12.1403 0.045393 12.3272 0.132938C12.514 0.220483 12.6793 0.348047 12.8114 0.506591L12.8662 0.576951L15.7496 4.52179C15.9327 4.7723 16.028 5.07634 16.0204 5.38658C16.0129 5.69682 15.903 5.99586 15.7078 6.23716L15.6483 6.30612L8.97206 13.5466C8.84523 13.6842 8.69224 13.7951 8.52203 13.8728C8.35182 13.9506 8.16782 13.9936 7.98079 13.9993C7.79377 14.0051 7.60746 13.9735 7.43279 13.9064C7.25812 13.8393 7.09859 13.738 6.96352 13.6085L6.90161 13.5447L0.371277 6.44121C0.163426 6.21513 0.0352388 5.92726 0.00627424 5.62152C-0.0226903 5.31579 0.0491579 5.00897 0.210857 4.74788L0.261047 4.67283L3.16644 0.591023C3.28764 0.420758 3.44524 0.279627 3.62779 0.177882C3.81035 0.0761367 4.01328 0.0163329 4.22184 0.0028144L4.3133 0H11.7302Z" fill="url(#paint0_linear_38576_14136)"/>
                <defs>
                <linearGradient id="paint0_linear_38576_14136" x1="3.28346" y1="0.547243" x2="10.9449" y2="10.9449" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FFEC88"/>
                <stop offset="1" stop-color="#FFAA46"/>
                </linearGradient>
                </defs>
               </svg>
               3
            </span>
          </div>
          <div class="flex items-center justify-between whitespace-nowrap">
            <p>${t('aiTranslateGuideAnimationDesc2')}</p>
            <span class="flex-center ml-[8px] text-[rgba(45,45,51,0.6)]">
              ${t('aiTranslateGuideAnimationDesc3')}
              <svg width="17" height="14" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.7302 0C11.9365 8.57777e-06 12.1403 0.045393 12.3272 0.132938C12.514 0.220483 12.6793 0.348047 12.8114 0.506591L12.8662 0.576951L15.7496 4.52179C15.9327 4.7723 16.028 5.07634 16.0204 5.38658C16.0129 5.69682 15.903 5.99586 15.7078 6.23716L15.6483 6.30612L8.97206 13.5466C8.84523 13.6842 8.69224 13.7951 8.52203 13.8728C8.35182 13.9506 8.16782 13.9936 7.98079 13.9993C7.79377 14.0051 7.60746 13.9735 7.43279 13.9064C7.25812 13.8393 7.09859 13.738 6.96352 13.6085L6.90161 13.5447L0.371277 6.44121C0.163426 6.21513 0.0352388 5.92726 0.00627424 5.62152C-0.0226903 5.31579 0.0491579 5.00897 0.210857 4.74788L0.261047 4.67283L3.16644 0.591023C3.28764 0.420758 3.44524 0.279627 3.62779 0.177882C3.81035 0.0761367 4.01328 0.0163329 4.22184 0.0028144L4.3133 0H11.7302Z" fill="url(#paint0_linear_38576_14136)"/>
                <defs>
                <linearGradient id="paint0_linear_38576_14136" x1="3.28346" y1="0.547243" x2="10.9449" y2="10.9449" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FFEC88"/>
                <stop offset="1" stop-color="#FFAA46"/>
                </linearGradient>
                </defs>
               </svg>
               6
            </span>
          </div>
        </div>
      </div>
    ` as string


    function _checkEnable() {
      const exist = Boolean(localStorage.getItem(key))
      return !exist
    }


    const _doStart = () => {
      const elList = document.querySelectorAll<HTMLElement>('#start-ai-translate-task-id *')
      const _instance = this._buildDriver({
        popoverOffset: 60,
        stagePadding: 20,
        steps: [
          {
            element: '#start-ai-translate-task-id',
            popover: {
              popoverClass: 'start-ai-translate-class',
              side: 'left',
              align: 'start',
              description: desc,
              nextBtnText: t('002561'),
              onNextClick() {
                Array.from(elList).forEach(el => el.style.removeProperty('pointer-events'))
                _instance.destroy()
              }
            },
          },
        ],
      })
      //开启引导动画不允许点击按钮
      Array.from(elList).forEach(el => el.style.setProperty('pointer-events', 'none'))
      _instance.drive()
      localStorage.setItem(key, 'true')
    }

    return {
      checkEnable: _checkEnable,
      doStart: _doStart,
    }
  }
}

export default AiTranslateGuideAnimation

<script setup lang="ts">
import SubtitleIcon from '~icons/ai-translate/subtltle-style.svg'
import SpeakerIcon from '~icons/ai-translate/speaker-icon.svg'
import FindReplaceIcon from '~icons/ai-translate/subtitle-find-replace.svg'
import TranslateIcon from '~icons/ai-translate/translate-icon.svg'
import EditIcon from '~icons/ai-translate/edit.svg'
import tipIcon from '@images/ai-translate/tip-icon.svg'
import OkIcon from '~icons/ai-translate/success.svg'
import {nextTick, onBeforeUnmount, reactive, ref, watch} from 'vue';
import MultiAddIcon from '~icons/text-to-speech/multi-add.svg'
import MultiMergeIcon from '~icons/text-to-speech/multi-merge.svg'
import {isClient, useEventListener} from '@vueuse/core';
import {
  createTransTaskApi,
  fetchTransTaskEquityApi,
  queryTransTaskApi,
  saveSubtitleTextApi,
  saveTranslateSubtitleStyleApi,
  saveVoiceSettingApi
} from '@/api/aiTranslateApi';
import useTranslateDetail from '@/component/online-edit/ai-translate/detail/useTranslateDetail';
import {OnClickOutside, UseElementBounding, vElementHover} from '@vueuse/components';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import {publicPath} from '@/utils/util';
import {EquityModel, ReplaceType, RetryStatus, Speaker, SrtModel,} from '@/interface/AiTranslate';
import VideoPlayer from '@/component/online-edit/ai-translate/detail/video-player/VideoPlayer.vue';
import {debounce} from '@/utils/debounce';
import {SrtResultModel} from '@/interface/OnlineEdit';
import useAiTranslate from '@/component/online-edit/ai-translate/useAiTranslate';
import LoadingIcon from '@/component/online-edit/ai-translate/icons/LoadingIcon.vue';
import {useI18n} from '@/locales';
import SubtitleStyle from '@/component/online-edit/ai-translate/detail/subtitle-style/SubtitleStyle.vue';
import SearchReplaceDialog from '@/component/online-edit/ai-translate/detail/search-replace/SearchReplaceDialog.vue';
import useSearchReplace from '@/component/online-edit/ai-translate/detail/search-replace/uesSearchReplace';
import CreditIcon from '~icons/home/<USER>';
import AiTranslateTrack from '@/track/AiTranslateTrack';
import useTranslateSubtitleStyle
  from '@/component/online-edit/ai-translate/detail/subtitle-style/useTranslateSubtitleStyle';
import ClientInfo from '@/utils/ClientInfo';
import showAiSatisfyDialog from '@/render/showAiSatisfyDialog';
import AvatarVoice from '@/component/online-edit/ai-translate/detail/voice/AvatarVoice.vue';
import CpsText from '@/component/online-edit/ai-translate/detail/CpsText.vue';
import {showToast} from '@/render/showToast';
import {removeUrlSearchParams} from '@/utils/browser';
//@ts-ignore
import {DynamicScroller, DynamicScrollerItem} from "vue-virtual-scroller/dist/vue-virtual-scroller.esm.js"
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import SpeakerSetting from '@/component/online-edit/ai-translate/detail/speaker/SpeakerSetting.vue';
import TimeStamp from '@/component/online-edit/ai-subtitle-v2/subtitle-operation/subtitle/timestamp/TimeStamp.vue';
import CloseIcon from '@/component/online-edit/ai-translate/icons/CloseIcon.vue';
import useExportDialog from '@/component/online-edit/ai-translate/detail/export/useExportDialog';
import UpdateAudio from '@/component/online-edit/ai-translate/detail/try-audio/UpdateAudio.vue';
import {showAiTranslateDeleteConfirmDialog} from '@/render/showPointDialog';
import useLoginService from '@/services/useLoginService';
import TheLoading from '@/component/online-edit/TheLoading.vue';
import {getVipPageUrl} from '@/utils/open';

const {t} = useI18n()

const props = defineProps<{
  taskId: string
}>()

const {isLoginRef, pointCountRef} = useLoginService()
const {searchInfo} = useSearchReplace()
const {previewStatusInfo} = useAiTranslate()
const {
  detailInfo,
  subtitleInfo,
  voiceListInfo,
  seekTo,
  videoSubtitleInfo,
  scrollRef,
  videoPreviewElRef,
  showPointNotEnoughDialog
} = useTranslateDetail()
const {handleClickExport} = useExportDialog()
const {styleInfo} = useTranslateSubtitleStyle()
const equityInfo = reactive({
  info: null as EquityModel | null,
  get textTransCount(): number {
    //剩余翻译次数
    const limit = equityInfo.info?.copy_writing_translation_equity?.limit ?? 0
    const used = equityInfo.info?.copy_writing_translation_equity?.used ?? 0
    return limit - used
  },
  //翻译任务试用时长
  get tryTaskDuration(): number {
    return equityInfo.info?.translation_equity?.limit_duration ?? 0
  },
  get tryTransAudioCount(): number {
    //重新翻译前#句 剩余参数
    const limit = equityInfo.info?.copy_writing_speech_equity?.limit ?? 0
    const used = equityInfo.info?.copy_writing_speech_equity?.used ?? 0
    return limit - used
  },
  get tryTransTextCount(): number {
    //重新翻译前#句的#
    return equityInfo.info?.copy_writing_speech_equity?.free_sample_audio_quantity ?? 1
  },
  get existFreeMergeCount(): boolean {
    const limit = equityInfo.info?.merge_equity.limit ?? 0
    const used = equityInfo.info?.merge_equity.used ?? 0
    return limit > used
  },
  async fetchEquityInfo() {
    const result = await fetchTransTaskEquityApi(props.taskId)
    if (result) {
      equityInfo.info = result
    }
  }
})

watch(isLoginRef, () => {
  if (isLoginRef.value === false) {
    previewStatusInfo.toStartSelectFile({
      writeMode: 'replace'
    })
  }
})

const addTaskIdQuery = (taskId: string) => {
  const url = new URL(location.toString())
  url.searchParams.set('task-id', taskId)
  history.replaceState({}, "", url)
}

function handleOpenEditWithFocusLast(index: number, ev: Event) {
  pauseVideo()
  subtitleInfo.openEdit(index)
  const el = ev.composedPath().find((e) => e instanceof HTMLElement && e.classList.contains('group/item')) as HTMLElement
  nextTick(() => {
    el.querySelector<HTMLTextAreaElement>('textarea.trans')?.focus()
  })
}

function handleUpdateSpeaker(item: SrtModel, speaker: string, voice: string) {
  if (item.translateRestParams) {
    item.translateRestParams.speaker = speaker
    item.translateRestParams.voice = voice
    detailInfo.retryStatus = RetryStatus.allowRetry
    handleSaveSubtitle('translateText')
  }
}

const pauseVideo = () => {
  videoPreviewElRef.value?.pauseVideo()
}

watch(() => videoSubtitleInfo.activeSubtitleIndex, (index) => {
  if (index !== -1) {
    const isPause = videoPreviewElRef.value?.videoElRef?.paused
    if (isPause) {

    } else {
      scrollRef.value?.scrollToItem(index)
    }
  }
}, {
  immediate: true,
  flush: 'post',
})

const updateAudioRef = ref()

const updateAudioInfo = reactive({
  isLoading: false,
  _progress: 0,
  get progress() {
    return this._progress
  },
  set progress(val: number) {
    this._progress = val
  },
  updateLoadingState(flag: boolean) {
    if (flag === true) {
      this.progress = 0
    }
    this.isLoading = flag
  }
})

const _saveVoiceSetting = async () => {
  await saveVoiceSettingApi(props.taskId, {
    voice: detailInfo.voice,
    volume: detailInfo.volume,
    rate: detailInfo.rate,
  })
  updateAudioRef.value?.handleClickUpdate()
}

function handleSafariMousedown(ev: MouseEvent) {
  if (ClientInfo.isSafari && ev.detail > 1) {
    ev.preventDefault()
  }
}

const loadingInfo = reactive({
  isLoading: false,
  isReady: false,
})
if (isClient) {
  if (props.taskId) {
    addTaskIdQuery(props.taskId)
  }

  loadingInfo.isLoading = true
  loadingInfo.isReady = false

  Promise.all([
    equityInfo.fetchEquityInfo(),
    detailInfo.fetchDetailInfo(props.taskId).then(() => voiceListInfo.fetchAllVoice()),
  ])
    .then(() => {
      setTimeout(() => {
        loadingInfo.isReady = true
      }, 1000)
    })
    .finally(() => {
      loadingInfo.isLoading = false
      showAiSatisfyDialog()
    })
    .catch((e) => {
      showToast(t('recordNotFound'))
      previewStatusInfo.toHistoryList({
        writeMode: 'replace'
      })
    })
}

const transTextInfo = reactive({
  isHoverTipImg: false,
  //翻译中的item列表
  loadingItemList: [] as SrtModel[],
  //存储每个item对应的controller
  controllers: new Map<SrtModel, AbortController>(),
  //存储每个item的翻译状态
  statusMap: new Map<SrtModel, 'Success!' | 'Failed!' | null>(),

  // 移除loading
  removeItem(item: SrtModel) {
    this.loadingItemList = this.loadingItemList.filter(i => i !== item)
  },
  // 移除控制器controller
  deleteItem(item: SrtModel) {
    this.controllers.delete(item)
  },

  async startTransTask(item: SrtModel, controller: AbortController) {
    if (this.loadingItemList.includes(item)) {
      return
    }

    this.loadingItemList.push(item)
    this.controllers.set(item, controller)
    this.statusMap.set(item, null)

    try {
      const taskId = await createTransTaskApi(props.taskId, item.text, controller.signal)

      const instance = new SingleTaskUtil({
        api: () => queryTransTaskApi(taskId)
      })

      controller.signal.onabort = () => {
        this.removeItem(item)
        instance.stopQuery()
      }

      const result = await instance.startQuery({
        onProgress(p) {
          console.log(p)
        }
      })
      if (result.result) {
        item.translateText = result.result
        this.statusMap.set(item, 'Success!')
        handleSaveSubtitle('translateText').then()
      }
    } catch (e) {
      this.removeItem(item)
      if (e instanceof Error && (e.message === 'ManualCancel' || e.name === 'TypeError')) {
        // 用户手动取消
      } else {
        this.statusMap.set(item, 'Failed!')
      }
    } finally {
      //翻译文案, 重新获取权益次数
      equityInfo.fetchEquityInfo().then()
      this.removeItem(item)
      this.deleteItem(item)
      setTimeout(() => {
        this.statusMap.delete(item)
      }, 2000)
    }
  }
})

async function handleStartTransText(item: SrtModel, index: number) {
  subtitleInfo.closeEdit()
  AiTranslateTrack.clickTranslateSubtitle({
    model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
  }).then()
  if (equityInfo.textTransCount === 0 && pointCountRef.value <= 0) {
    const res = await showPointNotEnoughDialog(1)
    if (res === 'ok') {
      open(getVipPageUrl('translate-multi-voice-update-task'))
      return
    } else {
      return
    }
  }
  const controller = new AbortController()
  await transTextInfo.startTransTask(item, controller)
  detailInfo.retryStatus = RetryStatus.allowRetry
}

function handleCancelTransText(item: SrtModel) {
  const controller = transTextInfo.controllers.get(item)
  if (controller) {
    controller.abort()
  }
}

const handleSaveSubtitle = async (key: 'text' | 'translateText') => {
  const id = props.taskId
  let list = [] as SrtResultModel[]
  if (key === 'text') {
    list = detailInfo.subtitleList.map(i => ({
      start: i.start,
      end: i.end,
      text: i.text,
      ...i?.originRestParams
    }))
    await saveSubtitleTextApi(id, list, key)

  } else if (key === 'translateText') {
    list = detailInfo.subtitleList.map(i => ({
      start: i.start,
      end: i.end,
      text: i.translateText,
      ...i?.translateRestParams,
    }))
    await saveSubtitleTextApi(id, list, key, detailInfo.speakerList)
  }
}

onBeforeUnmount(() => {
  detailInfo.resetDetailInfo()
})

//点击合成
async function handleClickStartConvert() {
  voiceDialogInfo.close()
  findReplaceInfo.close()
  styleDialogInfo.close()
  handleClickExport(props.taskId).then()
}

const styleDialogInfo = reactive({
  enableDialog: false,
  open() {
    voiceDialogInfo.close()
    findReplaceInfo.close()
    AiTranslateTrack.clickStyle({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    })
    styleDialogInfo.enableDialog = true

  },
  close() {
    styleDialogInfo.enableDialog = false
  }
})

const findReplaceInfo = reactive({
  enableDialog: false,
  open() {
    styleDialogInfo.close()
    voiceDialogInfo.close()
    AiTranslateTrack.clickEnableSearch({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    })
    findReplaceInfo.enableDialog = true
  },
  close() {
    findReplaceInfo.enableDialog = false
  },
  handleClickClose() {
    findReplaceInfo.close()
  },
  handleSaveSingle(type: ReplaceType) {
    AiTranslateTrack.clickReplace({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    })
    if (type === 'origin') {
      handleSaveSubtitle('text')
    } else if (type === 'translate') {
      detailInfo.retryStatus = RetryStatus.allowRetry
      handleSaveSubtitle('translateText')
    }
  },
  handleSaveAll(types: ReplaceType[]) {
    AiTranslateTrack.clickReplaceAll({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    })
    if (types.includes(ReplaceType.translate)) {
      detailInfo.retryStatus = RetryStatus.allowRetry
    }

    Promise.all([handleSaveSubtitle('text'), handleSaveSubtitle('translateText')])
  }
})

const voiceDialogInfo = reactive({
  isActive: false,
  open() {
    AiTranslateTrack.clickSpeakerManagement({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    }).then()
    findReplaceInfo.close()
    styleDialogInfo.close()
    voiceDialogInfo.isActive = true
  },
  close() {
    voiceDialogInfo.isActive = false
  },
})

//当字幕修改时 保存样式
watch([styleInfo], debounce(() => {
  saveTranslateSubtitleStyleApi(props.taskId)
}))

useEventListener('keydown', (ev: KeyboardEvent) => {
  if (ev.key.toUpperCase() === 'F' && (ev.ctrlKey || ev.metaKey)) {
    ev.preventDefault()
    findReplaceInfo.open()
  }
  //点击esc, 关闭查找弹窗
  if (ev.key.toUpperCase() === 'Escape'.toUpperCase()) {
    findReplaceInfo.close()
    styleDialogInfo.close()
    voiceDialogInfo.close()
  }
})

const handleDbClickSubtitle = (index: number, ev: Event) => {
  pauseVideo()
  subtitleInfo.openEdit(index)
  //nextTick+requestAnimationFrame防止与textarea的focus行为冲突
  nextTick(() => {
    requestAnimationFrame(() => {
      const el = ev.target as HTMLElement
      const textarea = el.querySelector('textarea')
      if (textarea) {
        textarea?.focus()
      } else {
        el.parentElement?.querySelector('textarea')?.focus()
      }
    })
  })
}

//简体/繁体/日语 50字  其他150字
const inputLimitInfo = reactive({
  specialList: ['zh', 'tw', 'jp', 'ja'],
  getMaxCount(lang: string) {
    return this.specialList.includes(lang) ? 50 : 150;
  },
  getMaxCps(lang: string) {
    return this.specialList.includes(lang) ? 12 : 25;
  }
})

onBeforeUnmount(() => {
  removeUrlSearchParams('task-id')
})

function handleSaveSetting(hasChange: boolean) {
  if (detailInfo.speakTask === Speaker.disable) {
    //单音色保存
    if (hasChange) {
      _saveVoiceSetting()
    }
  } else if (detailInfo.speakTask === Speaker.enable) {
    //多音色保存
    handleSaveSubtitle('translateText')
    if (hasChange) {
      detailInfo.retryStatus = RetryStatus.allowRetry
    }
  }
}

function handleUpdateTimeStamp(item: SrtModel, time: number, type: 'start' | 'end') {
  const s = time * 1000

  const save = () => {
    detailInfo.retryStatus = RetryStatus.allowRetry
    Promise.all([
      handleSaveSubtitle('text'),
      handleSaveSubtitle('translateText')
    ])
    AiTranslateTrack.clickTimestampsEdit({
      model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
    }).then()
  }

  if (type === 'start' && Number(item.start) !== s) {
    //@ts-ignore
    item.start = s
    save()
  } else if (type === 'end' && Number(item.end) !== s) {
    //@ts-ignore
    item.end = s
    save()
  }
}

function rendTips(title: string) {
  const elId = 'subtitle-list-container-id'
  const el = document.createElement('div')
  el.className = 'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex-center text-white text-[14px] scale-0 transition'
  el.innerHTML = `
    <span class="px-[20px] py-[12px] bg-[#656565] rounded-[8px] flex-center gap-[8px]">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="7" stroke="white" stroke-width="1.5"/>
        <path d="M8 3.2998C8.3 3.2998 8.5 3.3998 8.7 3.5998C8.8 3.6998 8.9 3.7998 8.9 3.8998C9 3.9998 9 4.1998 9 4.2998C9 4.5998 8.9 4.7998 8.7 4.9998C8.4 5.2998 8 5.3998 7.6 5.1998C7.5 5.1998 7.4 5.0998 7.3 4.9998C7.1 4.7998 7 4.5998 7 4.2998C7 4.1998 7 3.9998 7.1 3.8998C7.2 3.7998 7.2 3.6998 7.3 3.5998C7.4 3.4998 7.5 3.3998 7.6 3.3998C7.7 3.2998 7.9 3.2998 8 3.2998Z" fill="white"/>
        <path d="M8 11.6996V8.09961" stroke="white" stroke-width="1.6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      ${title}
    </span>
  `
  document.querySelector(`#${elId}`)?.appendChild(el)
  setTimeout(() => el.classList.add('!scale-100'), 0)
  setTimeout(() => el.remove(), 2000)
}

async function handleDeleteSubtitle(index: number) {
  AiTranslateTrack.clickDeleteItem({
    model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
  }).then()
  const result = await showAiTranslateDeleteConfirmDialog()
  if (result === 'ok') {
    detailInfo.subtitleList.splice(index, 1)
    detailInfo.retryStatus = RetryStatus.allowRetry
    handleSaveSubtitle('text').then()
    handleSaveSubtitle('translateText').then()
    scrollRef.value?.forceUpdate()
  }
}

function handleMergeSubtitle(originIndex: number, targetIndex: number) {
  AiTranslateTrack.clickMargeItem({
    model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
  }).then()
  const originItem = detailInfo.subtitleList[originIndex]
  const targetItem = detailInfo.subtitleList[targetIndex]
  if (originItem && targetItem) {
    originItem.end = targetItem.end
    originItem.text = originItem.text + targetItem.text
    originItem.translateText = originItem.translateText + targetItem.translateText
    detailInfo.subtitleList.splice(targetIndex, 1)
    handleSaveSubtitle('text')
    handleSaveSubtitle('translateText')
    detailInfo.retryStatus = RetryStatus.allowRetry
    scrollRef.value?.forceUpdate()
  }

}

function handleAddSubtitle(index: number) {
  AiTranslateTrack.clickInsertItem({
    model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
  }).then()
  const active = detailInfo.subtitleList[index]
  //时间单位都是s
  const activeEnd = parseFloat(active.end) / 1000
  let nextStart = 0
  if (index === detailInfo.subtitleList.length - 1) {
    //点击的是最后一条字幕, 需要与时长比较
    nextStart = detailInfo.duration
  } else {
    const next = detailInfo.subtitleList[index + 1]
    nextStart = parseFloat(next.start) / 1000
  }
  if (nextStart - activeEnd < 0.1) {
    rendTips(t('aiSubtitleTimestampTips'))
  } else {
    const i = index + 1
    detailInfo.subtitleList.splice(i, 0, {
      //@ts-ignore 这里start与end,由于前期判断失误ts类型使用了string, 但实际上就是number
      start: activeEnd * 1000,
      //@ts-ignore
      end: Math.min(nextStart, activeEnd + 4) * 1000,
      text: '',
      translateText: '',
      translateRestParams: {
        ...active.translateRestParams
      },
      originRestParams: {
        ...active.originRestParams
      },
    })
    subtitleInfo.openEdit(i)
    detailInfo.retryStatus = RetryStatus.allowRetry

    nextTick(() => {
      scrollRef.value?.forceUpdate()
      setTimeout(() => {
        document.querySelector<HTMLTextAreaElement>(`#subtitle-list-container-id [data-index="${i}"] textarea `)?.focus()
      }, 700)
    })

    handleSaveSubtitle('text')
    handleSaveSubtitle('translateText')
  }
}
</script>

<template>
  <div class="w-full h-[calc(100vh-200px)] flex gap-[40px] *:rounded-[20px] relative">
    <div
      v-if="loadingInfo.isLoading===true||loadingInfo.isReady===false"
      class="size-full flex-center flex-col gap-[16px] absolute inset-0 z-10 bg-white"
    >
      <LoadingIcon class="animate-spin"/>
    </div>
    <template v-if="loadingInfo.isLoading===false">
      <div
        v-show="loadingInfo.isReady===true"
        class="relative bg-white flex-1 py-[24px] flex flex-col shadow-[0_10px_20px_0_rgba(30,61,36,0.08)]"
      >
        <div class="*:cursor-pointer px-[24px] flex items-center gap-[30px] h-[32px] relative">
          <UseElementBounding
            class="flex items-center relative w-max hover:text-theme"
            v-slot="{left,top,height}"
            @click="findReplaceInfo.open"
          >
            <FindReplaceIcon/>
            <span class="ml-[4px]">{{ t('aiSubtitleFind') }}</span>
            <Teleport to="body">
              <SearchReplaceDialog
                v-if="findReplaceInfo.enableDialog"
                class="text-dark"
                :task-id="taskId"
                :start-x="left"
                :start-y="top+height"
                @click.stop
                @clickClose="findReplaceInfo.handleClickClose"
                @replaceSingle="findReplaceInfo.handleSaveSingle"
                @replaceAll="findReplaceInfo.handleSaveAll"
              />
            </Teleport>

          </UseElementBounding>

          <UseElementBounding
            class="flex items-center relative hover:text-theme"
            v-slot="{left,top,width,height}"
          >
            <div class="flex-center" @click="styleDialogInfo.open">
              <SubtitleIcon/>
              <span class="ml-[4px]">{{ t('aiTransSubtitleSetting') }}</span>
            </div>

            <Teleport to="body">
              <SubtitleStyle
                v-if="styleDialogInfo.enableDialog"
                @clickClose="styleDialogInfo.close"
                :task-id="taskId"
                :start-x="left"
                :start-y="top+height+4"
              />
            </Teleport>

          </UseElementBounding>

          <div v-if="detailInfo.speakTask===Speaker.disable||detailInfo.speakTask===Speaker.enable">
            <div class="flex-center">
              <SpeakerSetting
                :enable-dialog="voiceDialogInfo.isActive"
                @click="voiceDialogInfo.open"
                @clickOk="handleSaveSetting"
                @closeDialog="voiceDialogInfo.close"
              >
                <div class="hover:text-theme flex-center" id="speaker-manager-slot-id">
                  <SpeakerIcon/>
                  <span class="ml-[4px]">{{ t('aiTranslateKey26') }}</span>
                </div>
              </SpeakerSetting>
            </div>
          </div>

          <UpdateAudio
            ref="updateAudioRef"
            v-if="detailInfo.speakTask!==Speaker.zero"
            :merge-audio-equity="equityInfo.info!.merge_audio_equity"
            :try-count="equityInfo.tryTransAudioCount"
            :task-id="taskId"
            @onRetryFinish='equityInfo.fetchEquityInfo()'
            @isUpdateAudioLoading="(i)=>updateAudioInfo.updateLoadingState(i)"
            @updateAudioProgress="(i)=>updateAudioInfo.progress=i"
          />

        </div>

        <div
          class="h-full overflow-y-auto scroll-bar ![&::-webkit-scrollbar]:w-[10px] relative"
          id="subtitle-list-container-id"
          @mousedown="handleSafariMousedown"
        >
          <DynamicScroller
            ref="scrollRef"
            class="h-full scroll-bar"
            :items="detailInfo.subtitleList"
            :min-item-size="128"
            key-field="start"
          >
            <template v-slot="{ item,index,active }">
              <DynamicScrollerItem
                :key="item.start"
                :item="item"
                :active="active"
                :size-dependencies="[(item as SrtModel).text,(item as SrtModel).translateText]"
                :data-index="index"
                :index="item.start"
                class="scroll-item"
                :class="[videoSubtitleInfo.activeSubtitleIndex===index?'active':'']"
              >
                <div
                  class="group/item pl-[24px] mr-[6px] pr-[6px] pt-[16px]"
                  :class="[
                    subtitleInfo.editIndex===index?'editing':'',
                    transTextInfo.loadingItemList.includes(item)?'loading':'',
                  ]"
                >
                  <div
                    class="py-[16px] px-[8px] rounded-[6px] border group-hover/item:border-theme w-full h-auto flex cursor-pointer flex-col relative"
                    :class="[videoSubtitleInfo.activeSubtitleIndex===index?'border-theme':'border-transparent']"
                    @click="seekTo(parseInt(item.start) / 1000)"
                  >
                    <div class="flex items-center ml-[16px]">
                      <!--时间戳-->
                      <div
                        :key="item.start"
                        class="text-black/[0.5] text-[12px] flex items-center"
                        @click.stop="pauseVideo"
                      >
                        <!--开始时间的最小值是上一条字幕的结束时间 单位s-->
                        <!--开始时间的最大值是当前字幕的结束时间-0.1秒 单位s-->
                        <TimeStamp
                          :key="item.start"
                          class="*:cursor-pointer focus:*:cursor-auto"
                          :min="parseInt(detailInfo.subtitleList?.[index-1]?.end)/1000 || 0"
                          :max="(parseInt(item.end)/1000)-0.1"
                          :time="parseInt(item.start)/1000"
                          :is-last="index===detailInfo.subtitleList.length-1"
                          type="start"
                          @updateTime="(t)=>handleUpdateTimeStamp(item,t,'start')"
                          @tips="rendTips"
                        />
                        <span>-</span>
                        <!--结束时间的最小值是当前字幕的开始时间+0.1秒 单位s-->
                        <!--结束时间的最大值是下一条字幕的开始时间 单位s-->
                        <TimeStamp
                          :key="item.end"
                          class="*:cursor-pointer focus:*:cursor-auto"
                          type="end"
                          :min="parseInt(item.start)/1000 + 0.1"
                          :max="parseInt(detailInfo.subtitleList[index+1]?.start)/1000 || detailInfo.duration"
                          :time="parseInt(item.end)/1000"
                          :is-last="index===detailInfo.subtitleList.length-1"
                          @updateTime="(t)=>handleUpdateTimeStamp(item,t,'end')"
                          @tips="rendTips"
                        />
                      </div>
                      <template v-if="detailInfo.speakTask===Speaker.enable">
                        <AvatarVoice
                          :index="index"
                          :speaker="item.translateRestParams?.speaker??''"
                          :pop-position="index===detailInfo.subtitleList.length-1?'top':'bottom'"
                          @clickItem="(speaker,voice)=>handleUpdateSpeaker(item,speaker,voice)"
                          @click="pauseVideo"
                        />
                      </template>
                    </div>
                    <OnClickOutside
                      class="flex rounded-[12px] p-[16px] mt-[6px] bg-[#F5F5F5]"
                      @trigger="()=>subtitleInfo.editIndex===index&&subtitleInfo.closeEdit()"
                    >
                      <div
                        class="w-[var(--w)] flex-1 origin group-[.loading]/item:opacity-50 group-[.loading]/item:pointer-events-none"
                        @dblclick="handleDbClickSubtitle(index,$event)"
                      >
                        <div class="text-[16px] my-[2px] 2xl:mt-[8px] relative break-all">
                          <div
                            :data-edit-index="subtitleInfo.editIndex"
                            class="break-words whitespace-break-spaces min-h-4 [&_mark]:bg-transparent [&_mark.active]:text-theme [&_mark.active]:font-bold"
                            :class="subtitleInfo.editIndex===index ? 'invisible' : 'visible'"
                            v-html="searchInfo.highlightSearchWord(item.text)"
                          ></div>

                          <textarea
                            v-if="subtitleInfo.editIndex===index"
                            class="origin break-words absolute left-0 top-0 w-full h-[calc(100%+4px-22px-4px)] resize-none font-normal bg-transparent border-b-1 border-b-theme pb-[2px] text-theme overflow-hidden peer"
                            type="text"
                            v-model="item.text"
                            @change="handleSaveSubtitle('text')"
                            @click.stop
                          ></textarea>

                          <div
                            class="mt-[6px] h-[18px] min-w-[260px] peer-focus:visible invisible 3xl:whitespace-nowrap"
                            v-if="subtitleInfo.editIndex===index"
                          >
                            <CpsText
                              :text="item.text"
                              :max-count="inputLimitInfo.getMaxCount(detailInfo.originLang)"
                              :max-cps="inputLimitInfo.getMaxCps(detailInfo.originLang)"
                              :read-duration="(parseInt(item.end)-parseInt(item.start)) / 1000"
                            />
                          </div>
                        </div>
                      </div>

                      <div
                        v-if="transTextInfo.statusMap.has(item)&&(transTextInfo.statusMap.get(item) !== null)"
                        :class="[transTextInfo.statusMap.get(item)==='Success!'?'text-theme':'text-[#ef6f3c]']"
                        class="w-[76px] 3xl:w-[116px] flex-center"
                      >
                        {{ transTextInfo.statusMap.get(item) }}

                      </div>

                      <div
                        v-else
                        class="group-[.editing]/item:visible relative mx-[28px] 3xl:mx-[48px] flex-center cursor-pointer invisible group-hover/item:visible"
                        :class="[transTextInfo.loadingItemList.includes(item)?'!visible group':'']"
                      >

                        <div
                          class="size-[20px] relative group/trans group-[.loading]/item:invisible"
                        >
                          <TranslateIcon
                            class="hover:text-theme"
                            :class="[item.text.length===0?'opacity-40 pointer-events-none':'']"
                            @click="handleStartTransText(item,index)"
                          />
                          <div
                            class="absolute -translate-x-1/2 scale-0 group-hover/trans:scale-100 transition origin-bottom *:bg-white *:z-10 *:w-max  text-deep-color *:border *:border-[#EEE] *:rounded-[10px] *:shadow-[0_10px_20px_0_rgba(30,61,36,0.1)] *:p-[12px_16px] z-30"
                            :class="[index===0?'top-[calc(100%+4px)] left-1/2':'top-0 left-1/2 -translate-y-full ']"
                          >
                            <div class="flex-center gap-[4px]">
                              <span class="flex-center">
                                {{ t('aiTransRetryTranslate').split('#')[0] }}
                                <template v-if="equityInfo.textTransCount === 0">
                                  <CreditIcon/> 1
                                </template>
                                <template v-else>
                                  {{ equityInfo.textTransCount }}
                                </template>
                                {{ t('aiTransRetryTranslate').split('#')[1] }}
                              </span>
                              <img v-element-hover="(flag)=>transTextInfo.isHoverTipImg=flag" :src="tipIcon" alt="">
                            </div>


                            <div
                              class="absolute w-max left-full top-0 translate-x-[10px] transition z-30"
                              :class="[transTextInfo.isHoverTipImg?'opacity-100':'opacity-0']"
                            >
                              {{
                                t('aiTransRetryTranslateTimes').replace('#', String(equityInfo.info?.copy_writing_translation_equity?.limit))
                              }}
                            </div>
                          </div>

                        </div>

                      </div>

                      <div class="w-[var(--w)] flex-1 translate" @dblclick="handleDbClickSubtitle(index,$event)">
                        <div class="text-[16px] mt-[2px] 2xl:mt-[8px] relative break-all">
                          <div
                            class="break-words group-[.loading]/item:opacity-0 whitespace-break-spaces min-h-4 [&_mark]:bg-transparent [&_mark.active]:text-theme [&_mark.active]:font-bold"
                            :class="subtitleInfo.editIndex===index ? 'invisible' : 'visible'"
                            v-html="searchInfo.highlightSearchWord(item.translateText)"
                          ></div>

                          <textarea
                            v-if="subtitleInfo.editIndex===index"
                            class="trans break-words group-[.loading]/item:invisible absolute left-0 top-0 w-full h-[calc(100%+4px-22px-4px)] resize-none font-normal bg-transparent border-b-1 border-b-theme pb-[2px] text-theme overflow-hidden peer"
                            type="text"
                            v-model="item.translateText"
                            @change="handleSaveSubtitle('translateText');detailInfo.retryStatus = RetryStatus.allowRetry;"
                            @click.stop
                          ></textarea>

                          <div
                            class="mt-[6px] h-[18px] peer-focus:visible invisible min-w-[260px] 3xl:whitespace-nowrap"
                            v-if="subtitleInfo.editIndex===index"
                          >
                            <CpsText
                              :text="item.translateText"
                              :max-count="inputLimitInfo.getMaxCount(detailInfo.translateLang)"
                              :max-cps="inputLimitInfo.getMaxCps(detailInfo.translateLang)"
                              :read-duration="(parseInt(item.end)-parseInt(item.start)) / 1000"
                            />
                          </div>

                          <div
                            class="group/close w-max h-full absolute left-0 top-1/2 -translate-y-1/2 group-[.loading]/item:flex hidden items-center gap-[20px]"
                          >
                            <img class="w-[80px]" :src="publicPath('/gif/loading-without-bg.gif')" alt="">
                            <CloseIcon
                              class="text-gray-600 invisible scale-50 group-hover/close:visible group-hover/close:scale-100 transition"
                              @click.stop="handleCancelTransText(item)"
                            />
                          </div>

                        </div>
                      </div>
                      <div class="w-[16px] flex-center ml-[16px]">
                        <div
                          class="group-[.editing]/item:!hidden group-[.loading]/item:!hidden cursor-pointer hidden group-hover/item:block hover:text-theme-darker"
                          @click="handleOpenEditWithFocusLast(index,$event)"
                        >
                          <EditIcon/>
                        </div>
                        <div
                          class="hidden group-[.editing]/item:block w-full cursor-pointer"
                          @click="subtitleInfo.closeEdit()"
                        >
                          <OkIcon/>
                        </div>
                      </div>

                    </OnClickOutside>

                    <div
                      class="absolute right-0 top-0 translate-x-1/2 -translate-y-1/2 invisible scale-50 !group-[.loading]/item:hidden !group-[.editing]/item:hidden group-hover/item:visible group-hover/item:scale-100 transition"
                      :class="[detailInfo.subtitleList.length===1?'!hidden':'']"
                      @click.stop="handleDeleteSubtitle(index)"
                    >
                      <CloseIcon/>
                    </div>
                  </div>
                  <div
                    class="hidden !group-[.editing]/item:hidden !group-[.loading]/item:hidden group-hover/item:flex-center w-full pt-[12px]">
                    <span class="flex-1 border-dashed border-dark border-b-[1px]"></span>

                    <div
                      class="mx-[20px] cursor-pointer hover:text-theme"
                      :class="[detailInfo.subtitleList.length===1||index===detailInfo.subtitleList.length-1?'opacity-50 pointer-events-none':'']"
                      @click.stop="handleMergeSubtitle(index,index+1)"
                    >
                      <MultiMergeIcon/>
                      <span class="ml-[8px] text-[14px]">{{ t('multiVoiceMergeTitle') }}</span>
                    </div>

                    <span class="w-[8.8%] border-dashed border-dark border-b-[1px]"></span>

                    <div
                      class="mx-[20px] cursor-pointer hover:text-theme add-subtitle-options"
                      @click.stop="handleAddSubtitle(index)"
                    >
                      <MultiAddIcon/>
                      <span class="ml-[8px] text-[14px]">{{ t('multiVoiceAddTitle') }}</span>
                    </div>

                    <span class="flex-1 border-dashed border-dark border-b-[1px]"></span>
                  </div>
                </div>

              </DynamicScrollerItem>
            </template>
          </DynamicScroller>

        </div>

        <div
          v-show="updateAudioInfo.isLoading"
          class="absolute size-full bg-[rgba(250,250,250,0.8)] top-0 left-0 flex-center rounded-[20px] z-109"
        >
          <TheLoading :progress="updateAudioInfo.progress">
            {{ t('aiTranslateKey11') }}
          </TheLoading>

        </div>
      </div>
      <div
        v-show="loadingInfo.isReady===true"
        class="bg-white min-w-[34vw] 2xl:min-w-[600px] w-[35.2vw] pb-[20px] shadow-[0_10px_20px_0_rgba(30,61,36,0.08)] flex flex-col overflow-y-auto scroll-bar-none relative z-[2]"
      >
        <div>
          <VideoPlayer :task-id="props.taskId"/>
        </div>

        <div class="flex pt-[40px] flex-1 px-[30px]">
          <div class="size-full flex-col flex justify-end">
            <button
              :class="[updateAudioInfo.isLoading?'pointer-events-none opacity-34':'']"
              class="gradient-button w-full px-[20px] py-[8px] relative"
              @click="handleClickStartConvert"
            >
              <span>{{ t('aiTranslateKey12') }}</span>
            </button>
          </div>
        </div>
      </div>
    </template>

  </div>

</template>

<style scoped>

.scroll-item:focus-within .add-subtitle-options {
  opacity: 0.4;
  pointer-events: none;
}

</style>

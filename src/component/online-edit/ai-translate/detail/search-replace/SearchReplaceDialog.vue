<script setup lang="ts">
import CloseIcon from "~icons/ai-subtitle/close.svg"
import ReplaceIcon from "~icons/ai-subtitle/replace-icon.svg"

import {computed, onMounted, reactive, ref, watch} from 'vue';
import {useI18n} from '@/locales';
import {vFocus} from '@/directives/input'
import useSearchReplace from '@/component/online-edit/ai-translate/detail/search-replace/uesSearchReplace';
import {useDraggable} from '@vueuse/core';
import {ReplaceType} from '@/interface/AiTranslate';

const {t} = useI18n()

enum WordEditType {
  search,
  replace,
}

const props = defineProps<{
  startX: number,
  startY: number,
}>()

const emit = defineEmits<{
  clickClose: [],
  replaceSingle: [type: ReplaceType],
  replaceAll: [types: ReplaceType[]],
}>()

const {searchInfo, replaceInfo} = useSearchReplace()
const dragElRef = ref<HTMLDivElement>()
const searchReplaceElRef = ref<HTMLDivElement>()
const tabInfo = reactive({
  activeTab: WordEditType.search,
  setIndex(item: WordEditType) {
    tabInfo.activeTab = item
  },
})
const {x, y} = useDraggable(dragElRef, {
  initialValue: {x: 0, y: 0}
})
const position = reactive({
  currentX: x,
  currentY: y,
})

function controlPosition() {
  if (position.currentX < 0) {
    position.currentX = 0
  } else if (position.currentX + searchReplaceElRef.value!.clientWidth > window.innerWidth) {
    position.currentX = window.innerWidth - searchReplaceElRef.value!.clientWidth
  }
  if (position.currentY < 74) {
    position.currentY = 74
  } else if (position.currentY + searchReplaceElRef.value!.clientHeight > window.innerHeight) {
    position.currentY = window.innerHeight - searchReplaceElRef.value!.clientHeight
  }
}

watch([x, y], () => {
  controlPosition()
})
onMounted(() => {
  position.currentX = props.startX
  position.currentY = props.startY
  controlPosition()
})
const handleCloseDialog = () => {
  // 关闭弹框清空
  replaceInfo.replaceWord = ''
  searchInfo.searchWord = ''
  emit('clickClose')
}

//显示是否允许点击`上一个`
const enableSearchPrevRef = computed(() => {
  return searchInfo.searchCount > 0 && searchInfo.searchIndex > 0
})


//显示是否允许点击`下一个`
const enableSearchNextRef = computed(() => {
  return searchInfo.searchCount > 0 && searchInfo.searchIndex < searchInfo.searchCount - 1
})

//是否允许点击`替换`和`全部替换`
const enableReplaceRef = computed(() => {
  return searchInfo.searchCount > 0 && replaceInfo.replaceWord.length > 0
})

function handleReplaceSingle() {
  const replaceType = replaceInfo.handleReplaceSingle()
  emit('replaceSingle', replaceType)
}

function handleReplaceAll() {
  const replaceTypes = replaceInfo.handleReplaceAll()
  emit('replaceAll', replaceTypes)
}

</script>

<template>
  <div
    ref="searchReplaceElRef"
    :style="{left:`${position.currentX}px`,top:`${position.currentY}px`}"
    class="fixed z-[101] min-w-[386px] bg-white shadow-[0_10px_60px_0_rgba(0,0,0,0.1)] rounded-[16px] cursor-auto"
  >
    <div
      ref="dragElRef"
      class="flex items-center pt-[30px] px-[30px] justify-between cursor-move"
    >
      <div class="flex-center text-dark text-[18px] gap-[20px]">
        <button
          :class="[tabInfo.activeTab===WordEditType.search?'text-theme font-semibold text-[20px]':'']"
          @click="tabInfo.setIndex(WordEditType.search)"
        >
          {{ t('aiSubtitleFind') }}
        </button>
        <button
          :class="[tabInfo.activeTab===WordEditType.replace?'text-theme font-semibold text-[20px]':'']"
          @click="tabInfo.setIndex(WordEditType.replace)"
        >
          {{ t('aiSubtitleReplaceSingle') }}
        </button>
      </div>
      <button class="text-dark hover:text-danger-color" @click="handleCloseDialog">
        <CloseIcon/>
      </button>
    </div>

    <div class="flex flex-col w-full gap-[30px] px-[30px] py-[30px]">

      <div class="flex flex-col gap-[12px]">
        <div
          class="flex-center w-full h-[40px] px-[16px] border-[1px] border-solid border-[rgba(204,204,204,1)] rounded-[8px]">
          <input
            class="h-full w-full"
            type="text"
            v-focus
            :value="searchInfo.searchWord"
            @input="searchInfo.handleInput"
            :placeholder="t('aiSubtitleEnterFindText')"
            @keydown.enter="searchInfo.nextSearchWord"
            @keydown.up="searchInfo.preSearchWord"
            @keydown.down="searchInfo.nextSearchWord"
          />
          <div class="pl-[16px]">
            <span class="text-dark">{{ searchInfo.searchCount > 0 ? searchInfo.searchIndex + 1 : 0 }}</span>
            <span class="text-[rgba(45,45,51,0.5)]">/{{ searchInfo.searchCount }}</span>
          </div>

        </div>
        <div
          v-show="tabInfo.activeTab===WordEditType.replace"
          class="text-[14px] text-[#2D2D33]"
        >
          <ReplaceIcon/>
          {{ t('aiSubtitleReplaceWith') }}
        </div>
        <input
          v-if="tabInfo.activeTab===WordEditType.replace"
          class="w-full h-[40px] px-[16px] border-[1px] border-solid border-[rgba(204,204,204,1)] rounded-[8px]"
          type="text"
          v-focus
          :placeholder="t('aiSubtitleEnterReplaceText')"
          v-model="replaceInfo.replaceWord"
        />
      </div>
      <div
        v-if="tabInfo.activeTab===WordEditType.search"
        class="flex justify-end gap-[20px] text-[14px] font-semibold"
      >
        <button
          class="px-[26px] h-[36px] text-dark bg-white border-[1px] border-dark rounded-[8px] flex-center"
          :class="[enableSearchPrevRef?'':'pointer-events-none opacity-50']"
          @click="searchInfo.preSearchWord"
        >
          {{ t('aiSubtitlePrevios') }}
        </button>
        <button
          class="px-[26px] h-[36px] text-white bg-dark rounded-[8px] flex-center"
          :class="[enableSearchNextRef?'':'pointer-events-none opacity-50']"
          @click="searchInfo.nextSearchWord"
        >
          {{ t('aiSubtitleNext') }}
        </button>
      </div>
      <div
        v-else
        class="flex justify-end gap-[20px] text-[14px] font-semibold"
      >
        <button
          class="px-[26px] h-[36px] text-dark bg-white border-[1px] border-dark rounded-[8px] flex-center"
          :class="[enableSearchNextRef?'':'pointer-events-none opacity-50']"
          @click="searchInfo.nextSearchWord"
        >
          {{ t('aiSubtitleNext') }}
        </button>
        <button
          class="px-[26px] h-[36px] text-dark bg-white border-[1px] border-dark rounded-[8px] flex-center"
          :class="[enableReplaceRef?'':'pointer-events-none opacity-50']"
          @click="handleReplaceSingle"
        >
          {{ t('aiSubtitleReplaceSingle') }}
        </button>
        <button
          class="px-[26px] h-[36px] text-white bg-dark rounded-[8px] flex-center"
          :class="[enableReplaceRef?'':'pointer-events-none opacity-50']"
          @click="handleReplaceAll"
        >
          {{ t('aiSubtitleReplaceAll') }}
        </button>
      </div>

    </div>
  </div>
</template>

<style scoped>

</style>

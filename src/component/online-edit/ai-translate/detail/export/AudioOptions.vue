<script setup lang="ts">

import {createExportByTaskIdApi, queryExportByTaskIdApi} from '@/api/aiTranslateApi';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import showDialog from '@/render/showDialog';
import {useI18n} from '@/locales';
import {
  ExportCreateType,
  Speaker,
  SubtitleItemModel
} from '@/interface/AiTranslate';
import {computed} from 'vue';
import AiTranslateTrack, {AudioLangType} from '@/track/AiTranslateTrack';
import vipIcon from '@images/vip/must-vip-icon.svg'
import useLoginService from '@/services/useLoginService';
import useExportDialog from '@/component/online-edit/ai-translate/detail/export/useExportDialog';
import CreateTaskError from '@/services/exception/CreateTaskError';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {t} = useI18n()
const {isVipRef} = useLoginService()

const {handleExportOnlyVip} = useExportDialog()

const props = defineProps<{
  taskId: string,
  fileSize: number,
  format: string,
  duration: number,
  originSubtitles: SubtitleItemModel[],
  videoId: string,
  speakTask: number,
  translateLangTitle: string
}>()

const emit = defineEmits<{
  startLoading: [],
  updateProgress: [progress: number],
  endLoading: [],
  closeDialog: [],
}>()

const subtitleTransRef = computed(() => {
  return props.speakTask === Speaker.zero
})

const handleClickExport = async () => {
  if (isVipRef.value === false) {
    //不是vip 先购买
    handleExportOnlyVip().then()
    return
  }
  emit('startLoading')
  try {
    AiTranslateTrack.realGenerateVideo({
      format: 'mp3',
      audio_lang: AudioLangType.trans
    }).then()
    const taskId = await createExportByTaskIdApi(props.taskId, {
      type: ExportCreateType.audioWithTrans
    })
    const taskQuery = new SingleTaskUtil({
      api() {
        return queryExportByTaskIdApi(taskId)
      }
    })
    const data = await taskQuery.startQuery({
      onProgress(i) {
        emit('updateProgress', i)
      }
    })
    emit('endLoading')
    emit('closeDialog')
    AiTranslateTrack.generateVideoResult({
      result: 'success',
      filesize: props.fileSize,
      format: props.format,
      time: props.duration,
      text: props.originSubtitles.map(item => item.text).join('').substring(0, 1000),
      task_id: props.taskId,
      video_id: props.videoId,
      model: props.speakTask === Speaker.zero ? 'zero' : props.speakTask === Speaker.enable ? 'multi' : 'single'
    }).then()
    window.open(data.result, '_self')
    showDialog(t('aiTranslateKey35'), {
      cancelBtnClass: '!hidden'
    })
  } catch (e) {
    let errorStatus = ''

    if (e instanceof CreateTaskError) {
      const s = e.payload?.status
      if (s) {
        errorStatus = s.toString()
      }
    } else if (e instanceof QueryTaskError) {
      const s = e.payload?.state?.toString()
      if (s) {
        errorStatus = s.toString()
      }
    }
    AiTranslateTrack.generateVideoResult({
      result: 'fail',
      reason: TaskErrorMsg.safeToJSON(e),
      message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
      status: errorStatus,
    }).then()
    showDialog(t('002917'))
  }
}
</script>

<template>
  <div class="text-dark mt-[24px]">
    {{
      subtitleTransRef ? `*${t('aiTranslateKey33')}` : `${t('aiTranslateKey21')}(${props.translateLangTitle})`
    }}
  </div>
  <div class="flex justify-end mt-[24px]">
    <button
      :class="[subtitleTransRef?'opacity-50 pointer-events-none':'']"
      class="flex-center gap-[4px] gradient-button text-[16px] font-bold p-[12px_20px] min-w-[200px] relative"
      @click.stop="handleClickExport"
    >
      <img :src="vipIcon" alt="vip icon">
      {{ t('aiTranslateKey23') }}
    </button>
  </div>


</template>

<style scoped>

</style>

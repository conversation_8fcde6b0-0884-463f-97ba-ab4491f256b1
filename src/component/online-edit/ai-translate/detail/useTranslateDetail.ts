import {reactive, ref} from 'vue';
import {fetchCategoryTitleList<PERSON>pi, fetchDetailByTaskIdApi, fetchVoiceListByLangApi} from '@/api/aiTranslateApi';
import {
  RestParams,
  RetryStatus,
  Speaker,
  SpeakerItemType,
  SrtModel,
  SubtitleItemModel,
  SwitchAudioType,
  SwitchSubtitleType,
  VoiceItemType
} from '@/interface/AiTranslate';
import useTranslateSubtitleStyle
  from '@/component/online-edit/ai-translate/detail/subtitle-style/useTranslateSubtitleStyle';
import AiTranslateTrack from '@/track/AiTranslateTrack';
import {useI18n} from '@/locales';
import VideoPreviewV2 from '@/component/online-edit/ai-translate/detail/video-player/VideoPreviewV2.vue';
import showDialog from '@/render/showDialog';

const videoPreviewElRef = ref<InstanceType<typeof VideoPreviewV2> | null>(null)
const scrollRef = ref<any>()
const {t} = useI18n()
const {setSubtitleStyle} = useTranslateSubtitleStyle()
const detailInfo = reactive({
  uniqid: '',
  originLang: '',
  translateLang: '',
  subtitleList: [] as SrtModel[],//包含源语言和翻译语言的字幕
  videoUrl: '',
  videoHeight: 0,
  audioUrl: '',
  bgmUrl: '',
  duration: 0,
  //音色设置
  voice: '',
  rate: 1,
  volume: 100,
  switchAudioType: SwitchAudioType.translate,
  switchSubtitleType: SwitchSubtitleType.translate,
  retryStatus: RetryStatus.hidden,//重新合成音频状态
  speakTask: Speaker.disable,//是否区分说话人 0 1视频翻译｜2字幕翻译｜3克隆翻译
  speakerList: [] as SpeakerItemType[],//说话人列表

  async fetchDetailInfo(taskId: string) {
    const result = await fetchDetailByTaskIdApi(taskId)
    if (result) {
      this.subtitleList = this._handleSubtitle(result.origin_subtitles, result.translation_subtitles)
      this.videoUrl = result?.vod_url ?? ''
      this.audioUrl = result?.sample_audio_url ?? ''
      this.bgmUrl = result?.bgm_url ?? ''
      this.voice = result?.voice ?? ''
      this.rate = result?.speech_rate ?? 1
      this.volume = result?.volume ?? 100
      this.originLang = result.origin_lang ?? ''
      this.translateLang = result.translation_lang ?? ''
      this.duration = result?.duration ?? 0
      this.uniqid = result?.uniqid ?? ''
      setSubtitleStyle(result.subtitle_styles)
      this.videoHeight = result.height
      this.speakTask = result.speaker_identification
      this.speakerList = this._handleSpeakerTitle(result?.speakers ?? [])
      this.switchSubtitleType = result?.subtitle_type ?? SwitchSubtitleType.translate
      this.switchAudioType = result?.audio_type ?? SwitchAudioType.translate
      this.retryStatus = Boolean(result.subtitle_changed) ? RetryStatus.allowRetry : RetryStatus.hidden
    } else {
      throw new Error('task id was not found')
    }
  },
  _handleSubtitle(originList: SubtitleItemModel[], translateList: SubtitleItemModel[]) {
    //标准化字幕列表, 大模型返回的可能是有问题的, 在这个函数中过滤调整
    //1.根据start去重
    const normalizeList = (list: SubtitleItemModel[]) => {
      const map = new Map<string, SubtitleItemModel>();
      for (const item of list) {
        map.set(item.start, item);
      }
      return Array.from(map.values());
    }

    originList = normalizeList(originList)
    translateList = normalizeList(translateList)
    const subtitleList = originList.length > translateList.length ? originList : translateList
    const getRestParams = (item: SubtitleItemModel) => {
      //获取除start,end, text剩余的参数
      const copyItem = {...item}
      Reflect.deleteProperty(copyItem, 'start')
      Reflect.deleteProperty(copyItem, 'end')
      Reflect.deleteProperty(copyItem, 'text')
      return copyItem satisfies  RestParams
    }
    //以时间戳来匹配, l1,l2对应字幕项时间戳是相同的, 但是整个列表长度可能不同, 应当以长的为主
    const list = subtitleList.map((value, index) => {
      const origin = originList?.find(i => i.start === value.start)
      const translate = translateList?.find(i => i.start === value.start)
      const baseItem = origin || translate

      return {
        start: baseItem!.start,
        end: baseItem!.end,
        text: origin?.text ?? '',
        translateText: translate?.text ?? '',
        originRestParams: origin && getRestParams(origin),
        translateRestParams: translate && getRestParams(translate)
      } satisfies SrtModel

    }).filter(Boolean)
    return list
  },
  _handleSpeakerTitle(list: SpeakerItemType[]) {
    const key = 'speaker'
    list.forEach(item => {
      if (Boolean(item.title) === false) {
        const index = Number(item.speaker.replace(key, ''))
        item.title = t('aiTranslateKey27').replace('#', String(index + 1))
      }
    })
    return Array.from(list).sort((a, b) => {
      const i1 = a.speaker.replace(key, '')
      const i2 = b.speaker.replace(key, '')
      return Number(i1) - Number(i2)
    })
  },
  resetDetailInfo() {
    type ValueOfBasic = {
      [K in keyof typeof detailInfo as typeof detailInfo[K] extends Function ? never : K]: typeof detailInfo[K]
    }
    Object.assign(detailInfo, {
      uniqid: '',
      originLang: '',
      translateLang: '',
      subtitleList: [] as SrtModel[],//包含源语言和翻译语言的字幕
      videoUrl: '',
      audioUrl: '',
      duration: 0,
      //音色设置
      voice: '',
      rate: 1,
      volume: 100,
      retryStatus: RetryStatus.hidden,
      videoHeight: 0,
      speakTask: Speaker.disable,
      speakerList: [],
      switchSubtitleType: SwitchSubtitleType.translate,
      switchAudioType: SwitchAudioType.translate,
      bgmUrl: '',
    } satisfies ValueOfBasic)
  },
})

const voiceListInfo = reactive({
  list: [] as VoiceItemType[],
  async fetchAllVoice() {
    const categoryList = await fetchCategoryTitleListApi(detailInfo.translateLang)
    const list = await fetchVoiceListByLangApi(detailInfo.translateLang, categoryList[0])
    voiceListInfo.list = list
  }
})

const videoSubtitleInfo = reactive({
  //视频正在观看的字幕index
  activeSubtitleIndex: -1,
})


export default function useTranslateDetail() {
  const dateFormat = (second: number) => {
    const milliseconds = parseInt(String(second * 1000 % 1000)).toString().padStart(3, '0')
    const seconds = parseInt(String(second % 60))
    const Minutes = parseInt(String(second / 60)) % 60;
    const Hours = parseInt(String(second / 60 / 60));
    return (
      (Hours >= 10 ? Hours : "0" + Hours) +
      ":" +
      (Minutes >= 10 ? Minutes : "0" + Minutes) +
      ":" +
      (seconds >= 10 ? seconds : "0" + seconds) +
      "," +
      milliseconds
    );
  }

  async function showPointNotEnoughDialog(cost: number): Promise<'ok' | 'cancel' | 'close'> {
    return new Promise((resolve, reject) => {
      showDialog(t('aiSubtitleUpgradeProcess'), {
        subtitle: t('DialogEstimatedCredits') + cost + t('vipCreditsTab'),
        okText: t('aiSubtitleUpgradeUser'),
        cancelText: t('002606'),
        onClickOk(close) {
          close()
          resolve('ok')
        },
        onClickCancel(close) {
          close()
          resolve('cancel')
        },
        onClickClose(close) {
          close()
          resolve('close')
        },
      })
    })
  }

  async function showMultiVoiceUpdateDialog(cost: number): Promise<'ok' | 'cancel' | 'close'> {
    return new Promise((resolve, reject) => {
      showDialog(t('aiTranslateKey48').replace('#', '4'), {
        subtitle: t('DialogEstimatedCredits') + cost + t('vipCreditsTab') + (cost === 0 ? `(${t('aiTranslateKey49')})` : ''),
        okText: t('002428'),
        cancelText: t('002606'),
        onClickOk(close) {
          close()
          resolve('ok')
        },
        onClickCancel(close) {
          close()
          resolve('cancel')
        },
        onClickClose(close) {
          close()
          resolve('close')
        },
      })
    })
  }

  const subtitleInfo = reactive({
    editIndex: -1,
    openEdit(index: number) {
      AiTranslateTrack.clickEdit({
        model: detailInfo.speakTask === Speaker.zero ? 'zero' : detailInfo.speakTask === Speaker.clone ? 'clone' : detailInfo.speakTask === Speaker.enable ? 'multi' : 'single'
      }).then()
      this.editIndex = index
    },
    closeEdit() {
      this.editIndex = -1
    },

  })

  const seekTo = (time: number) => {
    videoPreviewElRef.value?.seekTo(time)
  }

  return {
    videoPreviewElRef: videoPreviewElRef,
    dateFormat: dateFormat,
    subtitleInfo: subtitleInfo,
    detailInfo: detailInfo,
    seekTo: seekTo,
    voiceListInfo: voiceListInfo,
    videoSubtitleInfo: videoSubtitleInfo,
    scrollRef: scrollRef,
    showPointNotEnoughDialog: showPointNotEnoughDialog,
    showMultiVoiceUpdateDialog: showMultiVoiceUpdateDialog,
  }
}

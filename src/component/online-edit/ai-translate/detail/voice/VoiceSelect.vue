<script setup lang="ts">

import {fetchVoiceListByLangApi} from '@/api/aiTranslateApi';
import PlayIcon from '@/component/online-edit/text-to-speech-v2/multi/views/icons/PlayIcon.vue';
import PauseIcon from '@/component/online-edit/text-to-speech-v2/multi/views/icons/PauseIcon.vue';
import ArrowIcon from '@/component/online-edit/text-to-speech-v2/multi/views/icons/ArrowIcon.vue';
import VoiceSetting from '@/component/online-edit/ai-translate/start-select-file/voice/VoiceSetting.vue';
import {useI18n} from '@/locales';
import {reactive} from 'vue';
import {vOnClickOutside} from '@vueuse/components';
import useVoiceSetting from '@/component/online-edit/ai-translate/start-select-file/voice/useVoiceSetting';
import {isClient} from '@vueuse/core';
import useTranslateDetail from '@/component/online-edit/ai-translate/detail/useTranslateDetail';
import LightIcon from '@/component/online-edit/ai-translate/icons/LightIcon.vue';

const {t} = useI18n()

const props = defineProps<{
  lang: string,
  dialogClass?: string | string[],
  maxTryCount: number,
}>()

const emits = defineEmits<{
  updateVoice: [voice: string],
  updateSpeed: [speed: number],
  updateVolume: [volume: number],
}>()

const {detailInfo} = useTranslateDetail()

const {voiceSettingInfo, voiceListTryListenInfo} = useVoiceSetting({
  activeVoice: detailInfo.voice,
  rate: detailInfo.rate,
  volume: detailInfo.volume,
})

const voiceDialogInfo = reactive({
  isActive: false,
  toggleDialog() {
    this.isActive = !this.isActive
  },
  closeDialog() {
    this.isActive = false
  }
})

function handleTogglePlay() {
  document.querySelector('video')?.pause()
  //试听audio位于body下
  document.querySelector<HTMLAudioElement>('body>audio')?.pause()
  voiceListTryListenInfo.handleClickTogglePlay(voiceSettingInfo.activeVoiceItem, voiceSettingInfo.activeVoiceItem!.url)
}

if (isClient) {
  voiceSettingInfo.fetchList(props.lang)
}

function handleUpdateSpeed(speed: number) {
  voiceSettingInfo.rate = speed
  emits('updateSpeed', speed)
}

function handleUpdateVolume(volume: number) {
  voiceSettingInfo.volume = volume
  emits('updateVolume', volume)
}

function handleUpdateVoice(voice: string) {
  voiceSettingInfo.activeVoice = voice
  emits('updateVoice', voice)
}


function handlePauseVideo() {
  document.querySelector<HTMLMediaElement>('.video-container video,.video-container audio')?.pause()
  document.querySelector<HTMLAudioElement>('body>audio')?.pause()
}
</script>

<template>
  <div
    v-if="voiceSettingInfo.activeVoiceItem"
    class="group/item flex-1 border border-dashed border-dark/[0.5] p-[8px] 2xl:p-[16px] rounded-[12px] flex items-center justify-between cursor-pointer hover:border-theme relative"
    v-on-click-outside="()=>voiceDialogInfo.closeDialog()"
    @click="voiceDialogInfo.toggleDialog"
  >
    <div class="flex flex-col !flex-row [--img-size:30px] [--img-gap:4px]">
      <div class="flex items-center 2xl:gap-[var(--img-gap)]">
        <div class="relative size-[24px] 2xl:size-[var(--img-size)] rounded-full">
          <img
            class="block size-full rounded-full"
            :src="voiceSettingInfo.activeVoiceItem.icon"
            alt="avatar"
          >
          <button
            :class="[voiceListTryListenInfo.uniqid?'!scale-100':'']"
            class="scale-0 group-hover/item:scale-100 transition flex-center absolute top-0 left-0 size-full rounded-full bg-black/[0.5]"
            @click.stop="handleTogglePlay"
          >
            <PlayIcon v-show="!voiceListTryListenInfo.uniqid"/>
            <PauseIcon v-show="voiceListTryListenInfo.uniqid"/>
          </button>

        </div>
        <span class="line-clamp-1 flex-1">{{ voiceSettingInfo.activeVoiceItem.voice_tag }}</span>
      </div>

      <div class="flex gap-[24px] text-[12px] ml-[calc(var(--img-size)+var(--img-gap))] !ml-[4px] !items-center">
        <div class="hidden">
          <span class="text-[rgba(45,51,46,0.60)]">{{ t('textSpeed') + ':' }}</span>
          <span class="ml-1">
            {{ !Number.isInteger(voiceSettingInfo.rate) ? voiceSettingInfo.rate : voiceSettingInfo.rate.toFixed(1) }}x
          </span>
        </div>
        <div class="text-[rgba(45,51,46,0.60)]">
          <span>(</span>
          <span>{{ t('aiTranslateVolumeSetting') + ':' }}</span>
          <span class="ml-1 text-dark">{{ voiceSettingInfo.volume }}%</span>
          <span>)</span>
        </div>
      </div>
    </div>

    <ArrowIcon
      class="transition"
      :class="[voiceDialogInfo.isActive?'rotate-180':'']"
    />

    <VoiceSetting
      v-show="voiceDialogInfo.isActive"
      class="absolute top-0 left-0 -translate-y-full"
      :class="[dialogClass]"
      :voice="voiceSettingInfo.activeVoiceItem.voice"
      :speed="Number(voiceSettingInfo.rate.toFixed(1))"
      :volume="voiceSettingInfo.volume"
      :category-list="voiceSettingInfo.categoryList"
      :default-voice-list="voiceSettingInfo.list"
      :fetch-voice-list-by-category="(category:string)=>fetchVoiceListByLangApi(lang,category)"
      :disable-adjust-speed="true"
      @click.stop
      @updateSpeed="(speed)=>handleUpdateSpeed(speed)"
      @updateVolume="(volume)=>handleUpdateVolume(volume)"
      @updateVoice="(voice)=>handleUpdateVoice(voice)"
      @startTryListenAudio="handlePauseVideo"
    >
      <template #top-area>
        <div class="px-[30px] text-dark/[0.7] text-[12px] flex mb-[18px] gap-1">
          <LightIcon/>
          <span class="inline">{{ t('aiTransRetryGenerateChance').replace('#', String(maxTryCount)) }}</span>
        </div>
      </template>

    </VoiceSetting>
  </div>
</template>

<style scoped>

</style>

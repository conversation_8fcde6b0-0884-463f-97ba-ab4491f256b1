<script setup lang="ts">

import bgGreen from '@images/online-edit/bg-green.svg';
import bgLine from '@images/online-edit/bg-line.svg';
import searchIcon from '@images/ai-subtitle/search-icon-bg.svg';
import BackBtn from '@/component/online-edit/BackBtn.vue';
import {useI18n} from '@/locales';
import useAiTranslate from '@/component/online-edit/ai-translate/useAiTranslate';
import {computed, onMounted, ref} from 'vue';
import StartSelectFile from '@/component/online-edit/ai-translate/start-select-file/StartSelectFile.vue';
import TranslateDetail from '@/component/online-edit/ai-translate/detail/TranslateDetail.vue';
import HistoryList from '@/component/online-edit/ai-translate/history-list/HistoryList.vue';
import useLoginService from '@/services/useLoginService';
import AiTranslateTrack from '@/track/AiTranslateTrack';
import {debounce} from '@/utils/debounce';
import useHistoryList from '@/component/online-edit/ai-translate/history-list/useHistoryList';
import NoVipCacheClearNotice from '@/component/global/NoVipCacheClearNotice.vue';
import {PreviewStatus} from '@/interface/AiTranslate';

const {t} = useI18n()
const {isLoginRef, doLogin} = useLoginService();
const {historyListInfo} = useHistoryList()
const {previewStatusInfo} = useAiTranslate()

onMounted(() => {
  const hasQuery = (v: string | null | undefined) => {
    return !!v && !!Reflect.get(PreviewStatus, v)
  }
  //一进页面需要如果地址栏没有v参数, 需要主动设置一次
  const searchMap = new URL(location.toString()).searchParams
  const v = searchMap.get('v')

  //如果地址栏带有task-id, 直接跳转到详情
  if (searchMap.get('task-id')) {
    previewStatusInfo.toDetail(searchMap.get('task-id')!, {writeMode: 'replace'})
    return
  }

  if (hasQuery(v) === false) {
    previewStatusInfo.toPage(PreviewStatus.startSelectFile, {writeMode: 'replace'})
  } else {
    previewStatusInfo.toPage(v as PreviewStatus, {writeMode: 'replace'})
  }

})

const PreviewRef = computed(() => {
  if (previewStatusInfo.isStartSelectFile) {
    return StartSelectFile
  } else if (previewStatusInfo.isDetail) {
    return TranslateDetail
  } else if (previewStatusInfo.isHistoryList) {
    return HistoryList
  }

  return StartSelectFile

})

const handleClickBack = () => {
  if (previewStatusInfo.enableFileWithScene === false && previewStatusInfo.isStartSelectFile === true) {
    previewStatusInfo.setEnableFileWithScene()
  } else {
    history.back()
  }
}
const handleToHistoryList = async () => {
  AiTranslateTrack.clickViewList({
    source: 'func_page'
  }).then()
  if (isLoginRef.value === false) {
    await doLogin()
  }
  previewStatusInfo.toHistoryList()
}
const handleClickCreateTask = () => {
  AiTranslateTrack.clickNewTask()
  previewStatusInfo.toStartSelectFile()
}

const enableSearchRef = ref(true)

const doSearch = debounce((value) => {
  if (!enableSearchRef.value) return
  historyListInfo.searchWord = value
  historyListInfo.fetchList()
}, 300)

</script>

<template>
  <div class="size-full bg-[#FAFAFA] pt-header-offset flex flex-col overflow-hidden">
    <div class="absolute right-0 top-0 z-0">
      <img :src="bgGreen" alt="bg icon" draggable="false">
    </div>
    <div class="absolute top-0 left-1/2 -translate-x-1/2 z-0">
      <img :src="bgLine" alt="bg icon" draggable="false">
    </div>

    <NoVipCacheClearNotice
      v-if="previewStatusInfo.isHistoryList"
      class="relative z-10"
    />

    <div class="px-[60px] mt-[30px] flex items-center z-10">

      <BackBtn @click="handleClickBack">
        <span>{{ t('001293') }}</span>
      </BackBtn>

      <div class="ml-auto">
        <form
          v-show="previewStatusInfo.isHistoryList"
          @submit.prevent="historyListInfo.fetchList"
          class="w-[268px] h-[32px] mr-[15px] rounded-[100px] flex-center bg-white text-[14px] has-[:focus]:outline-primary-color outline outline-1 outline-transparent"
        >
          <input
            ref="searchInputElRef"
            type="text"
            class="w-full h-full bg-transparent pl-[20px] pr-[14px]"
            :placeholder="t('historyListSearchText')"
            :value="historyListInfo.searchWord"
            @compositionstart="enableSearchRef = false"
            @compositionend="enableSearchRef = true"
            @input="(ev)=>doSearch((ev.target as HTMLInputElement).value)"
          />
          <button class="pr-[4px]">
            <img class="size-[26px] block object-cover" :src="searchIcon" alt="">
          </button>
        </form>
      </div>

      <button
        v-show="!previewStatusInfo.isHistoryList"
        class="border border-deep-color/[0.5] p-[6px_20px] rounded-[32px] text-[14px] text-dark font-semibold transition hover:translate-y-[4px] duration-500"
        @click="handleToHistoryList"
      >
        {{ t('speechConversionRecords') }}
      </button>

      <button
        v-show="previewStatusInfo.isHistoryList"
        class="p-[6px_30px] bg-theme rounded-[32px] hover:translate-y-[4px] transition text-white"
        @click="handleClickCreateTask"
      >
        {{ t('speechNewCreate') }}
      </button>
    </div>

    <div
      class="w-full h-full z-10"
      :class="[previewStatusInfo.isHistoryList?'px-[30px] 2xl:px-[60px] py-[20px] 2xl:py-[30px]':'px-[60px] py-[30px]']"
    >
      <Component
        :is="PreviewRef"
        :task-id="previewStatusInfo.detailTaskId"
      />
    </div>

  </div>
</template>

<style scoped>

</style>

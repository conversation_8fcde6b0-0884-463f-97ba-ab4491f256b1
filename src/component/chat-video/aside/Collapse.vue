<script setup lang="ts">
defineProps<{
  collapse: boolean
}>()
</script>

<template>
  <div>
    <template v-if="!collapse">
      <svg width="20" height="72" viewBox="0 0 20 72" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="72" rx="10" fill="white"/>
        <rect width="20" height="72" rx="10" fill="currentColor"/>
        <path d="M11.6569 41.3136L6.00001 35.6567L11.6569 29.9999" stroke="white" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </template>

    <template v-else>
      <svg width="20" height="72" viewBox="0 0 20 72" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="72" rx="10" fill="white"/>
        <path d="M0 0H10C15.5228 0 20 4.47715 20 10V62C20 67.5229 15.5228 72 10 72H0V0Z" fill="currentColor"
        />
        <path d="M8.65685 41.3136L14.3137 35.6567L8.65685 29.9999" stroke="white" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </template>
  </div>
</template>

<style scoped>

</style>

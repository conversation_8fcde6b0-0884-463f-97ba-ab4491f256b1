<script setup lang='ts'>
import questionIcon from '../../assets/images/chat-video/chat-video-feat/question-icon.svg'
import arrowIcon from '../../assets/images/chat-video/chat-video-feat/arrow-icon.svg'

import { useI18n } from "@/locales";
import { getLang } from "@central/i18n";
import { ref } from "vue";
const { t } = useI18n();
const lang = getLang();
let isShow = ref([false, false, false]);

const commonQuestion = [
  {
    question: t('howUseChatVideo'),
    answer: t('howUseChatVideoAns')
  },
  {
    question: t('howEnsuredSecurity'),
    answer: t('howEnsuredSecurityAns')
  },
  {
    question: t('whatPlatSupport'),
    answer: t('whatPlatSupportAns')
  }
]
</script>

<template>
  <div
    class="question-container flex flex-col mx-[auto] w-100% max-w-1200px p-[80px] rounded-[30px]"
  >
    <div v-for='(item, index) in commonQuestion' :key='item.question' :class="'question-box-'+index" class='mb-[30px] pb-[24px] border-b-1px border-[#e7eaec] border-solid' >
      <div class='flex flex-row items-center cursor-pointer mb-[10px] transition-all duration-200 translate-y-[0] hover:translate-y-[-4px]' @click='isShow[index]=!isShow[index]'>
        <img loading="lazy" :src="questionIcon" :alt='item.question' class='min-w-[30px] h-[30px] bg-no-repeat bg-center bg-cover mr-[20px]' draggable="false">
        <div class='text-[22px] font-bold text-[#022125]'>{{ item.question }}</div>
        <div class="flex-1"></div>
        <img loading="lazy" :src='arrowIcon' alt='' :class="isShow[index] ? 'rotate-180' : ''" class=' transition-all duration-200 ease-in-out bg-no-repeat bg-center bg-auto' >
      </div>
      <div
        :class="isShow[index] ? 'h-auto scale-y-100' : 'h-0 scale-y-0'"
        class="pl-[50px] text-[#788A7B] text-[16px] leading-[26px] transition origin-top will-change-transform"
      >
        {{ item.answer }}
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.question-container{
  background: rgba(250, 255, 249, 0.70);
  box-shadow: 0 10px 20px 10px rgba(178, 228, 189, 0.10);
  //backdrop-filter: blur(10px);
  .question-box-2{
    border: unset;
    margin-bottom: 0;
  }
}
</style>

---
import {useI18n} from "../../locales";
import {getLang} from "@central/i18n";

import {Picture} from "@central/assets/imagetools";
// import firstBgImg from "../../assets/images/chat-video/chat-video-feat/feat-chatvideo-second.png";
import FeatStartChatVideo from "./FeatStartChatVideo.vue"
// 使用src的路径，import进来的会报错
let firstBgImg = 'src/assets/images/chat-video/chat-video-feat/feat-chatvideo-second.png';
const lang = getLang();
const {t} = useI18n();
const isJp = lang === "jp";
---
<section
  class='relative pt-2px pb-160px  w-full'>
  <div class="absolute w-full h-full">
    <Picture src={firstBgImg} alt="bg" layout="fill"/>
  </div>
  <div class='relative mt-160px flex flex-col items-center z-1'>
    <h2
      class='mx-[auto] font-bold text-[40px] text-deep-color mb-[24px]'
      set:html={t('startChatVideoTitle')}
    >
    </h2>
    <p class='text-[16px] leading-[27px] text-deep-color'>{t('startChatVideoDesc')}</p>
    <FeatStartChatVideo client:visible/>
  </div>
</section>

<script setup lang="ts">

import {nextTick, onMounted, ref, watch} from 'vue';

const props = defineProps<{
  modelValue: string,
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const textAreaElRef = ref<HTMLTextAreaElement>()

function handleResize() {
  const el = textAreaElRef.value!
  if (<PERSON><PERSON><PERSON>(el) === false) return

  if (<PERSON><PERSON><PERSON>(el.value)) {
    el.style.height = 'auto'
    nextTick(() => {
      el.style.height = el.scrollHeight + 'px'
    })
  } else {
    el.style.height = 'auto'
  }
}

onMounted(() => {
  watch(() => props.modelValue, () => {
    handleResize()
  }, {
    flush: 'post',
    immediate: true
  })
})

const handleInput = (event: Event) => {
  const el = event.target as HTMLTextAreaElement
  emit('update:modelValue', el.value)
  handleResize()
}
</script>

<template>
    <textarea
      ref="textAreaElRef"
      rows="1"
      v-bind="$attrs"
      :value="modelValue"
      @input="handleInput"
    ></textarea>
</template>

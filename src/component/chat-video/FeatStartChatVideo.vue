<script setup lang='ts'>
import FeatStartChatAdvantage from "../../component/chat-video/FeatStartChatAdvantage.vue"
import GradientButton from "../../../src/component/base/button/GradientButton.vue"
import boostEfficiency from '@images/chat-video/chat-video-feat/boost-efficiency.png'
import chatRobotEn from '@images/chat-video/chat-video-feat/chat-robot-en.png'
import featAdvantageOk from '../../assets/images/chat-video/chat-video-feat/feat-advantage-ok.svg'
import {getRegionPath} from '@/utils/util'

import {onMounted} from 'vue'
import {useI18n} from '@/locales'
import {getLang} from '@central/i18n';

const {t} = useI18n()


const urlConfig = {
  chatVideo: `${getRegionPath()}/chat-video`,
  freeOnlineRecorder: `${getRegionPath()}/free-online-recorder`,
}
const lang = getLang()

const improveEfficiencyList = [

  lang !== 'zh' && {
    icon: featAdvantageOk,
    title: t('youtubeListItem1'),
    desc: t('youtubeListItem2'),
  },

  {
    icon: featAdvantageOk,
    title: t('filterInformation'),
    desc: t('filterInformationDesc'),
  },
  {
    icon: featAdvantageOk,
    title: t('learnResearch'),
    desc: t('learnResearchDesc'),
  },
]
const chatRobotList = [
  {
    icon: featAdvantageOk,
    title: t('chatGPTSummarize'),
    desc: t('chatGPTSummarizeDesc'),
  },
  {
    icon: featAdvantageOk,
    title: t('detailsSubtitle'),
    desc: t('detailsSubtitleDesc'),
  },
  {
    icon: featAdvantageOk,
    title: t('chatWithVideo'),
    desc: t('chatWithVideoDesc'),
  },
]

onMounted(() => {
  const advantageImg = document.querySelectorAll('#advantage-img')!
  const intersectionObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animation-top')
        intersectionObserver.unobserve(entry.target)
      }
    })
  }, {
    root: null,
    rootMargin: "0px",
    threshold: 0.3,
  })
  advantageImg.forEach(img => intersectionObserver.observe(img))
})
</script>

<template>
  <div class='mt-[90px] mx-auto w-80%'>
    <div class='flex flex-col lg:flex-row justify-evenly items-center small-feat-box'>
      <div class='md:w-[500px]'>
        <div class='text-#02212 text-22px font-bold small-feat-box-title'> {{ t('boostEfficiency') }}</div>
        <FeatStartChatAdvantage :data-source='improveEfficiencyList'/>
        <button class='toggleButton block mx-auto lg:ml-34px'>
          <GradientButton class="min-w-[260px] h-[70px]">
            <div class='flex'>
              <span class='text-28px font-bold'>{{ t('mainStartForFree') }}</span>
            </div>
          </GradientButton>
        </button>
      </div>
      <img
        :src='boostEfficiency'
        :alt="t('boostEfficiency')"
        class='w-[60%] h-[100%] opacity-0 small-img-width'
        id='advantage-img'
        draggable="false"
      />
    </div>
    <div
      class='flex flex-col-reverse lg:flex-row justify-evenly items-center mt-130px gap-[40px] small-feat-box special-feat-box'>
      <img
        :src='chatRobotEn'
        :alt="t('smartChatbot')"
        class='w-[88%] h-[100%] opacity-0 small-img-width'
        id='advantage-img'
        draggable="false"
      />
      <div class='w-full md:w-[500px]'>
        <div class='text-#02212 text-22px font-bold small-feat-box-title'>{{ t('smartChatbot') }}</div>
        <p class='text-deep-color text-[16px] leading-[24px] opacity-60 mt-[16px] small-feat-box-desc'>
          {{ t('smartChatbotDesc') }}</p>
        <FeatStartChatAdvantage :data-source='chatRobotList'/>
        <button class='toggleButton block mx-auto ml-34px'>
          <GradientButton class="min-w-[260px] h-[70px]">
            <div class='flex'>
              <span class='text-28px font-bold'>{{ t('mainStartForFree') }}</span>
            </div>
          </GradientButton>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.animation-top {
  animation: content-top-in .8s ease-in .2s forwards;
  // animation-delay: .2s;
  opacity: 0;
}

@keyframes content-top-in {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }

  100% {
    opacity: 1;
    transform: translate(0);
  }
}

.small-feat-box {
  @media screen and (max-width: 1366px) {
    .small-feat-box-title {
      font-size: 20px;
    }
    .small-feat-box-desc {
      font-size: 14px;
    }
    width: 90%;
    margin: 0 auto;
    .small-img-width {
      width: 54%;

    }
  }
}

.special-feat-box {
  @media screen and (max-width: 1366px) {
    margin-top: 130px;
    //font-size: ;
  }
}

</style>

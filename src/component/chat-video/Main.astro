---
import {useI18n} from "../../locales";
import {getLang} from "@central/i18n";
import reccloudLogo from "../../assets/images/chat-video/chat-video-feat/reccloud-logo.svg";
import bgBounceIcon from "../../assets/images/home/<USER>/bg-bounce.png";
import rebootOpenIcon from "../../assets/images/home/<USER>/reboot-open.svg";
import starIcon from "../../assets/images/online-edit/star.svg";
import {Picture} from "@central/assets/imagetools";
let firstBgImg = 'src/assets/images/chat-video/chat-video-feat/feat-chatvideo-first.png';

const lang = getLang();
const {t} = useI18n();
const isJp = lang === "jp";
---

<section class="relative w-full">
  <div class="chat-video-main relative pb-160px w-full">
    <div class="absolute w-full h-full">
      <Picture src={firstBgImg} alt="bg" layout="fill"/>
    </div>
    <div
      class="pt-180px flex flex-col justify-center items-center relative z-2"
    >
      <img class="mb-40px" src={reccloudLogo} alt="logo" draggable="false"/>
      <h1
        class="leading-normal px-[24px] text-40px font-bold small-de-size-title"
      >
        {
          isJp ? (
            <span class="relative">
              {t("featChatVideoTitleLeft")}
              <span class="px-[5px] relative">
                <span class="text-primary-color">
                  {t("featChatVideoTitleRight")}
                </span>
                <span
                  class="w-[100px] h-[100px] absolute -left-[20px] bottom-[10px] select-none slow-move pointer-events-none">
                  <img
                    draggable="false"
                    class="w-full h-full"
                    src={bgBounceIcon}
                    alt=""
                  />
                </span>
                <span
                  class="w-[24px] h-[26px] absolute left-[-6px] top-[6px] translate-x-[calc(50%-2px)] translate-y-[-50%] select-none">
                  <img
                    class="reboot-icon"
                    draggable="false"
                    src={rebootOpenIcon}
                    alt=""
                  />
                </span>
              </span>
            </span>
          ) : (
            <span class="relative">
              <span
                class="w-[100px] h-[100px] absolute -left-[20px] bottom-[10px] select-none slow-move pointer-events-none">
                <img
                  draggable="false"
                  class="w-full h-full"
                  src={bgBounceIcon}
                  alt=""
                />
              </span>
              <span
                class="w-[24px] h-[26px] absolute left-[-6px] top-[6px] translate-x-[calc(50%-2px)] translate-y-[-50%] select-none">
                <img
                  class="reboot-icon"
                  draggable="false"
                  src={rebootOpenIcon}
                  alt=""
                />
              </span>
              <span class="px-[5px]">
                <span class="text-primary-color">
                  {t("featChatVideoTitleLeft")}
                </span>
              </span>
              {t("featChatVideoTitleRight")}
            </span>
          )
        }
      </h1>
      <p class="mt-26px text-center lg:text-[16px]/[27px] small-de-size-desc">
        {t("chatVideoDesc")}
      </p>
      <p class="mt-26px text-center lg:text-[16px]/[27px] small-de-size-desc">
        {t("enableYoutubeTitle")}
      </p>
      <a
        href=""
        class="text-#1462EF text-[16px] leading-[24px] mt-[60px] hidden"
      >For Developers (API) ></a
      >
      <button class="mt-70px toggleButton gradient-button min-w-[260px] h-[70px] flex-center">
        <span class="flex">
          <span class="text-28px font-bold">{t("mainStartForFree")}</span>
          <img class="self-start" src={starIcon} alt=""/>
        </span>
      </button>
    </div>
  </div>
</section>

<style>
  .small-de-size-title {
    &:lang(de) {
      @media screen and (max-width: 1366px) {
        font-size: 36px;
      }
    }
  }

  .small-de-size-desc {
    &:lang(de) {
      @media screen and (max-width: 1366px) {
        font-size: 14px;
      }
    }
  }

  .main-title {
    background: linear-gradient(92deg, #56e8b3 0.54%, #18ad25 14.89%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .reboot-icon {
    animation: blink 2500ms infinite;
    transition: all 300ms ease-in-out;
  }

  @keyframes blink {
    0% {
      content: url("../../assets/images/home/<USER>/reboot-open.svg");
    }

    30% {
      content: url("../../assets/images/home/<USER>/reboot-close.svg");
    }

    32% {
      content: url("../../assets/images/home/<USER>/reboot-open.svg");
    }

    36% {
      content: url("../../assets/images/home/<USER>/reboot-close.svg");
    }
  }

  @keyframes slowMove {
    0% {
      transform: translate(0, 10px);
    }
    100% {
      transform: translate(0, -20px);
    }
  }

  .slow-move {
    animation: slowMove 3s infinite alternate ease-in-out;
    animation-play-state: running;

    &:hover {
      animation-play-state: paused;
    }
  }
</style>

<script>
  import TrackUtil from '../../track/_TrackUtil';
  import {getRegionPath} from '../../utils/util';

  function initEvent() {
    document.querySelector(".toggleButton")?.addEventListener("click", () => {
      window.open(getRegionPath() + '/chat-video-start', '_self')
    });
    const toggleButton = document.querySelectorAll("button.toggleButton");
    toggleButton.forEach((btn) => {
      btn.addEventListener("click", () => {
        TrackUtil.trackButtonClick('click_start_now')
        window.open(getRegionPath() + '/chat-video-start', '_self')
      })
    });
  }

  initEvent();

</script>

<script setup lang="ts">
import {Swiper, SwiperSlide} from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/pagination';
import {Autoplay, Pagination} from 'swiper'
import {reactive, ref} from 'vue'
import {Swiper as SwiperClass} from 'swiper/types'
import {getLang} from '@central/i18n';
import {getCdnUrl} from '@/utils/util';

const _l = getLang() === 'zh' ? 'zh' : 'en'
const list = ['1.mp4', '2.mp4', '3.mp4', '4.mp4'].map(item => getCdnUrl(`/video/reccloud/video-api/${_l}/${item}`))

const sliderList = reactive(list)

const swiperInstance = ref<SwiperClass | null>(null)
const handleChange = (e: SwiperClass) => {
  activeIndex.value = e.activeIndex
  swiperInstance.value!.slides[activeIndex.value].querySelector('video')?.play()
}

const activeIndex = ref(0)

</script>

<template>
  <div
    class="w-[62.5vw] bg-[rgba(24,173,37,0.1)] mx-auto
          flex flex-col justify-center items-center px-[36px]
          mt-[74px] border-10px border-[rgba(24,173,37,0.1)] rounded-[30px]"
  >
    <div class="w-full h-[35.5vw] bg-white mt-[28px] rounded-[28px] text-center overflow-hidden">
      <Swiper
        class="h-full"
        :slides-per-view="1"
        :space-between="20"
        :loop="true"
        :pagination="{
          clickable: true,
          el: '.pagination',
          enabled:true,
          bulletActiveClass:'active',
          bulletElement:'li',
          bulletClass:'normal',
        }"
        @swiper="sw => swiperInstance = sw"
        @slideChange="handleChange"
        :modules="[Autoplay,Pagination]"
      >
        <swiper-slide v-for="item in sliderList" v-slot="{isActive}">
          <div class="w-full h-full rounded-[20px] overflow-hidden">
            <video
              class="w-full h-full object-contain 3xl:scale-101"
              autoplay
              muted
              playsinline
              preload="auto"
              :src="item"
              @ended="isActive&&swiperInstance?.slideNext()"
            >
            </video>
          </div>
        </swiper-slide>
      </Swiper>
    </div>
    <div class="mt-[46px] mb-[40px]">
      <ul class="flex gap-[1vw] justify-center items-center pagination transition"></ul>
    </div>
  </div>
</template>

<style scoped>
:deep(.normal) {
  @apply w-[1vw] h-[1vw] bg-[rgba(24,173,37,0.1)] rounded-full transition-all duration-500 cursor-pointer
}

:deep(.active) {
  @apply w-[2vw] h-[9px] bg-primary-color rounded-[20px]
}
</style>

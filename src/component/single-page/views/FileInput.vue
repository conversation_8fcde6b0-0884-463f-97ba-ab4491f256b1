<script setup lang="ts">
import {getFilesOnDrop} from '@/utils/browser';
import {openFileDialog} from '@central/shared';

const props = defineProps<{
  accept?: string,
  multiple?: boolean,
  disabled?: boolean
}>()

const emit = defineEmits<{
  dragover: [ev: DragEvent]
  dragleave: [ev: DragEvent]
  change: [files: File[],from: 'drag' | 'click']
}>()

async function handleDropFiles(dataTransfer: DataTransfer) {
  if (props.disabled) {
    return
  }
  const files: File[] = await getFilesOnDrop(dataTransfer)
  if (files.length > 0) {
    emit('change', Array.from(files), 'drag')
  }
}

async function handleClickSelectFile() {
  if (props.disabled) {
    return
  }
  const files = await openFileDialog(props.accept ?? '', {
    multiple: props.multiple ?? false,
  })
  if (files && files.length > 0) {
    emit('change', Array.from(files), 'click')
  }
}

function handleDragover(ev: DragEvent) {
  if (props.disabled) {
    return
  }
  emit('dragover', ev)
}

function handleDragleave(ev: DragEvent) {
  if (props.disabled) {
    return
  }
  emit('dragleave', ev)
}

defineExpose({
  handleClickSelectFile: handleClickSelectFile
})

</script>

<template>
  <div
    @dragover.prevent="handleDragover($event)"
    @dragleave.prevent="handleDragleave($event)"
    @drop.prevent="handleDropFiles($event.dataTransfer!)"
  >
    <slot/>
  </div>
</template>

<style scoped>

</style>

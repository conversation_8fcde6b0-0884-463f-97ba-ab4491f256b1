import {createFileToTextTaskApi, queryFileToTextEquity} from '@/api/speechToTextMobileApi';
import {CreateAudioToTextTaskParams} from '@/api/audioToTextOnlineApi';
import {SpeechToTextEquity} from '@/interface/SpeechToText';
import BaseSpeechToTextModel from '@/component/single-page/speech-to-text-sing-page/BaseSpeechToTextModel';


class SpeechToTextMobileModel extends BaseSpeechToTextModel {
  private static _instance: SpeechToTextMobileModel

  static get instance(): SpeechToTextMobileModel {
    if (!SpeechToTextMobileModel._instance) {
      SpeechToTextMobileModel._instance = new SpeechToTextMobileModel()
    }
    return SpeechToTextMobileModel._instance
  }

  public creatFileToTextTaskApi(params: CreateAudioToTextTaskParams): Promise<string> {
    return createFileToTextTaskApi(params)
  }

  public queryEquityApi(deviceId: string): Promise<SpeechToTextEquity> {
    return queryFileToTextEquity(deviceId)
  }

  public getDeviceIdKey(): string {
    return '_Secure-STTD';
  }
}


export default SpeechToTextMobileModel

<script setup lang="ts">

import FileInput from '@/component/single-page/views/FileInput.vue';
import EnableSpeakerCheckbox from '@/component/single-page/audio-to-text-online/views/EnableSpeakerCheckbox.vue';
import VipIcon from '@/component/online-edit/text-to-speech-v2/icons/VipIcon.vue';
import dragIcon from '@/assets/images/audio-to-text-online/drag-icon.svg'

import {useI18n} from '@/locales';
import {computed, nextTick, reactive, ref, watch} from 'vue';
import GradientProgress from '@/component/youtube/views/GradientProgress.vue';
import useStartSelectFile from '@/component/online-edit/speech-to-text/start-select-file/useStartSelectFile';
import {queryAudioToTextTaskApi, uploadFileToAutoSpaceApi} from '@/api/audioToTextOnlineApi';
import useLoginService from '@/services/useLoginService';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import TextResult from '@/component/single-page/audio-to-text-online/TextResult.vue';
import {SrtWithSpeaker} from '@/interface/AudioToTextOnline';
import SelectFileBtn from '@/component/single-page/audio-to-text-online/views/SelectFileBtn.vue';
import showYoutubeDialog from '@/component/youtube/views/dialog/showYoutubeDialog';
import useAudioToTextOnline from '@/component/single-page/audio-to-text-online/useAudioToTextOnline';
import {AddFileErrorMsg, CheckFileItem, RealStartFileItemType} from '@/interface/SpeechToText';
import CreateTaskError from '@/services/exception/CreateTaskError';
import {notEnoughPointCode} from '@/services/constants';
import {isClient} from '@vueuse/core';
import {nanoid} from 'nanoid';
import AudioToTextTrack from '@/track/AudioToTextTrack';
import useTrackInfo from '@/hooks/utils/useTrackInfo';
import PricingDialog from '@/render/PricingDialog';
import useProductBuy from '@/component/vip/buy/useProductBuy';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {t} = useI18n();
const {setTrackByTaskId, getTaskCompleteTotalDuration} = useTrackInfo()
const {getEnable3DayItem, fetchProductData} = useProductBuy();
const {supportFile, fileToCheckFileItem, checkFilterFiles} = useStartSelectFile()
const {isLoginRef, checkLimitHasAvailable, pointCountRef, doLogin, isVipRef} = useLoginService()
const {
  showErrorDialog,
  showEquityWithNoLoginDialog,
  showFailedDialogWithOptions,
  showCountLimitDialog,
  showStorageLimitDialog,
  showPointNotEnoughWithLoginDialog,
  pageModel,
} = useAudioToTextOnline()

function getDeviceId() {
  const deviceKey = pageModel.getDeviceIdKey()
  const cacheId = localStorage.getItem(deviceKey)
  if (cacheId) {
    return cacheId
  } else {
    const id = nanoid()
    localStorage.setItem(deviceKey, id)
    return id
  }
}

let controller = new AbortController()
const fileInputRef = ref<InstanceType<typeof FileInput>>()
const loadingElId = 'audio-to-text-loading-id'

const equityInfo = reactive({
  limit: 0,
  used: 0,
  price: 0,
  // 每天限制免费次数对应的时长 s
  limitDuration: 0,
  async fetchEquityInfo() {
    const result = await pageModel.queryEquityApi(getDeviceId())
    equityInfo.limit = result.limit
    equityInfo.used = result.used
    equityInfo.price = result.price
    equityInfo.limitDuration = result.limit_duration
  }
})

if (isClient) {
  watch(isLoginRef, () => {
    equityInfo.fetchEquityInfo()
  }, {immediate: true})
}

const info = reactive({
  _viewStateRef: 'start' as 'start' | 'uploading' | 'taskLoading' | 'text',
  get viewStateRef() {
    return this._viewStateRef
  },
  set viewStateRef(value) {
    if (value === 'start') {
      this.uploadProgress = 0
      this.fileTitle = ''
      this.result.summaryText = ''
      this.result.srtList = ''
      this.result.rawText = ''
    }
    this._viewStateRef = value
  },
  enableSpeaker: false,
  uploadProgress: 0,
  fileTitle: '',
  result: {
    summaryText: '',//摘要
    srtList: '' as SrtWithSpeaker[] | string,//润色
    rawText: '' as SrtWithSpeaker[] | string,//原文
  },
})

async function startUploadFile(single: CheckFileItem) {
  let item: RealStartFileItemType | null = null
  try {
    info.uploadProgress = 0
    info.viewStateRef = 'uploading'
    controller = new AbortController()
    info.fileTitle = single.filename
    const dataList = await uploadFileToAutoSpaceApi([single], {
      onProgress(progress) {
        info.uploadProgress = progress
      },
      signal: controller.signal,
    })
    item = dataList[0]
  } catch (e) {
    info.viewStateRef = 'start'
    console.error(e)
    //@ts-ignore
    if (e && e.name === 'cancel') {
      console.log('手动取消')
      return
    }

    const result = await showErrorDialog({
      to: `#${loadingElId}`,
      reason: AddFileErrorMsg.networkError,
    })
    if (result === 'ok') {
      return startUploadFile(single)
    } else {
      return
    }
  }
  if (!item) {
    return
  }
  if (controller.signal.aborted) {
    //手动取消
    return
  }
  AudioToTextTrack.uploadFileSuccess({
    filesize: item.filesize,
    format: item.format,
    time: item.time,
    filename: item.filename ?? '',
  }).then()

  const h = 1 * 60 * 60
  if (info.enableSpeaker) {
    //区分说话人最大1小时
    if (single.duration > h) {
      showYoutubeDialog(t('aiAudioToTextOnlineResultFailReason'), {
        to: `#${loadingElId}`,
        onDialogClose() {
          info.viewStateRef = 'start'
        }
      })
      return
    }
  }

  await equityInfo.fetchEquityInfo()

  const isFreeDuration = Math.floor(item.time) <= equityInfo.limitDuration
  //视频的分钟数 不足一分钟算1分钟
  const min = Math.ceil(Math.floor(item.time) / 60)
  //区分说话人2点
  const price = info.enableSpeaker ? 2 : equityInfo.price
  //一分钟一点
  const cost = min * price
  let freeDuration = 0


  //是否有试用次数
  const checkExistTryTime = () => equityInfo.used < equityInfo.limit

  if (isLoginRef.value === false) {
    if (checkExistTryTime()) {
      if (isFreeDuration) {
        freeDuration = item.time
      } else {
        const result = await showEquityWithNoLoginDialog({
          to: `#${loadingElId}`,
          duration: equityInfo.limitDuration
        })
        if (result === 'close') {
          info.viewStateRef = 'start'
          return
        } else if (result === 'cancel') {
          //试用1分钟
          freeDuration = equityInfo.limitDuration
        } else if (result === 'ok') {
          if (isLoginRef.value === false) {
            try {
              await doLogin()
            } catch (e) {
              info.viewStateRef = 'start'
              return
            }
            await equityInfo.fetchEquityInfo()
          }
        }
      }

    } else {
      AudioToTextTrack.pointNotEnoughDialog().then()
      await fetchProductData()
      const getOkText = () => {
        if (isVipRef.value) {
          return t('pricingBuyNowKey')
        }
        return getEnable3DayItem() ? t('priceKey3DaysFreeTitle') : t('aiSubtitleUpgradeUser')
      }
      const result = await showFailedDialogWithOptions({
        title: t('aiAudioToTextOnlineResultFailReason1').replace('#', '1'),
        to: `#${loadingElId}`,
        cancelText: t('speechAudioCancel'),
        okText: getOkText(),
      })
      info.viewStateRef = 'start'
      if (result) {
        const result = await PricingDialog.showOnlyVipDialog({
          source: PricingDialog.source.pointNotEnough,
        })
        if (result === 'success') {

        } else {
          return
        }
      }
      return
    }

  }

  if (isLoginRef.value === true) {
    if (checkExistTryTime() || pointCountRef.value >= cost) {
      freeDuration = item.time
    } else {
      const result = await showPointNotEnoughWithLoginDialog(cost, `#${loadingElId}`)
      AudioToTextTrack.pointNotEnoughDialog().then()
      if (result === 'ok') {
        if (isVipRef.value) {
          PricingDialog.showVipWithPointDialog().then()
        } else {
          PricingDialog.showOnlyVipDialog({
            source: PricingDialog.source.pointNotEnough,
          }).then()
        }
      }
      info.viewStateRef = 'start'
      return
    }
  }


  AudioToTextTrack.realStartConvert({
    filesize: item.filesize,
    format: item.format,
    time: item.time,
    identify_speaker: Number(info.enableSpeaker),
  }).then()
  await doStartTask()

  async function doStartTask() {
    let taskId = ''
    try {
      info.viewStateRef = 'taskLoading'
      info.uploadProgress = 0
      taskId = await pageModel.creatFileToTextTaskApi({
        uniqid: item!.uniqid,
        enableSpeaker: info.enableSpeaker,
        freeDuration: freeDuration,
        deviceId: getDeviceId(),
      })
      setTrackByTaskId(taskId)
      const instance = new SingleTaskUtil({
        api: () => queryAudioToTextTaskApi(taskId),
      })
      const data = await instance.startQuery({
        onProgress(progress) {
          info.uploadProgress = progress
        },
      })
      const getText = () => {
        return data.contents?.map(item => item.text).join(',') || data.content
      }
      AudioToTextTrack.convertResult({
        result: 'success',
        filesize: item?.filesize,
        format: item?.format,
        time: item?.time,
        filename: item?.filename,
        completeDuration: getTaskCompleteTotalDuration(taskId),
        text: getText().substring(0, 1000),
        identify_speaker: Number(info.enableSpeaker),
      }).then()
      equityInfo.fetchEquityInfo()
      info.viewStateRef = 'text'
      if (info.enableSpeaker) {
        info.result.srtList = data.paragraphs
        info.result.rawText = data.contents
      } else {
        info.result.srtList = data.paragraph
        info.result.rawText = data.content
      }
      info.result.summaryText = data.summary
    } catch (e) {
      let errorStatus = ''

      if (e instanceof CreateTaskError) {
        const s = e.payload?.status
        if (s) {
          errorStatus = s.toString()
        }
      } else if (e instanceof QueryTaskError) {
        const s = e.payload?.state?.toString()
        if (s) {
          errorStatus = s.toString()
        }
      }

      if (e instanceof CreateTaskError && e.payload?.status?.toString() === notEnoughPointCode.toString()) {
        AudioToTextTrack.pointNotEnoughDialog().then()
        const result = await showPointNotEnoughWithLoginDialog(cost, `#${loadingElId}`)
        info.viewStateRef = 'start'
        if (result === 'ok') {
          if (isVipRef.value) {
            PricingDialog.showVipWithPointDialog().then()
          } else {
            PricingDialog.showOnlyVipDialog({
              source: PricingDialog.source.pointNotEnough
            }).then()
          }
        }
        return
      }

      if (e instanceof TypeError) {
        //网络异常
        const retry = await showFailedDialogWithOptions({
          to: `#${loadingElId}`,
          title: t('dialogResultNetworkError'),
          okText: t('speechAudioRetry'),
          cancelText: t('speechAudioCancel'),
        })
        if (retry) {
          return doStartTask()
        }
        info.viewStateRef = 'start'
        return
      }

      AudioToTextTrack.convertResult({
        result: 'fail',
        reason: TaskErrorMsg.safeToJSON(e),
        message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
        status: errorStatus,
        task_id: taskId,
      }).then()

      //返回的是错误对象 {state: <0}
      const errorMap = {
        '-11': 'empty-audio-track',
        '-8': 'timeout',
        default: 'unknown'
      }
      //@ts-ignore
      const msg = errorMap[e.state] || errorMap.default
      if (msg === 'empty-audio-track') {
        //任务不能重试, 返回到选择文件
        info.viewStateRef = 'start'
        const enable = await showFailedDialogWithOptions({
          to: `#${loadingElId}`,
          title: t('dialogResultEmptyAudioTrackError'),
          okText: t('aiSubtitleFindReplace'),
          cancelText: t('speechAudioCancel'),
        })
        if (enable === true) {
          handleOpenSelectFileDialog()
        }
      } else if (msg === 'timeout') {
        const retry = await showFailedDialogWithOptions({
          to: `#${loadingElId}`,
          title: t('speechAudioFailedRetry'),
          okText: t('speechAudioRetry'),
          cancelText: t('speechAudioCancel'),
        })
        if (retry) {
          return doStartTask()
        }
        info.viewStateRef = 'start'
      } else if (msg === 'unknown') {
        const enable = await showFailedDialogWithOptions({
          to: `#${loadingElId}`,
          title: t('dialogResultFileError'),
          okText: t('aiSubtitleFindReplace'),
          cancelText: t('speechAudioCancel'),
        })
        if (enable === true) {
          handleOpenSelectFileDialog()
        }
        //任务不能重试, 返回到选择文件
        info.viewStateRef = 'start'
      }
    }
  }
}

async function handleSelectFile(files: File[], from: 'drag' | 'click') {
  AudioToTextTrack.uploadFile().then()
  info.viewStateRef = 'start'
  _scrollToInput()
  if (files.length === 0) {
    return
  }
  const items = await fileToCheckFileItem(files, from)
  const {list, reason} = checkFilterFiles(items)
  if (list.length === 0) {
    if (info.enableSpeaker && reason === AddFileErrorMsg.durationLimit) {
      //优先提示区分说话人限制
      showYoutubeDialog(t('aiAudioToTextOnlineResultFailReason'), {
        to: `#${loadingElId}`,
        onDialogClose() {
          info.viewStateRef = 'start'
        }
      })
    } else {
      showErrorDialog({
        to: `#${loadingElId}`,
        reason: reason,
      }).then()
    }
    return
  }

  if (isLoginRef.value === true) {
    const enableLimit = await checkLimitHasAvailable()

    if (enableLimit.countHasAvailable === false) {
      showCountLimitDialog(`#${loadingElId}`).then()
      return
    } else if (enableLimit.storageHasAvailable === false) {
      showStorageLimitDialog(`#${loadingElId}`).then()
      return
    }
  }
  //暂时只考虑单文件上传
  const single = list[0]
  const fileDuration = single.duration
  const h = 1 * 60 * 60
  if (info.enableSpeaker) {
    //区分说话人最大1小时
    if (fileDuration > h) {
      showYoutubeDialog(t('aiAudioToTextOnlineResultFailReason'), {
        to: `#${loadingElId}`,
        onDialogClose() {
          info.viewStateRef = 'start'
        }
      })
      return
    }
  }
  //成功获取是才校验
  if (fileDuration > 0) {
    await equityInfo.fetchEquityInfo()

    //视频的分钟数 不足一分钟算1分钟
    const min = Math.ceil(Math.floor(fileDuration) / 60)
    //区分说话人2点
    const price = info.enableSpeaker ? 2 : equityInfo.price
    //一分钟一点
    const cost = min * price

    //是否有试用次数
    const checkExistTryTime = () => equityInfo.used < equityInfo.limit
    if (checkExistTryTime() === true) {


    } else {
      if (isLoginRef.value === false) {
        await fetchProductData()
        const getOkText = () => {
          if (isVipRef.value) {
            return t('pricingBuyNowKey')
          }
          return getEnable3DayItem() ? t('priceKey3DaysFreeTitle') : t('aiSubtitleUpgradeUser')
        }
        const result = await showFailedDialogWithOptions({
          title: t('aiAudioToTextOnlineResultFailReason1').replace('#', '1'),
          to: `#${loadingElId}`,
          cancelText: t('speechAudioCancel'),
          okText: getOkText(),
        })
        info.viewStateRef = 'start'
        if (result) {
          PricingDialog.showOnlyVipDialog({
            source: PricingDialog.source.pointNotEnough,
          }).then()
        }
        return
      } else {
        if (pointCountRef.value < cost) {
          const result = await showPointNotEnoughWithLoginDialog(cost, `#${loadingElId}`)
          info.viewStateRef = 'start'
          if (result === 'close') {
            return
          } else if (result === 'cancel') {
            return
          } else if (result === 'ok') {
            if (isVipRef.value) {
              PricingDialog.showVipWithPointDialog().then()
            } else {
              PricingDialog.showOnlyVipDialog({
                source: PricingDialog.source.pointNotEnough
              }).then()
            }
            return
          }
        }

      }
    }
  }

  await startUploadFile(single)

}

function handleCancelUpload() {
  controller.abort('test')
  info.viewStateRef = 'start'
}

function _scrollToInput() {
  nextTick(() => {
    const el = document.querySelector<HTMLElement>(`#${loadingElId}`)
    if (el) {
      const top = el.getBoundingClientRect().top + window.scrollY;
      window.scrollTo({
        top: top - 180,
        behavior: 'smooth'
      })
    }
  })
}

function handleOpenSelectFileDialog() {
  fileInputRef.value?.handleClickSelectFile()
}

const canFreeUseRef = computed(() => {
  return equityInfo.limit > equityInfo.used
})
</script>

<template>
  <div class="flex flex-col w-[90vw] lg:w-[62.5vw] text-dark dark:text-white">

    <div class="flex-center mb-[20px] lg:mb-[64px]" v-if="info.viewStateRef==='text'">
      <SelectFileBtn class="w-fit !-mt-[20px]" @click="handleOpenSelectFileDialog"/>
    </div>

    <div
      :id="loadingElId"
      v-show="info.viewStateRef!=='text'"
      class="w-full h-[300px] lg:h-[500px] bg-[#FFF] shadow-[0px_6px_12px_0px_rgba(17,88,23,0.12)] dark:shadow-none dark:bg-[rgba(185,236,255,0.12)] rounded-[16px] backdrop-blur-[40px] p-[10px] lg:p-[30px] flex flex-col relative z-[10]"
    >
      <FileInput
        ref="fileInputRef"
        class="w-full flex-1 border border-dashed border-dark/60 dark:border-[#e5e7eb] rounded-inherit"
        :accept="supportFile.all.join(',')"
        :disabled="info.viewStateRef!=='start'&&info.viewStateRef!=='text'"
        @change="handleSelectFile"
      >
        <div class="flex-center size-full relative py-[10px]">
          <div class="flex-center flex-col text-dark dark:text-white size-full">
            <div class="flex-center flex-col size-full">
              <img class="hidden dark:block w-[38px] lg:w-[64px]" :src="dragIcon" alt="drag icon" draggable="false">
              <p
                :class="[info.viewStateRef === 'start'?'hidden dark:block':'']"
                class="mt-[16px] lg:mt-[24px] font-medium text-[14px] lg:text-[16px] text-center px-[10px] line-clamp-2"
              >
                {{ info.fileTitle || t('aiAudioToTextOnlineDragInputTitle') }}
              </p>

              <template v-if="info.viewStateRef === 'start'">
                <SelectFileBtn class="w-fit mt-0 dark:mt-[24px] dark:lg:mt-[36px]" @click="handleOpenSelectFileDialog"/>
              </template>


              <template v-else-if="info.viewStateRef === 'uploading'||info.viewStateRef==='taskLoading'">
                <div class="mt-[20px] lg:mt-[60px] w-[80%]">
                  <div class="w-full flex-center gap-[10px]">
                    <GradientProgress class="w-full h-[12px] " :progress="info.uploadProgress"/>

                    <button
                      v-if="info.viewStateRef==='uploading'"
                      class="size-[16px] bg-white/[0.7] rounded-full flex-center flex-[1_0_auto]"
                      @click="handleCancelUpload"
                    >
                      <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 7L1 1.16216" stroke="#2D2D33" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M1 7L6.83784 1" stroke="#2D2D33" stroke-width="1.5" stroke-linecap="round"/>
                      </svg>
                    </button>
                    <div v-else class="pl-[14px] w-[3em] flex-[1_0_auto]">
                      <span class="text-[16px] lg:text-[28px] font-semibold">{{ info.uploadProgress }}</span>
                      <span class="text-[12px] lg:text-[14px]">%</span>
                    </div>
                  </div>
                  <p
                    class="mt-[24px] text-center text-[12px] lg:text-[16px] whitespace-pre-line"
                    v-if="info.viewStateRef==='taskLoading'">
                    {{ t('aiYoutubeSummarizerTaskLoadingText') }}
                  </p>
                </div>

              </template>
              <div
                v-if="info.viewStateRef==='start'"
                class="block dark:hidden text-white/[0.4] mt-[16px] text-center px-[10px]"
              >
                <span class="text-dark/40 dark:text-[#FFF]/40 text-[10px] lg:text-[14px]">
                  {{ t('aiAudioToTextOnlineSupportFmt') }}
                </span>
              </div>
            </div>

            <div
              v-if="info.viewStateRef==='start'"
              class="hidden dark:block text-white/[0.4] dark:self-end lg:mr-[20px] lg:mb-[20px] px-[10px] pb-[10px] lg:pb-[0px]"
            >
              <span class="text-dark/40 dark:text-[#FFF]/40 text-[10px] lg:text-[14px]">
                {{ t('aiAudioToTextOnlineSupportFmt') }}
              </span>
            </div>
          </div>

        </div>
      </FileInput>
      <div
        class="flex flex-col lg:flex-row justify-between items-center mt-[10px] lg:mt-[20px]"
        :class="[info.viewStateRef==='taskLoading'?'pointer-events-none opacity-40':'']"
      >
        <EnableSpeakerCheckbox class="text-[14px]" v-model="info.enableSpeaker"/>

        <div
          class="text-dark/50 dark:text-white/[0.5] text-[12px] flex-center"
          :class="[isClient&&canFreeUseRef?'invisible':'visible']"
        >
          <span>{{ t('aiAudioToTextOnlineEnableSpeakerDesc') }}</span>
          <VipIcon class="mx-1"/>
          <span class="block w-2">{{ info.enableSpeaker ? 2 : 1 }}</span>
        </div>
      </div>

    </div>

    <div
      v-if="info.viewStateRef==='text'"
      class="w-full min-h-[500px] bg-white dark:bg-[rgba(185,236,255,0.12)] rounded-[16px] backdrop-blur-[40px] p-[10px] lg:p-[30px] flex flex-col"
    >
      <TextResult
        :title="info.fileTitle"
        :summary-text="info.result.summaryText"
        :srt-list="info.result.srtList"
        :raw-text="info.result.rawText"
      />
    </div>
  </div>
</template>

<style scoped>
:deep(.youtube-dialog-container) {
  position: absolute !important;
}
</style>

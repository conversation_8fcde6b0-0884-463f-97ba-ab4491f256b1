<script setup lang="ts">
import {useI18n} from '@/locales';
import AudioToTextTrack from '@/track/AudioToTextTrack';

const {t} = useI18n();

const model = defineModel({
  default: false,
})

function handleToggleModel() {
  model.value = !model.value
  AudioToTextTrack.clickIdentifySpeaker()
}

</script>

<template>
  <div class="flex items-center">
    <div class="text-dark dark:text-white">
      <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="7.22539" cy="4.825" r="3.825" stroke="currentColor"/>
        <path
          d="M1.00232 15.1406C0.939815 13.0156 2.11981 8.76562 7.33981 8.76562C12.1023 8.76562 13.6898 13.0156 13.6023 15.1406"
          stroke="currentColor" stroke-linecap="round"/>
        <path d="M15.5801 2.95312C16.5004 4.00391 17.0004 7.00391 15.5801 8.42812" stroke="currentColor"
              stroke-linecap="round"/>
        <path d="M13.3301 4.14844C13.6176 4.58594 14.5004 6 13.3301 7.22344" stroke="currentColor"
              stroke-linecap="round"/>
      </svg>
    </div>
    <div class="ml-[8px] text-[12px] lg:text-[14px]"> {{ t('aiAudioToTextOnlineEnableSpeakerTitle') }}</div>

    <div class="ml-[12px] lg:ml-[24px]">
      <button
        class="w-[40px] h-[20px] border rounded-[22px] p-[3px] transition relative"
        :class="[model?'border-theme bg-theme':'border-[#CDCDCD]']"
        @click="handleToggleModel"
      >
        <span
          class="size-[12px] bg-dark/60 dark:bg-white rounded-full block transition-transform duration-300"
          :class="[model?'translate-x-[20px] !bg-white':' translate-x-0']"
        ></span>
      </button>

    </div>
  </div>
</template>

<style scoped>
</style>

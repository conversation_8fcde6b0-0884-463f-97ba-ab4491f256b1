<template>
  <div class="hue" @mousedown.prevent.stop="selectHue" ref="hueElRef">
    <canvas ref="canvasHue"/>
    <div :style="slideHueStyle" class="slide"/>
  </div>
</template>

<script lang="ts" setup>

import {onMounted, ref} from 'vue'
import {HSVType} from '@/component/test/test/ColorPicker.vue'

const props = defineProps<{
  hsv: HSVType,
  width: number
  height: number
}>()

const emit = defineEmits<{
  selectHue: [{ r: number, g: number, b: number }]
}>()

const slideHueStyle = ref({})

const hueElRef = ref()

const canvasHue = ref()


onMounted(() => {
  renderColor()
  renderSlide()
})

function renderColor() {
  const canvas: any = canvasHue.value
  const width = props.width
  const height = props.height
  const ctx = canvas.getContext('2d')
  canvas.width = width
  canvas.height = height

  const gradient = ctx.createLinearGradient(0, 0, 0, height)
  gradient.addColorStop(0, '#FF0000') // red
  gradient.addColorStop(0.17 * 1, '#FF00FF') // purple
  gradient.addColorStop(0.17 * 2, '#0000FF') // blue
  gradient.addColorStop(0.17 * 3, '#00FFFF') // green
  gradient.addColorStop(0.17 * 4, '#00FF00') // green
  gradient.addColorStop(0.17 * 5, '#FFFF00') // yellow
  gradient.addColorStop(1, '#FF0000') // red
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
}

function renderSlide() {
  slideHueStyle.value = {
    top: (1 - props.hsv.h / 360) * props.height - 2 + 'px',
  }
}

function selectHue(e: any) {
  const {top: hueTop} = hueElRef.value.getBoundingClientRect()
  const ctx = e.target.getContext('2d')

  const mousemove = (e: any) => {
    let y = e.clientY - hueTop

    if (y < 0) {
      y = 0
    }
    if (y > props.height) {
      y = props.height
    }

    slideHueStyle.value = {
      top: y - 2 + 'px',
    }
    // If you use the maximum value, the selected pixel will be empty, and the empty default is black
    const imgData = ctx.getImageData(0, Math.min(y, props.height - 1), 1, 1)
    const [r, g, b] = imgData.data
    emit('selectHue', {r, g, b})
  }

  mousemove(e)

  const mouseup = () => {
    document.removeEventListener('mousemove', mousemove)
    document.removeEventListener('mouseup', mouseup)
  }

  document.addEventListener('mousemove', mousemove)
  document.addEventListener('mouseup', mouseup)
}

</script>

<style lang="scss">
.hue {
  position: relative;
  margin-left: 8px;
  cursor: pointer;

  .slide {
    position: absolute;
    left: 0;
    top: 100px;
    width: 100%;
    height: 4px;
    background: #fff;
    box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.3);
    pointer-events: none;
  }
}
</style>

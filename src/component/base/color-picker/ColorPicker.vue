<script setup lang="ts">
import {computed, ref} from 'vue'
import {rgb2hex, setColorValue} from '@/component/test/test/composible'
import Saturation from '@/component/test/test/Saturation.vue'

const props = defineProps<{
  hexColor: string,
  alpha: number,
}>()
const emit = defineEmits<{
  'update:hexColor': [color: string]
  'update:alpha': [alpha: number]
}>()


export type HSVType = {
  h: number,
  s: number,
  v: number,
}

export type RGBType = {
  r: number,
  g: number,
  b: number,
}

const colorElRef = ref<InstanceType<typeof Saturation>>()

//#000000色值
const hexColorRef = ref(props.hexColor)

//hsv色值
const hsvRef = computed(() => {
  return setColorValue(hexColorRef.value) as HSVType
})

const handleUpdateColor = (c: RGBType) => {
  hexColorRef.value = rgb2hex(c, true)
  emit('update:hexColor', hexColorRef.value)
}
const alphaRef = computed({
  get() {
    return props.alpha
  },
  set(value) {
    emit('update:alpha', value)
  },
})
</script>

<template>
  <div class="px-[16px] mt-[10px]">
    <div class="flex">
      <Saturation
        ref="colorElRef"
        :color="hexColorRef"
        :hsv="hsvRef"
        :size="{width:220,height:165}"
        v-model:alpha="alphaRef"
        @select-saturation="handleUpdateColor"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>

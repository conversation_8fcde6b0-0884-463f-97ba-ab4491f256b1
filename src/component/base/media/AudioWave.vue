<!--音频波形-->
<script setup lang="ts">
import {onMounted, ref} from 'vue'

const props = defineProps<{
  src: MediaStream,
  width: number | string,
  height: number | string,
}>()

const canvasElRef = ref<HTMLCanvasElement>()
let isInit = false;
let dataArray: Uint8Array | null = null;
let analyser: AnalyserNode | null = null;

onMounted(() => {
  const ctx = canvasElRef.value!.getContext('2d')!

  function initAudioContext() {
    if (isInit) {
      return
    }
    const audioCtx = new AudioContext();
    const source = audioCtx.createMediaStreamSource(props.src)
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 512;
    dataArray = new Uint8Array(analyser.frequencyBinCount);
    source.connect(analyser);
    // analyser.connect(audioCtx.destination);
    isInit = true;
  }

  if (props.src) {
    initAudioContext()
  }

  function drawV2() {
    const cvs = canvasElRef.value!
    const barWidth = 6;
    requestAnimationFrame(drawV2);
    if (!cvs) return
    ctx.clearRect(0, 0, cvs.width, cvs.height);
    if (!isInit) {
      return
    }
    if (!dataArray) {
      return;
    }

    analyser!.getByteFrequencyData(dataArray!);

    //获取dataArray的平均值
    const avg = dataArray.reduce((acc, cur) => acc + cur, 0) / dataArray.length
    const barHeight = Math.floor(avg / 60 * cvs.height);

    const list = [0.2, 0.6, 0.4, 1, 0.4, 0.6, 0.2, 0.6, 0.4, 1, 0.4, 0.6, 0.2]
    for (let i = 0; i < list.length; i++) {
      ctx.fillStyle = `rgba(24, 173, 37, ${i % 2 === 0 ? '0.5' : '1'})`;
      const x1 = barWidth * i;
      const y = cvs.height - barHeight * list[i];
      // 直角矩形
      // ctx.fillRect(x1, y / 2, barWidth - 3, barHeight === 0 ? 2 : barHeight * list[i])

      const rectX = x1; // 矩形的起始x坐标
      const rectY = y / 2; // 矩形的起始y坐标
      const width = barWidth - 3; // 矩形的宽度
      const height = barHeight === 0 ? 2 : barHeight * list[i]; // 矩形的高度
      const radius = 1; // 圆角的半径
      ctx.beginPath();
      ctx.moveTo(rectX + radius, rectY);
      ctx.arcTo(rectX + width, rectY, rectX + width, rectY + radius, radius);
      ctx.arcTo(rectX + width, rectY + height, rectX + width - radius, rectY + height, radius);
      ctx.arcTo(rectX, rectY + height, rectX, rectY + height - radius, radius);
      ctx.arcTo(rectX, rectY, rectX + radius, rectY, radius);
      ctx.closePath(); // 可选步骤，闭合路径
      ctx.fill();
    }
  }

  drawV2();

  // function draw() {
  //   const cvs = canvasElRef.value!
  //   const len = 6
  //   const barWidth = 6;
  //   requestAnimationFrame(draw);
  //   if (!cvs) return
  //   ctx.clearRect(0, 0, cvs.width, cvs.height);
  //   if (!isInit) {
  //     return
  //   }
  //   if (!dataArray) {
  //     return;
  //   }
  //
  //   analyser!.getByteFrequencyData(dataArray!);
  //
  //   const result: Array<Array<number>> = []
  //   const step = Math.ceil(dataArray.length / (len * 2))
  //   dataArray.forEach((item, index) => {
  //     const i = Math.floor(index / step)
  //     if (!result[i]) {
  //       result[i] = []
  //     }
  //     result[i].push(item)
  //   })
  //
  //   for (let i = 0; i < len; i++) {
  //     ctx.fillStyle = `rgba(24, 173, 37, ${i % 2 === 0 ? '1' : '1'})`;
  //     const data = result[i].reduce((acc, cur) => acc + cur, 0) / result[i].length;
  //     const barHeight = Math.floor(data / 200 * cvs.height);
  //     const x1 = barWidth * i + cvs.width / 2;
  //     const x2 = cvs.width / 2 - (i + 1) * barWidth;
  //     const y = cvs.height - barHeight;
  //     // console.log(x1, y / 2, barWidth, barHeight)
  //     ctx.fillRect(x1, y / 2, barWidth - 2, barHeight === 0 ? 2 : barHeight);
  //     ctx.fillRect(x2, y / 2, barWidth - 2, barHeight === 0 ? 2 : barHeight);
  //   }
  // }
  //
  // draw();
})

</script>

<template>
  <div class="flex justify-center items-center">
    <canvas ref="canvasElRef" :width="width" :height="height"></canvas>
  </div>
</template>

<style scoped lang="scss">

</style>

<script setup lang="ts">

import GradientButton from '@/component/base/button/GradientButton.vue'
import OutlineButton from '@/component/base/button/OutlineButton.vue'
import TheCheckBox from '@/component/base/checkbox/TheCheckBox.vue'
import {reactive, ref} from 'vue'
import TheFilterDropdown from '@/component/base/dropdown/TheFilterDropdown.vue';
import TheAudio from '@/component/base/media/TheAudio.vue';
import {useDisplayMedia} from '@vueuse/core';
import RecorderUtil from '@/utils/RecorderUtil';
//@ts-ignore
import {TestComp} from './Test.tsx'

const checkboxRef = ref(true)

const dropdownInfo = reactive({
  list: new Array(10).fill(0).map((item, index) => index),
  selectItem: 0
})

const videoElRef = ref<HTMLVideoElement>()
const {start, stop, enabled} = useDisplayMedia({
  video: true,
  audio: true
})

const instance = new RecorderUtil()

instance.onStop = (blob) => {
  open(URL.createObjectURL(blob))
  console.log('onStop', blob)
}

async function handeClickStart() {
  const stream = await start()
  videoElRef.value!.srcObject = stream!
  instance.start(stream!)
}

async function handleClickStop() {
  stop()
  instance.stop()
}

const countRef = ref(0)

function handleClickTest() {
  countRef.value++
}
</script>

<template>
  <button class="outline rounded" @click="handeClickStart" v-if="!enabled">
    start
  </button>

  <div v-else @click="handleClickStop">
    <video class="size-[500px]" controls muted autoplay playsinline loop ref="videoElRef"></video>
    <p>{{instance.duration}}</p>
  </div>
  <main>
    <h1>UI Test</h1>
    <section class="w-max">
      <h2>Button</h2>
      <GradientButton class="w-[200px] h-[40px]">
        <span>测试</span>
      </GradientButton>
      <OutlineButton class="w-[200px] h-[40px]">
        <span>测试</span>
      </OutlineButton>
    </section>

    <section>
      <h2>CheckBox</h2>
      <div class="flex items-center w-fit cursor-pointer" @click="checkboxRef=!checkboxRef">
        <TheCheckBox :is-active="checkboxRef"/>
        点我切换
      </div>
    </section>

    <section class="w-[200px]">
      <h2>Dropdown</h2>
      <TheFilterDropdown
        :select-list="dropdownInfo.list"
        :select-title="dropdownInfo.selectItem.toString()"
        list-class="w-[200px] max-h-[200px]"
      >
        <template #item="{item}">
          <div class="h-[40px] flex items-center">this is {{ item }}</div>
        </template>
      </TheFilterDropdown>
    </section>

    <section>
      <TheAudio
        class="w-[400px] h-[40px]"
        show-adjust-speed
        src="https://qncdnjs.aoscdn.com/local/lightmv.cn/dist/static/media/banner-video.ffbacb0f.mp4"
      />
    </section>

  </main>
  <TestComp :msg="countRef" @click="handleClickTest">
    <p>这里是测试</p>
    <template v-slot:test="test">
      <span>template {{ test}}</span>
    </template>
  </TestComp>

</template>

<style scoped lang="scss">
section {
  @apply border space-y-[20px]
}
</style>

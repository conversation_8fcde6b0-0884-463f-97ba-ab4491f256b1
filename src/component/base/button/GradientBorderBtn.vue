<script setup lang="ts">
import {computed, useSlots} from 'vue';

const props = defineProps<{
  bgBorderClass?: string
  //边框宽度
  gradientBorderSize?: string | number
  //渐变背景的圆角
  bgBorderRadius?: string | number
}>()

const slots = useSlots()

const bgBorderRadiusRef = computed(() => {
  if (props.bgBorderRadius === undefined) {
    const className = slots.default?.()?.[0]?.props?.class
    //匹配 rounded-[20px]
    const match = className?.match(/rounded-\[(.*?)]/);
    if (match) {
      return match[1];
    }

    return '0px'
  }

  if (typeof props.bgBorderRadius === 'number') {
    return `${props.bgBorderRadius}px`
  }

  return props.bgBorderRadius.endsWith('px') ? props.bgBorderRadius : `${props.bgBorderRadius}px`
})

const gradientBorderSizeRef = computed(() => {
  return props.gradientBorderSize ?? 1
})

</script>

<template>
  <div class="relative w-full">
    <span
      class="absolute -left-[var(--border-size)] -top-[var(--border-size)] size-[calc(var(--border-size)*2+100%)] z-[0] bg-gradient-to-r from-[#D6FB72] to-[#76F9B1]"
      :class="props.bgBorderClass"
      :style="{
        borderRadius: bgBorderRadiusRef,
        ['--border-size']: gradientBorderSizeRef + 'px'
      }"
    ></span>
    <div class="relative">
      <slot/>
    </div>

  </div>
</template>

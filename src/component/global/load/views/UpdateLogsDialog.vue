<script setup lang="ts">
import BaseDialog from '@/component/dialog/BaseDialog.vue'
import logoIcon from '@/assets/images/logo.svg'
import {Swiper, SwiperSlide} from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/pagination';
import {Autoplay, Pagination} from 'swiper'
import {computed, ref} from 'vue';
import UpdateLogsBgIcon from '@/component/global/load/views/UpdateLogsBgIcon.vue';

const props = defineProps<{
  title: string,
  subtitle: string,
  dateTitle: string,
  logsTitle: string,
  bannerUrlList: string[],
  logContentHTML: string,
  class?: string | string[]
  id?: string
}>()

const emits = defineEmits<{
  clickClose: [],
}>()

const activeIndexRef = ref(0)

const transformEmojiTHMLRef = computed(() => {
  let html = props.logContentHTML
  const emojiMap = new Map(Object.entries({
    '[new-feature]': '🌟',
    '[new-design]': '💎',
    '[bug-fix]': '🐞',
    '[gift]': '🎁',
    '[new-tag]': '<i class="inline-block leading-[12px] text-[12px] not-italic bg-gradient-to-r from-[#8094ff] to-[#9542ff] text-white rounded-t-[9px] rounded-r-[9px] p-[3px_5px] h-[18px]">NEW</i>',
    '[hot-tag]': '<i class="inline-block leading-[12px] text-[12px] not-italic bg-gradient-to-r from-[#f11313] to-[#f11313] text-white rounded-t-[9px] rounded-r-[9px] p-[3px_5px] h-[18px]">HOT</i>'
  }))

  emojiMap.forEach((value, key) => {
    html = html.replace(key.trim(), value.trim())
  })

  return html
})

</script>

<template>
  <BaseDialog :enable-teleport="false">
    <div class="bg-white rounded-[8px] w-[640px] relative" :id="id" :class="props.class">
      <div class="absolute left-0 top-0 w-full h-auto">
        <UpdateLogsBgIcon/>
      </div>

      <div class="size-full relative p-[40px] z-10">

        <!--关闭按钮-->
        <div class="absolute right-[20px] top-[20px] cursor-pointer" @click="$emit('clickClose')">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M1.00049 10.999L10.9995 1" stroke="#666666" stroke-width="1.5" stroke-linecap="round"/>
            <path d="M1 1L11 10.997" stroke="#666666" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </div>

        <!--标题-->
        <div class="flex items-center gap-[16px]">
          <img class="size-[48px]" :src="logoIcon" alt="logo">

          <div class="flex flex-col">
            <p class="text-[16px] text-dark font-medium">{{ title }}</p>
            <p class="text-[#999] text-[14px]">{{ subtitle }}</p>
          </div>

        </div>

        <!--更新日志+时间-->
        <div class="flex justify-between items-center mt-[30px]">
          <p class="text-dark font-medium">{{ logsTitle }}</p>
          <p class="text-[12px] text-[#999]">{{ dateTitle }}</p>
        </div>

        <div
          v-if="bannerUrlList.length>0"
          class="mt-[24px] border border-theme/[0.3] rounded-[8px] overflow-hidden relative aspect-video"
        >
          <Swiper
            class="h-full w-full "
            :slides-per-view="1"
            :space-between="20"
            :loop="true"
            :autoplay="true"
            :pagination="bannerUrlList.length>1&&{
              clickable:true,
              enabled:true,
              bulletActiveClass:'active',
              bulletElement:'li',
              bulletClass:'normal',
            }"
            :modules="[Autoplay,Pagination]"
            @slide-change="sw=>activeIndexRef=sw.realIndex"
          >
            <swiper-slide v-for="item in bannerUrlList">
              <div class="w-full h-full rounded-[8px] *:size-full">
                <template v-if="item.endsWith('.mp4')">
                  <video class="object-cover" muted autoplay playsinline controls :src="item"></video>
                </template>
                <template v-else>
                  <img class="w-full object-contain" draggable="false" :src="item" alt="">
                </template>
              </div>
            </swiper-slide>
          </Swiper>

          <div
            v-if="bannerUrlList.length>1"
            class="absolute right-[16px] top-[16px] z-10 flex text-[12px]/[1] bg-black/[0.2] rounded-[10px] p-[4px_8px] text-white"
          >
            <span>{{ activeIndexRef + 1 }}</span>
            <span>/{{ bannerUrlList.length }}</span>
          </div>
        </div>

        <div
          class="mt-[20px] text-dark leading-[2em] max-h-[200px] overflow-y-auto text-[14px] font-medium"
          v-html="transformEmojiTHMLRef"
        >
        </div>

      </div>

    </div>
  </BaseDialog>
</template>

<style scoped>

:deep(.swiper-pagination) {
  @apply flex justify-center items-center gap-[4px];
}

:deep(.swiper-pagination li) {
  @apply size-[6px] rounded-full bg-black/[0.1] transition;
}

:deep(.swiper-pagination li.active) {
  @apply w-[12px] bg-theme;
}
</style>

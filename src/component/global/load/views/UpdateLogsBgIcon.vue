<template>
  <svg width="640" height="346" viewBox="0 0 640 346" fill="none" xmlns="http://www.w3.org/2000/svg">
    <mask id="mask0_965_650" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="640" height="346">
      <rect width="640" height="346" rx="8" fill="url(#paint0_linear_965_650)"/>
    </mask>
    <g mask="url(#mask0_965_650)">
      <g filter="url(#filter0_f_965_650)">
        <circle cx="45" cy="80" r="134" fill="#ADA718" fill-opacity="0.1"/>
      </g>
      <g filter="url(#filter1_f_965_650)">
        <circle cx="569" cy="22" r="134" fill="#1892AD" fill-opacity="0.15"/>
      </g>
      <g filter="url(#filter2_f_965_650)">
        <ellipse cx="303" cy="22" rx="212" ry="134" fill="#1DC924" fill-opacity="0.15"/>
      </g>
    </g>
    <defs>
      <filter id="filter0_f_965_650" x="-193" y="-158" width="476" height="476" filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="52" result="effect1_foregroundBlur_965_650"/>
      </filter>
      <filter id="filter1_f_965_650" x="331" y="-216" width="476" height="476" filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="52" result="effect1_foregroundBlur_965_650"/>
      </filter>
      <filter id="filter2_f_965_650" x="-13" y="-216" width="632" height="476" filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="52" result="effect1_foregroundBlur_965_650"/>
      </filter>
      <linearGradient id="paint0_linear_965_650" x1="320" y1="0" x2="320" y2="346" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFF1E6"/>
        <stop offset="1" stop-color="white"/>
      </linearGradient>
    </defs>
  </svg>

</template>


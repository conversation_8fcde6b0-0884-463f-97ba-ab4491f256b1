---
import {getLang} from "@central/shared";
import {getSitePathname} from "@central/i18n/utils";
import {useI18n} from '../../locales';
const {t} = useI18n();
const lang = getLang();
const cookiesPolicyHref = getSitePathname("/cookies-policy", Astro);
---

<div
  class="fixed bottom-[20px] z-[999] left-1/2 -translate-x-1/2 flex lg:flex-wrap lg:justify-center lg:py-[21px] lg:pr-[22px] lg:pl-[25px] lg:rounded-[18px] lg:max-w-[88%] lg:border | flex-wrap justify-start pt-[15px] px-[4%] pb-5 rounded-t-[10px] max-w-full w-[1240px] mx-auto bg-white border-primary-color items-center shadow-[0_3px_16px_0_rgba(141,140,163,0.3)] lg:mb-15px"
  id="cookies-win"
>
  <p
    class="lg:flex-1 lg:text-base | text-[14px] leading-6 w-full text-[#333] text-left"
  >
    {t("bannerCookieInfo")}
    <a class="text-theme cursor-pointer" href={cookiesPolicyHref}>
      {t("bannerCookieBtn")}
    </a>
    {lang === "jp" ? "をご覧ください。" : ""}
  </p>
  <button
    class="decline-btn hover:bg-theme hover:text-white lg:w-auto lg:mx-3 lg:mt-0 lg:px-6 lg:h-[50px] lg:leading-[50px] lg:text-[17px] | w-[48%] mx-auto mt-5 px-[3px] h-[45px] leading-[45px] text-[12px] block cursor-pointer min-w-[140px] border border-theme rounded-[25px] whitespace-nowrap text-center text-theme font-semibold"
  >
    {t("bannerDecline")}
  </button>
  <button
    id="accept-cookie-btn"
    class="accept-btn lg:w-auto lg:mx-3 lg:mt-0 lg:px-6 lg:h-[50px] lg:leading-[50px] lg:text-[17px] | w-[48%] mx-auto mt-5 px-[3px] h-[45px] leading-[45px] text-[12px] block cursor-pointer min-w-[140px] border border-theme bg-theme rounded-[25px] whitespace-nowrap text-center text-white font-semibold"
  >
    {t("bannerAccept")}
  </button>
</div>
<script is:inline>
  const dialogEl = document.querySelector('#cookies-win')
  const btns = dialogEl.querySelectorAll('.decline-btn,.accept-btn')
  if (btns && btns.length > 0) {
    btns.forEach(item => {
      item.onclick = closeCookiesWin
    })
  }
  const cookieKey = "accept-cookie";
  if (localStorage.getItem(cookieKey) === "true") {
    closeCookiesWin();
  }

  function closeCookiesWin() {
    localStorage.setItem(cookieKey, "true");
    dialogEl.remove();
  }
</script>

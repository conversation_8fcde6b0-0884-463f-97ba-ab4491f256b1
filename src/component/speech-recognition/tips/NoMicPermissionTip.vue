<script setup lang="ts">
import {useI18n} from '@/locales';

const {t} = useI18n()
const emit = defineEmits<{
  clickOk: []
}>()

const handleClose = () => {
  emit('clickOk')
}
</script>

<template>
  <div class="screen-recorder-container">
    <div class="not-allowed-tip tip-dialog-common" id="notAllowedTip" style="display: flex;">
      <div class="not-allowed-content dialog-content-common">
        <div class="not-allowed-title">{{ t('002791') }}</div>
        <div class="not-allowed-img not-audio-img">
          <div class="click-here-plz">{{ t('002792') }}</div>
        </div>
        <div class="just-chrome">{{ t('002793') }}</div>
        <div class="btn-common for-sure" id="hideNotAllowedTip" @click="handleClose">{{ t('002777') }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "@/style/recorder/record-screen-online.scss";

.screen-recorder-container {
  display: flex;
  z-index: 10001;
  background: none;
}

</style>

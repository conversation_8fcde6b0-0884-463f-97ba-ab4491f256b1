<script setup lang="ts">
import {SrtResultModel} from '@/interface/OnlineEdit';
import TimeStampModel
  from '@/component/online-edit/ai-subtitle-v2/subtitle-operation/subtitle/timestamp/TimeStampModel';

const props = defineProps<{
  srtList: SrtResultModel[]
}>()

const formatTime = (timestamp: string) => {
  const model = TimeStampModel.toModel(parseInt(timestamp) / 1000);
  //如果时分秒中的时是0 则不显示时 仅显示分:秒 如果时不是0 则显示时:分:秒

  if (model.h === 0) {
    // 当小时为0时，只显示 分:秒
    return `${String(model.min).padStart(2, '0')}:${String(model.s).padStart(2, '0')}`;
  } else {
    // 当小时不为0时，显示 时:分:秒
    return `${String(model.h).padStart(2, '0')}:${String(model.min).padStart(2, '0')}:${String(model.s).padStart(2, '0')}`;
  }
}

// 判断是否是句子的结束标点（支持多语言）
const isEndOfSentence = (text: string): boolean => {
  if (!text || text.trim().length === 0) return false;

  const lastChar = text.trim().slice(-1);

  // 包含多种语言的句子结束标点：
  // 英语/拉丁语系: . ! ? ; :
  // 中文/日文/韩文: 。 ！ ？ ； ：
  // 阿拉伯语: ؟ ؛
  // 其他常见结束符号
  const endPunctuations = [
    '.', '!', '?', ';', ':',
    '。', '！', '？', '；', '：',
    '؟', '؛',
    '।', '॥', // 印地语
    '។', // 高棉语
    '။', // 缅甸语
  ];

  return endPunctuations.includes(lastChar);
}

// 检测文本是否以大写字母开头（适用于拉丁语系）
const startsWithCapital = (text: string): boolean => {
  if (!text || text.trim().length === 0) return false;
  const firstChar = text.trim()[0];
  return /[A-Z\u00C0-\u00DC\u0400-\u042F]/.test(firstChar); // 拉丁文、西里尔文等大写字母
}

// 判断是否是句子的开始（支持多语言）
const isStartOfSentence = (text: string): boolean => {
  if (!text || text.trim().length === 0) return false;

  const trimmedText = text.trim();
  const firstChar = trimmedText[0];

  // 多语言引号和开始标记
  const startMarks = [
    '"', '"', "'", "'", '«', '»', '„', '‟', '「', '」', '『', '』',
    '【', '】', '〈', '〉', '《', '》', '〔', '〕', '«', '»', '‹', '›',
    '-', '—', '–', '−', '～', '~'
  ];

  // 检查是否以引号或破折号等开始
  if (startMarks.includes(firstChar)) {
    return true;
  }

  // 检查是否以大写字母开头（适用于拉丁语系语言）
  if (startsWithCapital(text)) {
    return true;
  }

  // 检查是否以常见的连接词开头（多语言）
  // 这里列出了一些常见语言中表示转折、因果关系的词语
  const conjunctions = [
    // 英语
    /^(but|however|nevertheless|therefore|thus|hence|consequently|moreover|furthermore|in addition|besides|also|too|instead|otherwise|alternatively)/i,
    // 中文
    /^(但是|然而|不过|因此|所以|总之|首先|其次|最后|另外|此外|而且|并且)/,
    // 西班牙语
    /^(pero|sin embargo|por lo tanto|además|también|primero|segundo|finalmente)/i,
    // 法语
    /^(mais|cependant|donc|ainsi|en outre|également|premièrement|deuxièmement|enfin)/i,
    // 德语
    /^(aber|jedoch|deshalb|daher|außerdem|auch|erstens|zweitens|schließlich)/i,
    // 俄语 (转写)
    /^(но|однако|поэтому|кроме того|также|во-первых|во-вторых|наконец)/i,
    // 日语 (罗马字)
    /^(しかし|それでも|だから|したがって|また|さらに|まず|次に|最後に)/,
    // 阿拉伯语 (转写)
    /^(lakin|bisabab|li'an|aydan|awalan|thaniyan|akhiran)/i,
  ];

  // 检查是否以连接词开头
  return conjunctions.some(regex => regex.test(trimmedText));
}

// 计算两个时间戳之间的时间差（秒）
const getTimeDifference = (time1: string, time2: string): number => {
  return (parseInt(time2) - parseInt(time1)) / 1000;
}

// 估计语言类型（简单判断）
const estimateLanguageType = (text: string): 'cjk' | 'latin' | 'other' => {
  if (!text) return 'other';

  // 检查是否包含汉字、日文或韩文字符
  if (/[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f\u3131-\u318E\uAC00-\uD7A3]/.test(text)) {
    return 'cjk';
  }

  // 检查是否主要是拉丁字母
  if (/^[a-zA-Z\u00C0-\u00FF\s\d.,;:'"!?()-]+$/.test(text)) {
    return 'latin';
  }

  return 'other';
}

// 将短句子合并为适当长度且符合语义的段落（支持多语言）
const mergeSrtItems = (srtList: SrtResultModel[]) => {
  if (!srtList?.length) return [];

  // 语言类型配置
  type LanguageType = 'cjk' | 'latin' | 'other';

  interface MergeConfig {
    minChars: number;     // 最小字符数
    idealMin: number;     // 理想最小长度
    idealMax: number;     // 理想最大长度
    connector: string;    // 默认连接符
  }

  const langConfigs: Record<LanguageType, MergeConfig> = {
    cjk: {minChars: 12, idealMin: 20, idealMax: 120, connector: ''},
    latin: {minChars: 15, idealMin: 20, idealMax: 120, connector: ' '},
    other: {minChars: 15, idealMin: 20, idealMax: 120, connector: ' '}
  };

  // 自动确定样本文本的语言类型
  const sampleTexts = srtList.slice(0, Math.min(10, srtList.length)).map(item => item.text).join(' ');
  const dominantLangType = estimateLanguageType(sampleTexts);

  // 合并配置
  const MAX_TIME_GAP = 2.5; // 最大时间间隔（秒）

  // 存储合并结果
  const mergedList: Array<{ start: string, text: string }> = [];

  // 当前正在处理的合并项
  interface CurrentMergeItem {
    start: string;
    text: string;
    end: string;
    langType: LanguageType;
  }

  let currentItem: CurrentMergeItem = {
    start: '',
    text: '',
    end: '',
    langType: dominantLangType
  };

  // 判断是否应该结束当前合并
  const shouldEndMerge = (
    current: CurrentMergeItem,
    nextText: string,
    nextLangType: LanguageType,
    hasNextItemStartMark: boolean,
    timeGap: number
  ): boolean => {
    const config = langConfigs[current.langType];

    return (
      // 语言类型变化
      current.langType !== nextLangType ||
      // 当前文本已足够长，添加下一项会超过理想最大长度
      (current.text.length >= config.idealMin &&
        (current.text.length + nextText.length > config.idealMax)) ||
      // 当前句子以结束标点结尾，且下一句有明显开始标记
      (isEndOfSentence(current.text) && hasNextItemStartMark) ||
      // 当前句子足够长且以结束标点结尾
      (current.text.length >= config.idealMin && isEndOfSentence(current.text)) ||
      // 时间间隔过大
      timeGap > MAX_TIME_GAP ||
      // 当前要合并的句子本身就很长
      nextText.length > config.idealMin * 1.5
    );
  };

  // 决定连接符
  const getConnector = (current: CurrentMergeItem, nextText: string): string => {
    // CJK语言通常不需要空格
    if (current.langType === 'cjk') return '';

    // 如果当前文本已经以标点结尾，使用空格连接
    if (isEndOfSentence(current.text)) return ' ';

    // 如果下一句不是明显的新句开始且很短，可能是句子的一部分
    const config = langConfigs[current.langType];
    if (!isStartOfSentence(nextText) && nextText.length < config.minChars) {
      return current.langType === 'latin' ? ' ' : '';
    }

    // 默认连接符
    return langConfigs[current.langType].connector;
  };

  // 处理每个字幕项
  for (let i = 0; i < srtList.length; i++) {
    const item = srtList[i];
    const nextItem = i < srtList.length - 1 ? srtList[i + 1] : null;
    const itemLangType = estimateLanguageType(item.text);

    // 第一个字幕项
    if (currentItem.text === '') {
      currentItem = {
        start: item.start,
        text: item.text,
        end: item.start,
        langType: itemLangType
      };
      continue;
    }

    // 计算与下一项的时间间隔
    const timeGap = nextItem ? getTimeDifference(item.start, nextItem.start) : 0;

    // 判断是否需要结束当前合并
    const nextHasStartMark = nextItem ? isStartOfSentence(nextItem.text) : false;

    if (shouldEndMerge(currentItem, item.text, itemLangType, nextHasStartMark, timeGap)) {
      // 保存当前合并结果
      mergedList.push({
        start: currentItem.start,
        text: currentItem.text
      });

      // 开始新的合并
      currentItem = {
        start: item.start,
        text: item.text,
        end: item.start,
        langType: itemLangType
      };
    } else {
      // 合并当前字幕，选择合适的连接符
      const connector = getConnector(currentItem, item.text);
      currentItem.text += connector + item.text;
      currentItem.end = item.start;
    }
  }

  // 添加最后一个合并项（如果有）
  if (currentItem.text) {
    mergedList.push({
      start: currentItem.start,
      text: currentItem.text
    });
  }

  return mergedList;
};

const getUIText = () => {
  return mergeSrtItems(props.srtList).map(item => `${formatTime(item.start)} ${item.text}`).join('\n')
}

defineExpose({
  getUIText: () => getUIText(),
})

</script>

<template>
  <div>
    <div
      v-for="item in mergeSrtItems(srtList)"
      class="mb-[20px] flex flex-col lg:flex-row"
    >
      <span class="text-white/[0.6] text-[14px] w-[80px] flex-shrink-0">{{ formatTime(item.start) }}</span>
      <span class="text-white text-[16px]">{{ item.text }}</span>
    </div>
  </div>
</template>

<style scoped>

</style>

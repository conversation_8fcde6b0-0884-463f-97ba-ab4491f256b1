<script setup lang="ts">
import {unified} from 'unified';
import remarkParse from 'remark-parse';
import remarkRehype from 'remark-rehype';
import remarkGfm from 'remark-gfm';
import rehypeStringify from 'rehype-stringify';
import {ref, onMounted} from 'vue';
import {selectElText} from '@/utils/browser';

const props = defineProps<{
  text: string,
}>()

const elRef = ref<HTMLDivElement>()

const outputMarkdown = (text: string) => {
  try {
    return unified()
      .use(remarkParse)
      .use(remarkRehype)
      .use(remarkGfm)
      .use(rehypeStringify)
      .processSync(text)
      .toString()
  } catch (e) {
    return text
  }
}

const getUIText = () => {
  if (elRef.value) {
    return selectElText(elRef.value)
  }
}

defineExpose({
  getUIText: () => getUIText(),
})
</script>

<template>
  <div class="markdown" v-html="outputMarkdown(text)" ref="elRef"></div>
</template>

<style>
.markdown {
  color: #2D2D33;
}

.dark .markdown {
  color: #D1D1D1;
}

.markdown > *:first-child {
  margin-top: 0 !important;
}

.markdown::-webkit-scrollbar-thumb {
  background: #2D2D33;
}

.dark .markdown::-webkit-scrollbar-thumb {
  background: #D1D1D1;
}

.markdown strong {
  color: #2D2D33;
}

.dark .markdown strong {
  color: white;
}

.markdown hr {
  display: none;
}

.markdown p {
  margin: 18px 0 0 0;
}

.markdown ul {
  margin: 0 0 18px 0;
}

.markdown h1, .markdown h2, .markdown h3, .markdown h4, .markdown h5, .markdown h6 {
  margin: 18px 0;
  font-size: 20px;
  color: #2D2D33;
  font-weight: bold;
}

.dark .markdown h1, .dark .markdown h2, .dark .markdown h3, .dark .markdown h4, .dark .markdown h5, .dark .markdown h6 {
  margin: 18px 0;
  font-size: 20px;
  color: white;
  font-weight: bold;
}
</style>

<script setup lang="ts">
import {useI18n} from '@/locales';

type ClassType = string | string[] | Record<keyof CSSStyleDeclaration, string>

const {t} = useI18n()

const props = defineProps<{
  okText?: string,
  cancelText?: string,
  cancelBtnClass?: ClassType,
  okBtnClass?: ClassType,
  dialogClass?: ClassType,
  contentClass?: ClassType,
}>()

const emit = defineEmits<{
  clickClose: [],
  clickCancel: [],
  clickOk: [],
}>()

const handleClickClose = () => {
  emit('clickClose')
}

const handleClickCancel = () => {
  emit('clickCancel')
}

const handleClickOk = () => {
  emit('clickOk')
}

</script>

<template>
  <div :class="dialogClass" data-dialog-class>
    <div
      class="w-[90%] mx-auto lg:w-[480px] min-h-[228px] bg-white dark:bg-[#595961] flex flex-col rounded-[24px] text-[14px] lg:text-[16px]"
    >

      <button class="ml-auto text-dark dark:text-white mt-[18px] mr-[18px]" @click="handleClickClose">
        <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14.3996 13.8008L1.59961 1.34673" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          <path d="M1.59961 13.8L14.0537 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        </svg>
      </button>

      <div class="mt-[20px] px-[30px] flex-1 text-dark dark:text-white" :class="contentClass" data-content-class>
        <slot>
          默认文案
        </slot>
      </div>

      <div class="px-[30px] py-[30px] flex justify-center lg:justify-end">

        <button
          data-cancel-btn-class
          class="dark:bg-dark/[0.6] border-1 border-dark dark:border-none h-[42px] min-w-[110px] px-[6px] lg:px-[20px] rounded-[24px] text-dark dark:text-white/[0.6] font-medium"
          :class="props.cancelBtnClass"
          @click="handleClickCancel"
          v-html="cancelText ?? t('000940')"
        ></button>

        <button
          data-ok-btn-class
          class="gradient-button-auto-theme h-[42px] ml-[20px] min-w-[110px] px-[6px] lg:px-[20px] rounded-[24px] text-white font-medium"
          :class="props.okBtnClass"
          @click="handleClickOk"
          v-html="okText ?? t('000992')"
        ></button>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>

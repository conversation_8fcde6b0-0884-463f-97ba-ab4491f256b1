<script setup lang="ts">
import linkIcon from '@images/ai-youtube/link-icon.svg'
import StarIcon from '~icons/ai-youtube/star-icon.svg'
import {ref} from 'vue'
import {useI18n} from '@/locales';
import YoutubeUtils from '@/component/youtube/utils/YoutubeUtils';

const {t} = useI18n()

const props = defineProps<{
  disabled?: boolean,
  btnTitle: string,
}>()

const emits = defineEmits<{
  beforeSubmit: [url: string]
  submit: [url: string]
}>()

//是否是合法的youtube视频链接ref
const isYoutubeURLRef = ref(true)
const submitBtnRef = ref<HTMLButtonElement | null>(null)

const handleSubmit = (ev: Event) => {
  const formData = new FormData(ev.target as HTMLFormElement)
  const url = formData.get('url')
  emits('beforeSubmit', url as string)
  //检查url是否是youtube视频链接
  if (!url || !YoutubeUtils.checkIsYoutubeUrl(url as string)) {
    isYoutubeURLRef.value = false
    return
  }
  isYoutubeURLRef.value = true
  emits('submit', url as string)
}

</script>

<template>
  <div class="flex flex-col gap-[24px] w-full px-[4px]">
    <form
      class="relative w-full flex flex-col lg:flex-row items-center lg:h-[98px] px-[24px] py-[16px] lg:py-0 lg:px-[30px] gradient-border"
      @submit.prevent="handleSubmit"
    >

      <div class="mr-[24px] flex-1 w-full flex items-center gap-[10px] lg:gap-[24px]">
        <div class="h-[30px] self-start flex lg:items-center">
          <img :src="linkIcon" class="size-[20px] lg:size-[40px] " alt="link icon">
        </div>
        <div class="flex flex-col flex-1">

          <textarea
            name="url"
            class="w-full min-h-[30px] lg:!h-[30px] text-white placeholder:text-white/[0.8] bg-transparent text-[14px] lg:text-[20px] resize-none lg:whitespace-nowrap lg:overflow-x-auto lg:overflow-y-hidden"
            :placeholder="t('aiYoutubeSummarizerInputPlaceholder')"
            rows="1"
            autofocus
            autocomplete="on"
            @input="(e: Event) => {
              const target = e.target as HTMLTextAreaElement
              target.style.height = 'auto'
              target.style.height = target.scrollHeight + 'px'
            }"
            @keydown.enter.prevent="(e: KeyboardEvent) => {
              submitBtnRef?.click()
            }"
          ></textarea>

          <span class="block lg:hidden text-[#F06464] text-[12px]" :class="[isYoutubeURLRef ? 'invisible' : 'visible']">
            {{ t('aiYoutubeSummarizerInputErrorText') }}
          </span>
        </div>

      </div>

      <slot name="btn-left"></slot>

      <button
        ref="submitBtnRef"
        class="gradient-button-auto-theme w-full lg:w-auto lg:min-w-[216px] px-[10px] 3xl:px-[20px] h-[38px] lg:h-[58px] text-[14px] lg:text-[20px] font-semibold mt-[16px] lg:mt-0"
        type="submit"
        :disabled="props.disabled"
        :class="[disabled?'opacity-80 pointer-events-none':'']"
      >
        <span class="flex-center h-[28px]">
           <span>{{ btnTitle }}</span>
           <span class="mb-4"><StarIcon/></span>
        </span>

        <span class="hidden lg:block">
          <slot name="btn-title-bottom"></slot>
        </span>
      </button>

      <span class="block lg:hidden">
        <slot name="btn-title-bottom"></slot>
      </span>
    </form>

    <span class="hidden lg:block text-[#F06464]" :class="[isYoutubeURLRef ? 'invisible' : 'visible']">
      {{ t('aiYoutubeSummarizerInputErrorText') }}
    </span>
  </div>

</template>

<style scoped>

/* 隐藏滚动条但保留滚动功能 */
textarea {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

textarea::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 覆盖浏览器自动填充样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
textarea:-webkit-autofill:active {
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: white !important;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 0 1000px transparent !important;
  background-color: transparent !important;
}


.gradient-border {
  --radius: 24px;
  position: relative;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: var(--radius);
  transition: border-color 0.3s;
}

@media screen and (max-width: 1024px) {
  .gradient-border {
    --radius: 12px;
  }
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(90deg, #D6FB72, #76F9B1);
  border-radius: var(--radius);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: -1;
}

.gradient-border::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: black;
  border-radius: var(--radius);
  z-index: -1;
}

.gradient-border:focus-within {
  border-color: transparent;
}

.gradient-border:focus-within::before {
  opacity: 1;
}
</style>

<script setup lang="ts" generic="T,U extends {items:T[]}">
import {vInfiniteScroll} from '@vueuse/components'
import {onActivated, reactive, watch} from 'vue';
import useLoginService from '@/services/useLoginService';
import {isClient} from '@vueuse/core';


//参数固定 api必须返回 对象:包含items属性 { items: T[] }
const props = defineProps<{
  fetchApi: (page: number) => Promise<U | undefined>
}>()

defineSlots<{
  item(props: { item: T, index: number }): any
}>()

const {isLoginRef} = useLoginService()

const historyListInfo = reactive({
  list: [] as T[],
  _page: 1,
  _canLoadMore: false,
  canLoadMore() {
    return historyListInfo._canLoadMore
  },
  async _fetchList(page: number) {
    const result = await props.fetchApi(page)
    if (result && result.items) {
      this._canLoadMore = result.items.length >= 20
      return result.items
    }
    return []
  },
  async fetchHistoryList(page: number) {
    this._page = page
    const list = await this._fetchList(page)
    this.list = list
  },
  async loadMore() {
    this._page++
    const list = await this._fetchList(this._page)
    this.list = [...this.list, ...list]
  },
  handleDeleteByIndex(index: number) {
    if (index !== -1) {
      this.list.splice(index, 1)
    }
  },
})
onActivated(() => {
  historyListInfo.fetchHistoryList(1)
})

if (isClient) {
  watch(isLoginRef, () => {
    historyListInfo.fetchHistoryList(1)
  })
}

defineExpose({
  handleDeleteByIndex(index: number) {
    historyListInfo.handleDeleteByIndex(index)
  },
  get hasItem() {
    return historyListInfo.list.length > 0
  },
})

</script>

<template>
  <div
    v-infinite-scroll="[()=>historyListInfo.loadMore(),{
      distance:30,
      canLoadMore:historyListInfo.canLoadMore,
      interval:1000,
    }]"
  >
    <template :key="index" v-for="(item, index) in historyListInfo.list">
      <slot
        name="item"
        :item="item as T"
        :index="index"
      ></slot>
    </template>

  </div>
</template>

<style scoped>

</style>

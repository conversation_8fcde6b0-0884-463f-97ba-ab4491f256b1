class YoutubeUtils {

  //检查url是否是youtube url
  static checkIsYoutubeUrl(url: string) {
    const regex = /^(?:https?:\/\/)?(?:www\.|m\.)?(?:youtube\.com\/(?:watch\?(?:.*&)*v=|embed\/|v\/|shorts\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(?:[&?#].*)?$/;
    return regex.test(url)
  }

  // 根据url提取videoId
  static getVideoIdByFullUrl(url: string) {
    try {
      // 处理空URL
      if (!url || url.trim() === '') {
        return null;
      }

      if (this.checkIsYoutubeUrl(url) === false) {
        return null;
      }

      // 尝试使用正则表达式匹配各种YouTube URL格式
      // 匹配模式1: youtube.com/watch?v=VIDEO_ID 或 youtube.com/watch?foo=bar&v=VIDEO_ID&other=param
      const regExp1 = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|watch\?.+&v=)([^#&?]*).*/;
      const match1 = url.match(regExp1);
      if (match1 && match1[2].length === 11) {
        return match1[2];
      }

      // 匹配模式2: youtube.com/shorts/VIDEO_ID
      const regExp2 = /^.*(youtube.com\/shorts\/)([^#&?]*).*/;
      const match2 = url.match(regExp2);
      if (match2 && match2[2].length === 11) {
        return match2[2];
      }

      // 匹配模式3: youtube.com/live/VIDEO_ID
      const regExp3 = /^.*(youtube.com\/live\/)([^#&?]*).*/;
      const match3 = url.match(regExp3);
      if (match3 && match3[2].length === 11) {
        return match3[2];
      }

      // 尝试作为URL解析
      try {
        const urlObj = new URL(url);

        // 处理youtube.com链接
        if (urlObj.hostname.includes('youtube.com')) {
          // 标准watch链接
          const vParam = urlObj.searchParams.get('v');
          if (vParam) return vParam;

          // 处理/embed/, /v/, /shorts/等路径
          const paths = urlObj.pathname.split('/').filter(Boolean);
          if (paths.length > 1 && ['embed', 'v', 'shorts', 'live'].includes(paths[0])) {
            return paths[1];
          }
        }

        // 处理youtu.be短链接
        if (urlObj.hostname.includes('youtu.be')) {
          return urlObj.pathname.substring(1);
        }
      } catch {
        // URL解析失败，继续尝试其他方法
      }

      return null;
    } catch (error) {
      console.error('解析YouTube URL出错:', error);
      return null;
    }
  }

}


export default YoutubeUtils

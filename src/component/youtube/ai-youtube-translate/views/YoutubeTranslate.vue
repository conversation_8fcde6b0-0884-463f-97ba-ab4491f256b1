<script setup lang="ts">
import URLInput from '@/component/youtube/views/URLInput.vue';
import GradientProgress from '@/component/youtube/views/GradientProgress.vue';
import {createSubtitleTaskApi, getYoutubeSubtitleEquityApi, querySubtitleTaskApi} from '@/api/aiYoutubeTranslateApi';
import SingleTaskUtil from '@/services/task-query/SingleTaskUtil';
import {delay} from '@/utils/util';
import {nextTick, onMounted, reactive, ref, watch} from 'vue';
import {SrtResultModel} from '@/interface/OnlineEdit';
import showYoutubeDialog from '@/component/youtube/views/dialog/showYoutubeDialog';
import {useI18n} from '@/locales';
import CreateTaskError from '@/services/exception/CreateTaskError';
import {nanoid} from 'nanoid';
import YoutubeTranslateTrack from '@/track/YoutubeTranslateTrack';
import pointIcon from '@images/ai-youtube/point.svg'
import useTrackInfo from '@/hooks/utils/useTrackInfo';
import TransLangsSelect from '@/component/youtube/ai-youtube-translate/views/TransLangsSelect.vue';
import {AiSummaryHasSubtitle} from '@/interface/AiYoutube';
import YoutubeVideo from '@/component/youtube/views/YoutubeVideo.vue';
import disableSubtitleIcon from '@images/ai-youtube/subtitle/disable-subtitle.svg'
import enableSubtitleIcon from '@images/ai-youtube/subtitle/enable-subtitle.svg'
import {downloadFile} from '@central/shared';
import {getLang} from '@central/i18n';
import useLoginService from '@/services/useLoginService';
import PricingDialog from '@/render/PricingDialog';
import useProductBuy from '@/component/vip/buy/useProductBuy';
import QueryTaskError from '@/services/exception/QueryTaskError';
import TaskErrorMsg from '@/services/exception/TaskErrorMsg';

const {getTaskCompleteTotalDuration, setTrackByTaskId} = useTrackInfo()
const {isLoginRef, isVipRef, doLogin} = useLoginService()
const {fetchProductData, getEnable3DayItem} = useProductBuy();

function getDeviceId() {
  const deviceKey = '_Secure-YVTD'
  const cacheId = localStorage.getItem(deviceKey)
  if (cacheId) {
    return cacheId
  } else {
    const id = nanoid()
    localStorage.setItem(deviceKey, id)
    return id
  }
}

const {t} = useI18n();
const viewStateRef = ref<'start' | 'loading' | 'text'>('start')
const loadingElId = 'translate-loading-id'

onMounted(() => {
  watch(isLoginRef, () => {
    priceInfo.fetchPrice()
  }, {immediate: true})
})

const priceInfo = reactive({
  _limit: 0,
  _used: 0,
  price: 0,
  transPrice: 0,
  get hasCount() {
    return this._limit > this._used
  },
  async fetchPrice() {
    const {youtube} = await getYoutubeSubtitleEquityApi(getDeviceId())
    this._limit = youtube.limit
    this._used = youtube.used
    this.price = youtube.price
    this.transPrice = youtube.transPrice
  }
})

const taskInfo = reactive({
  progress: 0,
  enableSubtitle: true,
  langs: getLang() === 'jp' ? 'ja' : getLang(),//默认选中源语言

  get isOriginLang() {
    return taskInfo.langs === 'origin'
  },

  result: {
    duration: 0,
    title: '',
    url: '',
    srtList: [] as SrtResultModel[],
  },
})

const handleBeforeSubmit = (url: string) => {
  YoutubeTranslateTrack.clickStartTranslate({
    source: url
  })
}

function _scrollToInput() {
  nextTick(() => {
    const el = document.querySelector<HTMLElement>(`#${loadingElId}`)
    if (el) {
      const top = el.getBoundingClientRect().top + window.scrollY;
      window.scrollTo({
        top: top - 300,
        behavior: 'smooth'
      })
    }
  })
}

const handleSubmit = async (url: string, freeTime?: number) => {
  viewStateRef.value = 'loading'
  taskInfo.progress = 0
  _scrollToInput()
  const lang = taskInfo.langs
  let taskId = ''
  try {
    taskId = await createSubtitleTaskApi(url, getDeviceId(), lang, freeTime)
    await delay(1000)
    setTrackByTaskId(taskId)
    const _instance = new SingleTaskUtil({
      api: () => querySubtitleTaskApi(taskId)
    })
    let hasTrackStarted = false
    const data = await _instance.startQuery({
      onProgress(progress, data) {
        const oldProgress = taskInfo.progress
        const random = Math.floor(Math.random() * 5)
        taskInfo.progress = Math.min(99, oldProgress + random)
        if (data.state === 4 && !hasTrackStarted) {
          hasTrackStarted = true
          YoutubeTranslateTrack.realStartTranslateVideo({
            source: url,
            lang: lang,
          }).then()
        }
      },
    })
    YoutubeTranslateTrack.convertResult({
      result: 'success',
      time: data.duration,
      filename: data.title,
      source: url,
      completeDuration: getTaskCompleteTotalDuration(taskId),
      text: data.subtitles.map(i => i.text).join('').substring(0, 1000),
      cc_subtitle: Number(data.has_subtitle),
      lang: lang,
    }).then()
    taskInfo.result.url = data.url
    taskInfo.result.srtList = data.subtitles
    taskInfo.result.title = data.title
    taskInfo.result.duration = data.duration
    viewStateRef.value = 'text'

  } catch (e) {
    console.error(e)

    let errorStatus = ''
    let hasSubtitles = AiSummaryHasSubtitle.no
    let duration = 0

    if (e instanceof CreateTaskError) {
      const s = e.payload?.status
      if (s) {
        errorStatus = s.toString()
      }
    } else if (e instanceof QueryTaskError) {
      hasSubtitles = e.payload?.has_subtitle ?? AiSummaryHasSubtitle.no
      const s = e.payload?.error?.status?.toString()
      if (s) {
        errorStatus = s.toString()
      }
      duration = e.payload?.duration
    }

    if (errorStatus === '19109') {
      const text = t('aiYoutubeTranslateResultFailReason2').replaceAll('#', '10')
      showYoutubeDialog(text, {
        to: `#${loadingElId}`,
        okText: t('aiSubtitleSignLog'),
        cancelText: `<span class="text-gradient">${t('speechTrialMinutes').replace('#', '10')}</span>`,
        async onClickOk(close) {
          close()
          await doLogin()
          await handleSubmit(url)
        },
        onClickCancel(close) {
          close()
          handleSubmit(url, 600)
        },
        onDialogClose() {
          viewStateRef.value = 'start'
        }
      })
      return
    }

    if (errorStatus === '19105') {
      YoutubeTranslateTrack.pointNotEnoughDialog().then()
      const title = isLoginRef.value ? t('noPointNoVipToUpgradeTitle') : t('dialogOnlyVipTitle')
      const cost = Math.ceil(duration / 60) * (taskInfo.isOriginLang ? priceInfo.price : priceInfo.transPrice)
      const subtitle = cost > 0 ? t('costPointCountsTitle').replace('#', String(cost)) : undefined
      await fetchProductData()
      const getOkText = () => {
        if (isVipRef.value) {
          return t('pricingBuyNowKey')
        }
        return getEnable3DayItem() ? t('priceKey3DaysFreeTitle') : t('aiSubtitleUpgradeUser')
      }
      showYoutubeDialog(title, {
        to: `#${loadingElId}`,
        okText: getOkText(),
        subtitle: subtitle,
        onClickOk(close) {
          close()
          if (isVipRef.value) {
            PricingDialog.showVipWithPointDialog().then()
          } else {
            PricingDialog.showOnlyVipDialog({
              source: PricingDialog.source.pointNotEnough,
            }).then()
          }
        },
        onDialogClose() {
          viewStateRef.value = 'start'
        }
      })
      return
    }

    if (errorStatus === '19456') {
      const text = t('aiYoutubeTranslateResultFailReason3').replaceAll('#', '1.5')
      showYoutubeDialog(text, {
        to: `#${loadingElId}`,
        onDialogClose() {
          viewStateRef.value = 'start'
        }
      })
      return
    }

    YoutubeTranslateTrack.convertResult({
      result: 'fail',
      reason: TaskErrorMsg.safeToJSON(e),
      message: TaskErrorMsg.getReason(errorStatus) || errorStatus,
      status: errorStatus,
      task_id: taskId,
      lang: lang,
    }).then()

    const errorText = `
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.999 18C29.999 24.628 24.627 30 17.999 30C11.371 30 5.99902 24.628 5.99902 18C5.99902 11.372 11.371 6 17.999 6C24.627 6 29.999 11.372 29.999 18Z" stroke="currentColor" stroke-width="2"/>
        <path d="M18.0002 22.4961C17.2037 22.4961 16.5557 23.1441 16.5557 23.9406C16.5557 24.7371 17.2037 25.3851 18.0002 25.3851C18.7967 25.3851 19.4447 24.7371 19.4447 23.9406C19.4447 23.1441 18.7967 22.4961 18.0002 22.4961Z" fill="currentColor"/>
        <path d="M17.999 11.9961V19.4961" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>${t('aiYoutubeTranslateResultFail')}</span>
    `

    showYoutubeDialog(errorText, {
      class: '!absolute !bg-transparent text-[#F06464]',
      to: `#${loadingElId}`,
      titleClass: 'flex items-center gap-[6px]',
      dangerouslyUseHTMLString: true,
      okText: t('speechAudioRetry'),
      onClickOk(close) {
        handleSubmit(url)
        close()
      },
      onClickCancel(close) {
        close()
        viewStateRef.value = 'start'
      },
      onClickClose() {
        close()
        viewStateRef.value = 'start'
      }
    })

  } finally {
    priceInfo.fetchPrice()
  }
}

function handleClickDownloadSrt() {
  YoutubeTranslateTrack.clickDownloadSrt().then()
  const list = taskInfo.result.srtList
  downloadSubtitleSrt([list], taskInfo.result.title)

  async function downloadSubtitleSrt(subtitleList: Array<SrtResultModel[]>, filename: string) {
    const text = _buildSrtText(subtitleList)
    const blob = new Blob([text], {type: "text/plain"})
    filename = filename.replace(/\.[^/.]+$/, "")
    const _url = URL.createObjectURL(blob)
    await downloadFile(_url, `${filename}.srt`)
    URL.revokeObjectURL(_url)

    function _buildSrtText(subtitleList: Array<SrtResultModel[]>) {
      //可能是双语字幕, 所以是Array 内部是字幕列表
      const [l1, l2] = subtitleList

      const getList = () => {
        if (l1 && l2) {
          return l1.length >= l2.length ? l1 : l2
        } else {
          return l1 || l2 || []
        }
      }

      let text = ''
      getList().forEach((value, i) => {
        const item1 = l1?.find(i => i.start === value.start)
        const item2 = l2?.find(i => i.start === value.start)
        const item = item1 || item2
        if (item) {
          text += `${i + 1}\n`
          text += `${dateFormat(parseFloat(item.start) / 1000)} --> ${dateFormat(parseFloat(item.end) / 1000)}\n`
        }
        if (item1) {
          text += `${item1.text.trim()}\n`
        }
        if (item2) {
          text += `${item2.text.trim()}\n`
        }
        text += '\n'
      })
      return text
    }

    function dateFormat(second: number) {
      const date = second;
      const milliseconds = parseInt(String(date * 1000 % 1000)).toString().padStart(3, '0')
      const seconds = parseInt(String(date % 60))
      const Minutes = parseInt(String(date / 60)) % 60;
      const Hours = parseInt(String(date / 60 / 60));
      return (
        (Hours >= 10 ? Hours : "0" + Hours) +
        ":" +
        (Minutes >= 10 ? Minutes : "0" + Minutes) +
        ":" +
        (seconds >= 10 ? seconds : "0" + seconds) +
        "," +
        milliseconds
      );
    }
  }
}

const subTitleText = reactive({
  title: ''
})

const handleTimeUpdate = (timestamp: number) => {
  const currentTime = timestamp * 1000
  //找到在时间区间内的item
  const getSectionItem = (subtitleList: SrtResultModel[]) => {
    return subtitleList.find(item => {
      return currentTime >= parseFloat(item.start) && currentTime < parseFloat(item.end)
    })
  }

  const l1Item = getSectionItem(taskInfo.result.srtList)
  if (l1Item) {
    subTitleText.title = l1Item.text ? l1Item.text + '\n' : ''
  } else {
    subTitleText.title = ''
  }
}

</script>

<template>
  <div class="flex flex-col w-[90vw] lg:w-[62.5vw]">

    <URLInput
      @beforeSubmit="handleBeforeSubmit"
      @submit="handleSubmit"
      :disabled="viewStateRef==='loading'"
      :btn-title="viewStateRef==='loading' ? t('translating') :  t('aiYoutubeTranslateStartTaskBtnTitle')"
    >
      <template #btn-left>
        <TransLangsSelect v-model="taskInfo.langs"/>
      </template>

      <template #btn-title-bottom v-if="!priceInfo.hasCount && priceInfo.price>0">
       <span class="text-[12px] text-white/[0.5] lg:text-dark/[0.7] flex-center font-normal mt-[10px] lg:mt-0">
          <span>{{ t('aiSubtitlePerMin') }}</span>
          <img :src="pointIcon" alt="" loading="lazy">
          <span class="pl-[2px]">{{ taskInfo.isOriginLang ? priceInfo.price : priceInfo.transPrice }}</span>
        </span>
      </template>
    </URLInput>

    <div
      v-if="viewStateRef!=='start'"
      class="mt-[20px] w-full bg-[#B9ECFF1F] text-white rounded-[12px] lg:rounded-[16px] backdrop-blur-[40px]"
    >
      <div v-if="viewStateRef==='loading'" :id="loadingElId">
        <div class="py-[40px] px-[20px] lg:px-[100px] h-[260px] lg:h-[400px] flex-center flex-col">
          <div class="flex-center w-full">
            <div class="flex-1 h-[8px] lg:h-[16px]">
              <GradientProgress :progress="taskInfo.progress"/>
            </div>
            <span class="pl-[14px] w-[3em]">
              <span class="text-[16px] lg:text-[28px] font-semibold">{{ taskInfo.progress }}</span>
              <span class="text-[12px] lg:text-[14px]">%</span>
            </span>
          </div>

          <div class="mt-[32px] lg:mt-[64px] text-center text-[14px] lg:text-[16px] whitespace-pre-line">
            {{ t('aiYoutubeSummarizerTaskLoadingText') }}
          </div>
        </div>
      </div>

      <div v-else-if="viewStateRef==='text'">
        <div class="px-[16px] lg:px-[30px]">
          <div class="pt-[20px] lg:pt-[30px]">
            <YoutubeVideo
              :url="taskInfo.result.url"
              :total-duration="taskInfo.result.duration"
              @timeUpdate="handleTimeUpdate"
            >
              <template #subtitle>
                <div
                  class='absolute left-1/2 bottom-[5%] -translate-x-1/2 z-[100] bg-black/[0.7] w-max max-w-full px-[16px] rounded-[10px] whitespace-pre-line break-words [paint-order:stroke]'
                  v-show="taskInfo.enableSubtitle && subTitleText.title"
                >
                  <div class="my-[2px] text-[14px] lg:text-[1.5vw]">{{ subTitleText.title }}</div>
                </div>
              </template>

              <template #options>
                <div class="flex-center *:w-[20px] lg:*:w-[25px]">
                  <button @click="taskInfo.enableSubtitle = true" v-show="!taskInfo.enableSubtitle">
                    <img :title="t('003111')" class="w-full" :src="disableSubtitleIcon" alt="close icon">
                  </button>

                  <button @click="taskInfo.enableSubtitle = false" v-show="taskInfo.enableSubtitle">
                    <img :title="t('003110')" class="w-full" :src="enableSubtitleIcon" alt="open icon">
                  </button>
                </div>
              </template>
            </YoutubeVideo>
          </div>
          <button
            class="gradient-button-auto-theme w-full lg:w-auto lg:min-w-[230px] h-[38px] lg:h-[58px] px-[10px] 3xl:px-[20px] py-[8px] lg:py-[16px] text-[14px] lg:text-[20px] font-semibold flex justify-center gap-[8px] mt-[24px] lg:mt-[40px] mx-auto mb-[20px] lg:mb-[50px]"
            @click="handleClickDownloadSrt"
          >
            <span class="flex items-end">
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M19.4167 12.793V15.1313C19.4167 16.8596 17.9 18.283 16.0583 18.283H4.35833C2.51667 18.283 1 16.8596 1 15.1313V12.793"
                  stroke="#2D2D33" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
                <path d="M5.7666 10.7617L10.2083 15.0317L14.7583 10.7617" stroke="#2D2D33" stroke-width="2"
                      stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10.208 15.03V1" stroke="#2D2D33" stroke-width="2" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>

            <span>{{ t('aiYoutubeTranslateResultText4') }}</span>
            <svg class="inline-block" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="m9 0 .44 1.867a9 9 0 0 0 6.693 6.692L18 9l-1.867.44a9 9 0 0 0-6.692 6.693L9 18l-.44-1.867a9 9 0 0 0-6.693-6.692L0 9l1.867-.44a9 9 0 0 0 6.692-6.693L9 0Z"
                fill="currentColor"></path>
            </svg>
          </button>
        </div>
      </div>

    </div>
  </div>

</template>

<style scoped>
</style>

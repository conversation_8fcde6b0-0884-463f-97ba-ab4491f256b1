---
import {Picture} from '@central/assets/imagetools';
import CommentsListV2 from "@/component/home/<USER>";
import translateStarIcon from '@/assets/images/ai-translate/translate-star.png'

interface Props {
  commentsList: Array<{ comment: string, name: string, avatar: string }>,
  title: string,
  sectionBg?: string,
}
const {
  commentsList,
  title,
  sectionBg = 'src/assets/images/ai-translate/ai-translate-comment-bg.png'
} = Astro.props as Props
---
<section class="relative bg-black pt-[60px] lg:pt-[180px] text-white">
  <div class="absolute inset-0 object-cover">
    <Picture
      src={sectionBg}
      alt="background"
      layout="fill"
      width={1920}
      attributes={{
        img: {
          draggable: false,
        }
      }}
    />
  </div>
  <div class="relative z-10">
    <h2 class="text-[28px] max-w-[90%] mx-auto lg:text-[48px] font-semibold text-center pb-[30px] lg:pb-[90px]"
        set:html={title}>
    </h2>
    <CommentsListV2
      starIcon={translateStarIcon}
      list={commentsList}
      liClassName="pb-0 lg:pb-[40px]"
      avatarClassName="bg-transparent shadow-[0px_10px_40px_0px_rgba(24,173,37,0.2)]"
      nameClassName="!text-white/50"
      client:idle/>
  </div>
</section>

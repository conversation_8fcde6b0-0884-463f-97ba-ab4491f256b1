import {Directive} from '@vue/runtime-core';

///输入框相关指令

/**
 * @desc 自动聚焦
 *
 * @example
 * ```js
 *
 * //将自动聚焦
 * <input
 *  v-focus
 * >
 * //将自动聚焦并执行回调
 * <input
 *  v-focus="(el:HTMLElement)=>el.select()"
 * >
 *
 * ```
 */
export const vFocus: Directive = {
  mounted(el: HTMLElement, binding) {
    el.focus()
    if (typeof binding.value === 'function') {
      binding.value(el)
    }
  }
}

//自动选中所有内容
export const vSelect: Directive = {
  mounted(el: HTMLInputElement, binding) {
    el.select()
    if (typeof binding.value === 'function') {
      binding.value(el)
    }
  }
}

import {Directive} from '@vue/runtime-core';
import {isClient} from '@vueuse/core';

const observer = !isClient ? null : new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const el = entry.target as HTMLMediaElement
      const prop = el.dataset.prop
      const value = el.dataset.value
      if (prop && value) {
        el.setAttribute(prop, value)
        el.load?.()
        observer?.unobserve(el)
      }
    }
  })
})
//视频懒加载
export const vLazyMedia: Directive = {
  mounted(el: HTMLMediaElement, binding) {
    const prop = binding.arg ?? 'src'
    const value: string = binding.value
    el.dataset.value = value
    el.dataset.prop = prop
    observer?.observe(el)
  }
}

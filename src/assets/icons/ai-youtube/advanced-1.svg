<svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path
    d="M19.7999 29.3984C25.8156 29.3984 30.7974 24.972 31.6658 19.1984M7.79993 17.3984C7.79993 21.5877 9.94659 25.2755 13.1999 27.422M29.4008 10.1984C27.2115 7.28377 23.7259 5.39844 19.7999 5.39844C16.8221 5.39844 14.0976 6.48312 11.9999 8.27893"
    stroke="url(#paint0_linear_52984_133)" stroke-width="2.4" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="30.6" cy="11.3969" r="3.6" fill="url(#paint1_linear_52984_133)"/>
  <circle cx="20.9999" cy="29.3969" r="3.6" fill="url(#paint2_linear_52984_133)"/>
  <circle cx="7.79995" cy="14.9984" r="3.6" fill="url(#paint3_linear_52984_133)"/>
  <path d="M15.4375 17.8073L18.7338 21.375L24.9375 14.25" stroke="url(#paint4_linear_52984_133)" stroke-width="2.4"
        stroke-linecap="round" stroke-linejoin="round"/>
  <defs>
    <linearGradient id="paint0_linear_52984_133" x1="7.79993" y1="17.3984" x2="32.1055" y2="20.5141"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#D6FB72"/>
      <stop offset="1" stop-color="#76F9B1"/>
    </linearGradient>
    <linearGradient id="paint1_linear_52984_133" x1="27" y1="11.3969" x2="34.3313" y2="12.3419"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#D6FB72"/>
      <stop offset="1" stop-color="#76F9B1"/>
    </linearGradient>
    <linearGradient id="paint2_linear_52984_133" x1="17.3999" y1="29.3969" x2="24.7312" y2="30.3419"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#D6FB72"/>
      <stop offset="1" stop-color="#76F9B1"/>
    </linearGradient>
    <linearGradient id="paint3_linear_52984_133" x1="4.19995" y1="14.9984" x2="11.5313" y2="15.9435"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#D6FB72"/>
      <stop offset="1" stop-color="#76F9B1"/>
    </linearGradient>
    <linearGradient id="paint4_linear_52984_133" x1="15.4375" y1="17.8125" x2="24.9893" y2="19.4542"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#D6FB72"/>
      <stop offset="1" stop-color="#76F9B1"/>
    </linearGradient>
  </defs>
</svg>

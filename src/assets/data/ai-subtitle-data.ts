import {useI18n} from '@/locales'

const {t} = useI18n()
export type LangItem = {
  title: string
  value: string
}

//所有的翻译语言
export function getLangList(): LangItem[] {
  return [
    {title: t('English'), value: 'en'},
    {title: t('Chinese'), value: 'zh'},
    {title: t('Japanese'), value: 'ja'},
    {title: t('French'), value: 'fr'},
    {title: t('Taiwan'), value: 'tw'},
    {title: t('German'), value: 'de'},
    {title: t('Portuguese'), value: 'pt'},
    {title: t('Spanish'), value: 'es'},
    {title: t('Afrikaans'), value: 'af'},
    {title: t('Amharic'), value: 'am'},
    {title: t('Arabic'), value: 'ar'},
    {title: t('Assamese'), value: 'as'},
    {title: t('Azerbaijani'), value: 'az'},
    {title: t('Bashkir'), value: 'ba'},
    {title: t('Belarusian'), value: 'be'},
    {title: t('Bulgarian'), value: 'bg'},
    {title: t('Bengali'), value: 'bn'},
    {title: t('Tibetan'), value: 'bo'},
    {title: t('Breton'), value: 'br'},
    {title: t('Bosnian'), value: 'bs'},
    {title: t('Catalan'), value: 'ca'},
    {title: t('Czech'), value: 'cs'},
    {title: t('Welsh'), value: 'cy'},
    {title: t('Danish'), value: 'da'},
    {title: t('Greek'), value: 'el'},
    {title: t('Estonian'), value: 'et'},
    {title: t('Basque'), value: 'eu'},
    {title: t('Persian'), value: 'fa'},
    {title: t('Finnish'), value: 'fi'},
    {title: t('Faroese'), value: 'fo'},
    {title: t('Galician'), value: 'gl'},
    {title: t('Gujarati'), value: 'gu'},
    {title: t('Hausa'), value: 'ha'},
    {title: t('Hawaiian'), value: 'haw'},
    {title: t('Hebrew'), value: 'he'},
    {title: t('Hindi'), value: 'hi'},
    {title: t('Croatian'), value: 'hr'},
    {title: t('Haitian Creole'), value: 'ht'},
    {title: t('Hungarian'), value: 'hu'},
    {title: t('Armenian'), value: 'hy'},
    {title: t('Indonesian'), value: 'id'},
    {title: t('Icelandic'), value: 'is'},
    {title: t('Italian'), value: 'it'},
    {title: t("Georgian"), value: "ka"},
    {title: t("Kazakh"), value: "kk"},
    {title: t("Khmer"), value: "km"},
    {title: t("Kannada"), value: "kn"},
    {title: t("Korean"), value: "ko"},
    {title: t("Latin"), value: "la"},
    {title: t("Luxembourgish"), value: "lb"},
    {title: t("Lingala"), value: "ln"},
    {title: t("Lao"), value: "lo"},
    {title: t("Lithuanian"), value: "lt"},
    {title: t("Latvian"), value: "lv"},
    {title: t("Malagasy"), value: "mg"},
    {title: t("Maori"), value: "mi"},
    {title: t("Macedonian"), value: "mk"},
    {title: t("Malayalam"), value: "ml"},
    {title: t("Mongolian"), value: "mn"},
    {title: t("Marathi"), value: "mr"},
    {title: t("Malay"), value: "ms"},
    {title: t("Maltese"), value: "mt"},
    {title: t("Burmese"), value: "my"},
    {title: t("Nepali"), value: "ne"},
    {title: t("Dutch"), value: "nl"},
    {title: t("Norwegian Nynorsk"), value: "nn"},
    {title: t("Norwegian"), value: "no"},
    {title: t("Occitan"), value: "oc"},
    {title: t("Punjabi"), value: "pa"},
    {title: t("Polish"), value: "pl"},
    {title: t("Pashto"), value: "ps"},
    {title: t("Romanian"), value: "ro"},
    {title: t('Russian'), value: 'ru'},
    {title: t('Saudi Arabia'), value: 'sa'},
    {title: t('Sindhi'), value: 'sd'},
    {title: t('Sinhala'), value: 'si'},
    {title: t('Slovak'), value: 'sk'},
    {title: t('Slovenian'), value: 'sl'},
    {title: t('Shona'), value: 'sn'},
    {title: t('Somali'), value: 'so'},
    {title: t('Albanian'), value: 'sq'},
    {title: t('Serbia'), value: 'sr'},
    {title: t('Sundanese'), value: 'su'},
    {title: t('Sweden'), value: 'sv'},
    {title: t('Tamil'), value: 'ta'},
    {title: t('Telugu'), value: 'te'},
    {title: t('Tajik'), value: 'tg'},
    {title: t('Thailand'), value: 'th'},
    {title: t('Turkmenistan'), value: 'tk'},
    {title: t('Tagalog'), value: 'tl'},
    {title: t('Turkey'), value: 'tr'},
    {title: t('Tatar'), value: 'tt'},
    {title: t('Ukraine'), value: 'uk'},
    {title: t('Urdu'), value: 'ur'},
    {title: t('Uzbekistan'), value: 'uz'},
    {title: t('Vietnam'), value: 'vi'},
    {title: t('Yiddish'), value: 'yi'},
    {title: t('Yoruba'), value: 'yo'},
  ]
}

//获取翻译语言+源语言map用于文案翻译
export const getFullLangMap = () => {
  const list = [
    ...[{title: t('convertDefLang'), value: 'origin'}],
    ...getLangList(),
  ]
  return new Map(list.map(item => [item.value, item.title]))
}

export const getPresetFontStyle = () => {
  return [
    {
      fontColor: '#FFFFFF',
      bgColor: null,
      textStrokeColor: '#C0C1C0',
    },
    {
      fontColor: '#FF0000',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#FF9300',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#CB7830',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#18AD25',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#3E8BFF',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#8952FF',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#C1C1C1',
      bgColor: null,
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: null,
      textStrokeColor: '#5576FC',
    },
    {
      fontColor: '#FFFFFF',
      bgColor: null,
      textStrokeColor: '#3DCABB',
    },
    {
      fontColor: '#FFFFFF',
      bgColor: null,
      textStrokeColor: '#2D2D33',
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#E5E6E8',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#FBEA57',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#85E964',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#7899FF',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#F8867D',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#BFA6F5',
      bgColor: '#2D2D33',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#F2C1BE',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#F9DFBC',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#FEF7A3',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#CEEFC5',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#D1DBFB',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#D9CAF8',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#BFA6F5',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#BCBFC3',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#F9DC43',
      textStrokeColor: null,
    },
    {
      fontColor: '#2D2D33',
      bgColor: '#FFFFFF',
      textStrokeColor: null,
    },
    {
      fontColor: '#F08BE9',
      bgColor: '#ADA0F2',
      textStrokeColor: '#FFFFFF',
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#572236',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#A6EAEB',
      textStrokeColor: '#18A7AA',
    },
    {
      fontColor: '#FFFC01',
      bgColor: '#ABE398',
      textStrokeColor: '#49B126',
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#E57269',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#F2A854',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#FBEA57',
      textStrokeColor: null,
    },
    {
      fontColor: '#FFFFFF',
      bgColor: '#7FCF65',
      textStrokeColor: null,
    },
    // {
    //   fontColor: '#FFFFFF',
    //   bgColor: '#A5BAF9',
    //   textStrokeColor: null,
    // },
  ]
}

export const getFontFamilyList = () => {
  return [
    {
      type: 0,
      name: 'Andale Mono',
      val: 'andale mono',
    },
    {
      type: 1,
      name: 'Open Sans',
      val: 'Open Sans',
    },
    {
      type: 2,
      name: 'Comic Sans Ms',
      val: 'comic sans ms',
    },
    {
      type: 3,
      name: 'Impact',
      val: 'impact,chicago',
    },
    {
      type: 4,
      name: 'Times New Roman',
      val: 'times new roman',
    },
    {
      type: 10,
      name: t('aiSubtitleStyleSourceHan'),
      val: '思源黑体,Source Han Sans',
    },
    {
      type: 5,
      name: t('aiSubtitleStyleSongTi'),
      val: '宋体,SimSun,Songti SC',
    },
    {
      type: 6,
      name: t('aiSubtitleStyleSimHei'),
      val: '黑体,SimHei,Heiti SC',
    },
    {
      type: 7,
      name: t('aiSubtitleStyleSimKai'),
      val: '楷体,楷体_GB2312,SimKai,STKaiti',
    },
    {
      type: 8,
      name: t('aiSubtitleStyleLishu'),
      val: '隶书,SimLi',
    },
    {
      type: 9,
      name: t('aiSubtitleStyleYaHei'),
      val: '',
    }
  ]
}

export const getDefaultFontFamily = () => {
  return getFontFamilyList().find(item => item.name === t('aiSubtitleStyleYaHei'))!.type
}


export const getFontNameByType = (type: number) => {
  return getFontFamilyList().find(item => item.type === type)!.name
}

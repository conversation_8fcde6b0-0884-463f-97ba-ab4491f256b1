<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 21.8 30.8" style="enable-background:new 0 0 21.8 30.8;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);}
	.st1{opacity:0.8;fill:url(#SVGID_3_);enable-background:new    ;}
	.st2{fill:none;stroke:#1FB5C9;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
</style>
<g>
	<defs>
		<rect id="SVGID_1_" width="21.8" height="30.8"/>
	</defs>
	<clipPath id="SVGID_2_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
	<g class="st0">
		
			<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="11.0227" y1="31.7556" x2="11.0227" y2="16.7808" gradientTransform="matrix(1 0 0 -1 0 31.8898)">
			<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.5"/>
			<stop  offset="1" style="stop-color:#1FB5C9"/>
		</linearGradient>
		<path class="st1" d="M8.8,1.2L3,11.4c-1,1.7,0.2,3.7,2.2,3.7h11.7c1.9,0,3.1-2.1,2.2-3.7l-6-10.2C12.1-0.4,9.7-0.4,8.8,1.2z"/>
		<path class="st2" d="M10.9,13v15.8"/>
		<path class="st2" d="M2,28.8h17.8"/>
	</g>
</g>
</svg>

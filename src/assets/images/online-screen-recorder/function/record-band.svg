<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="隔离模式" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
	 y="0px" width="228.167px" height="115.083px" viewBox="0 0 228.167 115.083" enable-background="new 0 0 228.167 115.083"
	 xml:space="preserve">
<g>
	<g>
		<path fill="#E9F1F4" d="M228.264,47.765c-50.402,74.983-129.59,76.577-174.809,55.06C5.415,80.792-8.656,35.648,5.999,5.29
			C17.332,8.987,29.627,7.938,41.08,3.054c-11.675,9.285-8.299,39.149,23.172,53.111c28.649,13.449,85.395,10.996,115.275-56.223
			C198.119,12.002,214.777,28.431,228.264,47.765z"/>
		<g>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M215.925,55.307
				C163.69,121.43,88.199,118.196,47.511,95.03C4,71.35-4.707,27.738,11.445,1.239c10.946,4.29,23.098,3.737,34.598-0.757
				c-12.66,5.275-14.164,33.448,13.23,48.895c24.572,14.932,78.044,17.139,110.154-41.388
				C187.387,20.041,203.281,36.298,215.925,55.307z"/>
			<path fill="none" stroke="#128ECC" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M11.541,32.628
				c-0.726-8.205,0.576-16.039,3.308-22.826c7.015,0.18,14.102-1.162,20.911-3.835c-2.566,3.547-4.273,8.398-4.43,13.894
				C25.381,25.204,18.695,29.527,11.541,32.628z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M36.367,7.606
				c0.547-0.96,1.154-1.846,1.81-2.649c0.634-0.28,1.265-0.573,1.894-0.876c-0.668,0.735-1.291,1.556-1.855,2.452
				C37.604,6.901,36.987,7.259,36.367,7.606z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M34.365,12.391
				c0.336-1.157,0.749-2.257,1.23-3.289c0.609-0.379,1.215-0.768,1.816-1.168c-0.502,0.969-0.939,2.008-1.302,3.107
				C35.534,11.501,34.953,11.951,34.365,12.391z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M33.415,17.921
				c0.082-1.309,0.257-2.575,0.515-3.789c0.573-0.469,1.141-0.947,1.703-1.436c-0.285,1.154-0.49,2.364-0.605,3.617
				C34.497,16.859,33.959,17.394,33.415,17.921z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M34.693,28.513
				c-0.43-1.451-0.753-2.887-0.973-4.299c0.489-0.605,0.97-1.22,1.442-1.841c0.178,1.364,0.456,2.757,0.842,4.164
				C35.576,27.203,35.139,27.861,34.693,28.513z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M37.376,35.1
				c-0.763-1.466-1.412-2.935-1.952-4.396c0.422-0.675,0.834-1.356,1.238-2.044c0.494,1.418,1.098,2.848,1.812,4.274
				C38.117,33.662,37.752,34.384,37.376,35.1z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M41.54,41.589
				c-1.103-1.43-2.091-2.88-2.966-4.341c0.352-0.735,0.692-1.476,1.023-2.222c0.821,1.422,1.756,2.835,2.803,4.229
				C42.123,40.039,41.837,40.816,41.54,41.589z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M52.694,52.247
				c-1.7-1.224-3.29-2.503-4.769-3.824c0.204-0.825,0.396-1.654,0.578-2.486c1.417,1.294,2.945,2.547,4.581,3.747
				C52.967,50.54,52.836,51.394,52.694,52.247z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M61.08,57.345
				c-2.019-1.029-3.936-2.133-5.744-3.299c0.11-0.863,0.209-1.73,0.296-2.599c1.745,1.144,3.597,2.226,5.549,3.235
				C61.158,55.572,61.125,56.458,61.08,57.345z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M70.752,61.393
				c-2.305-0.773-4.52-1.643-6.634-2.594c0.013-0.896,0.014-1.791,0.003-2.688c2.051,0.936,4.199,1.791,6.438,2.553
				C70.635,59.575,70.699,60.484,70.752,61.393z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M91.4,65.301
				c-2.738-0.153-5.41-0.44-8.008-0.848c-0.172-0.926-0.354-1.85-0.549-2.771c2.521,0.397,5.119,0.679,7.779,0.827
				C90.895,63.439,91.152,64.37,91.4,65.301z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M104.094,65.013
				c-2.895,0.284-5.746,0.413-8.541,0.399c-0.279-0.928-0.571-1.854-0.875-2.777c2.721,0.013,5.498-0.114,8.319-0.395
				C103.374,63.161,103.74,64.084,104.094,65.013z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M117.255,62.642
				c-2.987,0.786-5.957,1.394-8.889,1.837c-0.387-0.928-0.785-1.85-1.194-2.766c2.86-0.437,5.758-1.032,8.674-1.807
				C116.328,60.813,116.796,61.725,117.255,62.642z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M8.521,15.333
				c0.524-2.004,1.156-3.957,1.884-5.847c0.657,0.079,1.314,0.144,1.974,0.195c-0.707,1.805-1.321,3.674-1.832,5.596
				C9.872,15.309,9.197,15.328,8.521,15.333z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M6.91,24.681
				c0.182-2.138,0.484-4.24,0.898-6.295c0.684-0.047,1.365-0.107,2.046-0.18c-0.403,1.974-0.696,3.995-0.872,6.054
				C8.294,24.414,7.604,24.554,6.91,24.681z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M6.938,34.429
				c-0.197-2.219-0.263-4.418-0.203-6.586c0.694-0.168,1.385-0.348,2.074-0.54c-0.062,2.093-0.001,4.218,0.188,6.364
				C8.314,33.932,7.628,34.186,6.938,34.429z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M11.084,51.509
				c-0.9-2.242-1.658-4.495-2.275-6.746c0.676-0.355,1.346-0.721,2.01-1.096c0.6,2.184,1.336,4.371,2.213,6.551
				C12.389,50.658,11.739,51.088,11.084,51.509z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M16.109,61.508
				c-1.334-2.188-2.523-4.401-3.568-6.631c0.646-0.452,1.285-0.915,1.917-1.386c1.021,2.164,2.183,4.314,3.487,6.438
				C17.341,60.463,16.729,60.991,16.109,61.508z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M23.044,71.055
				c-1.768-2.077-3.392-4.2-4.869-6.357c0.603-0.55,1.196-1.105,1.782-1.669c1.443,2.099,3.031,4.163,4.76,6.185
				C24.168,69.833,23.61,70.448,23.044,71.055z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M40.06,86.333
				c-2.509-1.74-4.884-3.56-7.119-5.447c0.499-0.694,0.987-1.395,1.466-2.102c2.188,1.836,4.51,3.605,6.965,5.297
				C40.944,84.834,40.507,85.584,40.06,86.333z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M52.236,93.563
				c-2.896-1.464-5.669-3.029-8.311-4.684c0.422-0.77,0.832-1.543,1.232-2.321c2.588,1.61,5.305,3.134,8.143,4.558
				C52.957,91.935,52.602,92.75,52.236,93.563z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M65.951,99.375
				c-3.241-1.129-6.373-2.381-9.385-3.74c0.34-0.834,0.669-1.67,0.986-2.51c2.953,1.322,6.023,2.537,9.201,3.633
				C66.498,97.631,66.23,98.504,65.951,99.375z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M94.589,105.583
				c-3.757-0.354-7.436-0.873-11.024-1.539c0.166-0.932,0.319-1.862,0.461-2.797c3.517,0.637,7.121,1.125,10.802,1.455
				C94.76,103.665,94.68,104.625,94.589,105.583z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M111.966,106.042
				c-3.953,0.164-7.853,0.144-11.683-0.049c0.06-0.972,0.106-1.943,0.142-2.914c3.753,0.166,7.573,0.164,11.447-0.02
				C111.916,104.053,111.947,105.047,111.966,106.042z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M129.91,104.036
				c-4.07,0.748-8.115,1.289-12.113,1.637c-0.053-1.004-0.115-2.006-0.191-3.008c3.923-0.369,7.893-0.929,11.887-1.695
				C129.643,101.991,129.783,103.01,129.91,104.036z"/>
			<path fill="none" stroke="#128ECC" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M26.734,66.859
				C20.037,59.068,15.58,50.603,13.282,42.23c6.909-4.225,13.173-9.551,18.561-15.77c1.063,5.887,3.845,12.146,8.522,18.029
				C36.932,52.568,32.338,60.099,26.734,66.859z"/>
			<path fill="none" stroke="#128ECC" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M67.66,93.458
				c-12.374-4.19-23.023-10.262-31.504-17.347c4.812-7.646,8.5-15.921,10.949-24.593c6.188,5.427,14.236,10.145,23.795,13.307
				C71.149,74.469,70.06,84.097,67.66,93.458z"/>
			<path fill="none" stroke="#128ECC" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M128.843,97.167
				c-15.552,3.11-30.659,2.986-44.347,0.59c1.18-9.912,1.009-19.926-0.505-29.789c10.7,1.646,22.644,1.35,34.95-1.834
				C123.533,76.002,126.865,86.438,128.843,97.167z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M141.32,52.207
				c-2.971,1.855-5.973,3.49-8.982,4.92c-0.582-0.895-1.172-1.781-1.772-2.662c2.938-1.41,5.867-3.024,8.769-4.857
				C140.005,50.465,140.667,51.332,141.32,52.207z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M154.432,42.303
				c-2.834,2.554-5.729,4.863-8.664,6.942c-0.692-0.861-1.396-1.715-2.108-2.56c2.864-2.054,5.69-4.334,8.457-6.858
				C152.896,40.644,153.667,41.468,154.432,42.303z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M166.598,29.197
				c-2.597,3.326-5.291,6.381-8.06,9.183c-0.795-0.822-1.6-1.635-2.41-2.437c2.695-2.771,5.32-5.793,7.848-9.083
				C164.857,27.627,165.732,28.406,166.598,29.197z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M162.781,93.282
				c-4.08,1.977-8.189,3.7-12.302,5.188c-0.267-1.043-0.546-2.084-0.837-3.121c4.033-1.495,8.061-3.225,12.061-5.202
				C162.074,91.187,162.433,92.232,162.781,93.282z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M180.889,82.638
				c-3.943,2.764-7.947,5.253-11.991,7.481c-0.396-1.051-0.802-2.097-1.22-3.137c3.962-2.23,7.885-4.717,11.743-7.477
				C179.921,80.543,180.41,81.588,180.889,82.638z"/>
			<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M198.021,68.401
				c-3.702,3.622-7.505,6.943-11.383,9.979c-0.521-1.049-1.053-2.092-1.594-3.129c3.793-3.035,7.511-6.354,11.127-9.974
				C196.8,66.312,197.416,67.353,198.021,68.401z"/>
			<path fill="none" stroke="#128ECC" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M193.777,61.456
				c-14.016,14.371-29.607,24.028-45.273,30.028c-3.397-10.875-8.112-21.282-14.018-30.948
				c12.369-5.782,24.621-14.867,35.346-28.298C178.813,40.886,186.864,50.722,193.777,61.456z"/>
		</g>
	</g>
</g>
</svg>

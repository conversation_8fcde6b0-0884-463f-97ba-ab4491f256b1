<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="153.389px" height="127.887px" viewBox="0 0 153.389 127.887" enable-background="new 0 0 153.389 127.887"
	 xml:space="preserve">
<g display="none">
	<g display="inline">
		<rect x="-6.6" y="-286.648" fill="#FFFFFF" width="371.18" height="268.59"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="-6.15" y1="-286.648" x2="53.115" y2="-286.648"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="-6.6" y1="-286.648" x2="-6.6" y2="-227.381"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="364.58" y1="-18.059" x2="305.312" y2="-18.059"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="365.027" y1="-18.059" x2="365.027" y2="-77.324"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="364.58" y1="-286.648" x2="364.568" y2="-227.029"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="364.58" y1="-286.648" x2="305.302" y2="-286.743"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="-6.145" y1="-18.281" x2="-6.145" y2="-77.546"/>
		
			<line fill="none" stroke="#EF3543" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="-6.145" y1="-17.832" x2="53.123" y2="-17.832"/>
		<g>
			<path fill="#FFFFFF" d="M193.09-153.477c0,0,4.504-1.233,5.771-4.911"/>
			<path fill="#FFFFFF" d="M194.86-150.445c0,0,2.646-0.014,4.028-3.063"/>
			<path fill="#FFFFFF" d="M142.103-153.21c0,0-4.521-1.188-5.828-4.853"/>
			<path fill="#FFFFFF" d="M140.363-150.159c0,0-2.654,0.012-4.06-3.021"/>
			<path fill="#FFFFFF" d="M172.762-105.748l-7.982,0.045c-3.838,0.021-6.979-3.092-6.995-6.925
				c-0.022-3.841,3.08-6.979,6.921-6.998l7.982-0.041c3.841-0.024,6.977,3.085,7.002,6.923
				C179.707-108.905,176.6-105.764,172.762-105.748z M164.717-117.108c-2.446,0.012-4.434,2.018-4.418,4.465
				c0.015,2.45,2.015,4.431,4.466,4.423l7.986-0.043c2.452-0.01,4.432-2.015,4.415-4.467c-0.009-2.452-2.011-4.433-4.463-4.42
				L164.717-117.108z"/>
			<path fill="none" stroke="#000000" stroke-width="12.8402" stroke-miterlimit="10" d="M223.407-171.671"/>
			<path fill="none" stroke="#000000" stroke-width="12.8402" stroke-miterlimit="10" d="M113.073-171.098"/>
		</g>
		<g>
			<path fill="#EF3543" d="M306.049-263.927c1.119-0.232,2.721-0.358,4.246-0.358c2.362,0,3.889,0.435,4.956,1.4
				c0.866,0.762,1.348,1.934,1.348,3.254c0,2.265-1.421,3.762-3.229,4.375v0.076c1.323,0.458,2.11,1.677,2.519,3.457
				c0.559,2.39,0.969,4.043,1.32,4.705h-2.289c-0.278-0.481-0.66-1.958-1.142-4.094c-0.512-2.362-1.428-3.254-3.435-3.331h-2.086
				v7.425h-2.21L306.049-263.927L306.049-263.927z M308.259-256.123h2.264c2.367,0,3.866-1.298,3.866-3.257
				c0-2.21-1.602-3.179-3.94-3.201c-1.067,0-1.83,0.103-2.188,0.203L308.259-256.123L308.259-256.123z"/>
			<path fill="#EF3543" d="M328.61-255.055h-6.661v6.179h7.425v1.856h-9.639v-17.141h9.257v1.856h-7.043v5.417h6.661V-255.055
				L328.61-255.055z"/>
			<path fill="#EF3543" d="M344.23-247.579c-0.813,0.406-2.442,0.813-4.528,0.813c-4.83,0-8.466-3.051-8.466-8.67
				c0-5.364,3.636-9.003,8.949-9.003c2.137,0,3.483,0.458,4.067,0.764l-0.531,1.808c-0.843-0.408-2.037-0.713-3.46-0.713
				c-4.017,0-6.688,2.566-6.688,7.069c0,4.192,2.415,6.89,6.585,6.89c1.347,0,2.72-0.278,3.61-0.713L344.23-247.579z"/>
		</g>
	</g>
	<g display="inline">
		<path opacity="0.3" fill="#5B6B75" enable-background="new    " d="M111.798-84.218c0.014-8.918,1.189-17.481,3.314-25.45
			c-9.495-10.289-22.38-16.635-36.597-16.656c-27.666-0.041-50.402,23.849-52.822,54.342l86.844,0.136
			C112.05-75.875,111.789-80.003,111.798-84.218z"/>
		<path opacity="0.5" fill="#5B6B75" enable-background="new    " d="M220.634-78.916c-0.008-5.229-0.695-10.247-1.94-14.915
			c5.562-6.028,13.115-9.747,21.444-9.76c16.211-0.026,29.532,13.975,30.95,31.843L220.2-71.669
			C220.486-74.031,220.64-76.449,220.634-78.916z"/>
		<path fill="#5B6B75" d="M170.76-161.486c-25.711-0.041-47.589,21.614-55.646,51.818c-2.125,7.969-3.302,16.532-3.314,25.45
			c-0.009,4.215,0.252,8.343,0.739,12.37l18.639,0.028l97.533,0.149c0.499-4.027,0.771-8.154,0.778-12.367
			C229.553-126.76,203.261-161.438,170.76-161.486z"/>
	</g>
	<path display="inline" fill="#FFDA40" d="M80.917-224.064c0,8.418-6.827,15.244-15.242,15.244c-8.42,0-15.245-6.826-15.245-15.244
		s6.825-15.245,15.245-15.245C74.09-239.309,80.917-232.482,80.917-224.064z"/>
</g>
<g id="Forma_1_14_">
	<g>
		<path fill="#FFFFFF" d="M24.684,41.265v60.198c0,6.093,21.165,16.119,47.257,16.119c26.099,0,47.258-10.026,47.258-16.119V41.265
			c-13.389,2.989-30.021,4.661-47.258,4.661C54.711,45.926,38.061,44.254,24.684,41.265z M71.94,0.479
			C32.538,0.479,0.6,8.998,0.6,19.5c0,5.656,9.345,10.733,24.083,14.215c12.596,2.976,29.111,4.799,47.257,4.799
			s34.658-1.823,47.258-4.799c14.737-3.48,24.093-8.559,24.093-14.215C143.291,8.997,111.34,0.479,71.94,0.479z"/>
	</g>
</g>
<g id="Forma_1_12_">
	<g>
		<path fill="#EDF2F4" d="M34.78,51.569v60.198c0,6.094,21.166,16.119,47.258,16.119c26.097,0,47.258-10.025,47.258-16.119V51.569
			c-13.389,2.989-30.021,4.661-47.258,4.661C64.809,56.23,48.158,54.559,34.78,51.569z M82.038,10.784
			c-39.401,0-71.34,8.518-71.34,19.021c0,5.655,9.345,10.733,24.082,14.215c12.597,2.976,29.113,4.798,47.258,4.798
			c18.146,0,34.658-1.822,47.258-4.798c14.737-3.48,24.093-8.56,24.093-14.215C153.388,19.302,121.438,10.784,82.038,10.784z"/>
	</g>
</g>
<g id="Forma_1_10_">
	<g>
		<path fill="none" stroke="#5B6B75" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M24.684,41.265
			v60.198c0,6.093,21.165,16.119,47.257,16.119c26.099,0,47.258-10.026,47.258-16.119V41.265
			c-13.389,2.989-30.021,4.661-47.258,4.661C54.711,45.926,38.061,44.254,24.684,41.265z M71.94,0.479
			C32.538,0.479,0.6,8.998,0.6,19.5c0,5.656,9.345,10.733,24.083,14.215c12.596,2.976,29.111,4.799,47.257,4.799
			s34.658-1.823,47.258-4.799c14.737-3.48,24.093-8.559,24.093-14.215C143.291,8.997,111.34,0.479,71.94,0.479z"/>
	</g>
</g>
<path display="none" fill="none" stroke="#128ECC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="
	M25.693-59.448h245.395c0,0-143.955,10.336-147.838,16.563c-3.884,6.224,87.104,12.759,87.104,12.759"/>
</svg>

<svg width="163" height="141" viewBox="0 0 163 141" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path opacity="0.3"
        d="M58.4437 53.2636C57.1484 46.9489 63.5176 41.8024 69.4195 44.3948L143.049 76.736C148.97 79.3366 149.468 87.5443 143.905 90.8417L84.1728 126.249C79.4771 129.033 73.4379 126.36 72.341 121.013L58.4437 53.2636Z"
        fill="url(#paint0_linear_266_2145)"/>
  <g filter="url(#filter0_b_266_2145)">
    <circle cx="49.9694" cy="59.4169" r="43.7348" transform="rotate(-8.55124 49.9694 59.4169)" fill="#18AD25"
            fill-opacity="0.1"/>
    <circle cx="49.9694" cy="59.4169" r="42.9912" transform="rotate(-8.55124 49.9694 59.4169)" stroke="white"
            stroke-opacity="0.6" stroke-width="1.48713"/>
  </g>
  <defs>
    <filter id="filter0_b_266_2145" x="-2.69416" y="6.75335" width="105.327" height="105.327"
            filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="4.4614"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_266_2145"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_266_2145" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_266_2145" x1="67.4507" y1="84.3351" x2="130.488" y2="63.1371"
                    gradientUnits="userSpaceOnUse">
      <stop offset="1" stop-color="#56E8B3"/>
    </linearGradient>
  </defs>
</svg>

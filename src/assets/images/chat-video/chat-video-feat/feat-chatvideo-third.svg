<svg width="1920" height="975" viewBox="0 0 1920 975" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="975" fill="#18AD25" fill-opacity="0.03"/>
<mask id="mask0_10045_6144" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1920" height="975">
<rect width="1920" height="975" fill="#18AD25"/>
</mask>
<g mask="url(#mask0_10045_6144)">
<g opacity="0.6">
<mask id="mask1_10045_6144" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1123" y="338" width="1026" height="711">
<rect x="1123" y="338" width="1025.18" height="710.758" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask1_10045_6144)">
<g opacity="0.7">
<circle cx="310.969" cy="310.969" r="310.969" transform="matrix(-0.866025 -0.5 -0.5 0.866025 2126.79 877.426)" fill="url(#paint0_linear_10045_6144)"/>
<circle opacity="0.7" cx="203.858" cy="203.858" r="203.858" transform="matrix(-0.866025 -0.5 -0.5 0.866025 1613.89 1143.86)" fill="url(#paint1_linear_10045_6144)"/>
<circle cx="64.7853" cy="64.7853" r="64.7853" transform="matrix(-0.866025 -0.5 -0.5 0.866025 1660.64 558.422)" fill="url(#paint2_linear_10045_6144)"/>
</g>
</g>
</g>
<g opacity="0.6">
<mask id="mask2_10045_6144" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-270" y="-69" width="786" height="545">
<rect x="516" y="475.933" width="786" height="544.933" transform="rotate(-180 516 475.933)" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask2_10045_6144)">
<g opacity="0.7">
<circle cx="238.418" cy="238.418" r="238.418" transform="matrix(0.866025 0.5 0.5 -0.866025 -253.603 62.3601)" fill="url(#paint3_linear_10045_6144)"/>
<circle opacity="0.7" cx="156.296" cy="156.296" r="156.296" transform="matrix(0.866025 0.5 0.5 -0.866025 139.636 -141.911)" fill="url(#paint4_linear_10045_6144)"/>
<circle cx="49.6704" cy="49.6704" r="49.6704" transform="matrix(0.866025 0.5 0.5 -0.866025 103.792 306.938)" fill="url(#paint5_linear_10045_6144)"/>
</g>
</g>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_10045_6144" x1="310.969" y1="621.938" x2="310.969" y2="-2.89613e-07" gradientUnits="userSpaceOnUse">
<stop stop-color="#02FE0C" stop-opacity="0.2"/>
<stop offset="1" stop-color="#07FE02" stop-opacity="0.05"/>
</linearGradient>
<linearGradient id="paint1_linear_10045_6144" x1="203.858" y1="0" x2="203.858" y2="407.715" gradientUnits="userSpaceOnUse">
<stop stop-color="#0270FE" stop-opacity="0.03"/>
<stop offset="1" stop-color="#0270FE" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint2_linear_10045_6144" x1="64.7853" y1="129.571" x2="64.7853" y2="-6.0336e-08" gradientUnits="userSpaceOnUse">
<stop stop-color="#02FE0C" stop-opacity="0.2"/>
<stop offset="1" stop-color="#07FE02" stop-opacity="0.05"/>
</linearGradient>
<linearGradient id="paint3_linear_10045_6144" x1="238.418" y1="476.836" x2="238.418" y2="-2.22044e-07" gradientUnits="userSpaceOnUse">
<stop stop-color="#02FE0C" stop-opacity="0.2"/>
<stop offset="1" stop-color="#07FE02" stop-opacity="0.05"/>
</linearGradient>
<linearGradient id="paint4_linear_10045_6144" x1="156.296" y1="0" x2="156.296" y2="312.592" gradientUnits="userSpaceOnUse">
<stop stop-color="#0270FE" stop-opacity="0.03"/>
<stop offset="1" stop-color="#0270FE" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint5_linear_10045_6144" x1="49.6704" y1="99.3408" x2="49.6704" y2="-4.62592e-08" gradientUnits="userSpaceOnUse">
<stop stop-color="#02FE0C" stop-opacity="0.2"/>
<stop offset="1" stop-color="#07FE02" stop-opacity="0.05"/>
</linearGradient>
</defs>
</svg>

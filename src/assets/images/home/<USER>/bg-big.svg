<svg width="1920" height="697" viewBox="0 0 1920 697" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_11213_1619" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1920" height="697">
<rect width="1920" height="697" fill="#18AD25"/>
</mask>
<g mask="url(#mask0_11213_1619)">
<g opacity="0.3" filter="url(#filter0_f_11213_1619)">
<circle cx="1266" cy="872" r="396" fill="#56E8B3"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_11213_1619)">
<circle cx="1770" cy="-75" r="488" fill="url(#paint0_linear_11213_1619)"/>
</g>
<g opacity="0.2" filter="url(#filter2_f_11213_1619)">
<circle cx="257" cy="-125" r="432" fill="#56E8B3"/>
</g>
</g>
<defs>
<filter id="filter0_f_11213_1619" x="670" y="276" width="1192" height="1192" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_11213_1619"/>
</filter>
<filter id="filter1_f_11213_1619" x="782" y="-1063" width="1976" height="1976" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_11213_1619"/>
</filter>
<filter id="filter2_f_11213_1619" x="-375" y="-757" width="1264" height="1264" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_11213_1619"/>
</filter>
<linearGradient id="paint0_linear_11213_1619" x1="1395.58" y1="203.322" x2="2176.87" y2="-280.933" gradientUnits="userSpaceOnUse">
<stop stop-color="#56E8B3"/>
<stop offset="1" stop-color="#18AD25"/>
</linearGradient>
</defs>
</svg>

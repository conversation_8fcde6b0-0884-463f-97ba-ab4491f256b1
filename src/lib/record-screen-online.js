import $ from 'jquery'
import {getLang} from "@central/i18n";
import {getPageName, trackUpload} from "../track/_TrackUtil.ts";
import useLoginService, {recToken, updateUserinfo} from "../services/useLoginService";
import {getVipPageUrl} from "../utils/open";
import http from "../services/http";
import './fix-webm-duration.js'
import {putVideoInfo, uploadFile} from "../api/recOSSApi";
import {getRegionPath} from "../utils/util";
import {getDuration} from "@/utils/browser";

const {isLoginRef, doLogin, checkLimit, userIdRef, isVipRef} = useLoginService()
let globalText = {}
/** 插件安装弹窗 */
function _pluginDialog() {

  function addPluginDom() {
    const pluginDialog = document.querySelector('#plugin-rec-notice-dialog')
    if (pluginDialog) {
      return
    }
    const el = document.createElement('div');
    el.classList.add('plugin-rec-notice');
    el.id = 'plugin-rec-notice-dialog';
    el.innerHTML = `
        <div class="close" id="plugin-rec-close-btn"></div>
        <div class="content">
            <div class="logo"></div>
            <div class="title">
                ${globalText.plugin.title}
            </div>
        </div>
        <div class="install" id="plugin-rec-install-btn">${globalText.plugin.btn}</div>
      `
    document.body.appendChild(el);
  }

  checkEnablePlugin()

  function checkEnablePlugin() {

    const enableNotice = window.localStorage.getItem("plugin-rec-enable-notice");
    const installedPlugin = document.querySelector("body.rec-cloud-plugin-enable");
    if (!enableNotice && !installedPlugin) {
      addPluginDom()

      const closeBtn = document.querySelector("#plugin-rec-close-btn");
      if (closeBtn) {
        closeBtn.addEventListener("click", function () {
          window.localStorage.setItem("plugin-rec-enable-notice", "true");
          const dialog = document.querySelector("#plugin-rec-notice-dialog");
          dialog && dialog.remove();
        });
      }


      const installBtn = document.querySelector("#plugin-rec-install-btn");
      if (installBtn) {
        installBtn.addEventListener("click", function () {
          trackUpload({
            event: "button_click",
            button_name: "extension_install_btn"
          }).then()
          const url = "https://chrome.google.com/webstore/detail/reccloud-online-screen-re/noobnjlihannbcbdmfdoooeghacggmmn?utm_source=chrome-ntp-icon";
          window.open(url, "_blank");
        });
      }

      const pluginInstallDialog = document.querySelector("#plugin-rec-notice-dialog");
      const logo = pluginInstallDialog.querySelector(".content .logo");
      if (pluginInstallDialog) {
        window.requestAnimationFrame(time => {
          pluginInstallDialog.classList.add("active");
          if (logo) {
            logo.classList.add("active");
          }
        });
      }
    }
  }
}

/** 插件评论弹窗 */
function _pluginReview() {
  addPluginDom()
  checkEnableReview()

  function addPluginDom() {
    const pluginDialog = document.querySelector('#review-plugin-notice-id')
    if (pluginDialog) {
      return
    }
    const el = document.createElement('div');
    el.classList.add('review-plugin-notice');
    el.id = 'review-plugin-notice-id';
    el.innerHTML = `
        <div class="layout-container">
            <div class="logo"></div>
            <div class="title-area">
                <div class="title">
                    ${globalText.review.title}
                </div>
                <div class="subtitle">
                    ${globalText.review.desc}
                </div>
            </div>

            <div class="to-review" id="to-review-id">
                 ${globalText.review.btn}
            </div>

            <div class="close" id="review-close"></div>
        </div>
      `
    document.body.appendChild(el);
  }

  function checkEnableReview() {
    const checkTimeAndNum = localStorage.getItem("checkEnableReview") || "true";
    if (checkTimeAndNum === "true") {
      if (_checkInstallPluginTime(3) || _checkRecordNum()) {
        //已经点击评价过了
        if (localStorage.getItem("plugin-rec-review-success")) {
          return;
        }
        //判断是否已经7天了
        enableDialog();
        localStorage.setItem("checkEnableReview", "false");
        window.localStorage.setItem("plugin-rec-install-time", Date.now().toString());
      }
    } else {
      if (_checkInstallPluginTime(3)) {
        //已经点击评价过了
        if (localStorage.getItem("plugin-rec-review-success")) {
          return;
        }
        enableDialog();
        window.localStorage.setItem("plugin-rec-install-time", Date.now().toString());
      }
    }

    function enableDialog() {
      const dialog = document.querySelector("#review-plugin-notice-id");
      dialog.classList.add("active");
      trackUpload({
        event: "show_extension_comment"
      }).then();

      const url = "https://chrome.google.com/webstore/detail/reccloud-online-screen-re/noobnjlihannbcbdmfdoooeghacggmmn/reviews";
      const reviewBtn = dialog.querySelector("#to-review-id");
      reviewBtn.onclick = function () {
        trackUpload({
          event: "button_click",
          button_name: "to_review_btn"
        }).then();
        window.localStorage.setItem("plugin-rec-review-success", "true");
        window.open(url, "_blank");
        dialog.classList.remove("active");
      };

      const closeBtn = dialog.querySelector("#review-close");
      closeBtn.onclick = function () {
        dialog.classList.remove("active");
      };
    }
  }

  /**
   * 检查插件安装时间是否超过3天
   * @param dayNum {number} 天数
   * @return {boolean} true: 超过3天，false: 未超过3天
   */
  function _checkInstallPluginTime(dayNum) {
    const installTime = window.localStorage.getItem("plugin-rec-install-time");
    if (!installTime) {
      window.localStorage.setItem("plugin-rec-install-time", Date.now().toString());
      return false;
    }
    const now = Date.now();
    const diff = now - installTime;
    // const day = 24 * 60 * 60 * 1000;
    //TODO 换成3min
    const day = 60 * 1000;
    // const day = 60 * 1000;
    return diff > dayNum * day;
  }

  /**
   * 检查start-record元素是否点击了5次
   * @private
   * @return {boolean} true: 点击了5次，false: 未点击5次
   */
  function _checkRecordNum() {
    const recordNum = window.localStorage.getItem("plugin-rec-record-num");
    return Number(recordNum) >= 3;
  }

}

function _recordInit(text) {
  globalText = text
  let gumStream;
  let gdmStream;
  let recorder;
  let recordingData = [];
  let recorderStream;
  let cameraStream;
  const isMac = /Mac/i.test(window.navigator.userAgent);
  let useMic = false;
  let useAudio = isMac ? false : true;
  let useCamera = false;
  let cameraIniting = false;
  const width = 480;
  const height = 320;
  let recordedSeconds = 0;
  let recordedCountTime = 0;
  let videoName = null;
  let recordStatus = 'setting';
  let client;
  let hasCamera;
  let hasMicrophone;
  let countTimer = null;
  let uploadingStatus = 'uploading';
  let isReadyCounting = false;
  let fileUploading = false;
  let bindFn = null;
  let formatedVideoUrl;
  let fixedVideoBlob = null;
  let userMediaOptions = {video: true, audio: false};
  let displayMediaOptions = {video: false, audio: false};
  const previewVideo = document.getElementById('preview-video');
  const cameraVideo = document.getElementById('camera-video');
  const langMap = new Map([['ar', 'en'], ['el', 'en'], ['cs', 'cz'], ['ja', 'jp'], ['da', 'dk'], ['sv', 'se'], ['kr', 'en']])
  const lang = getLang()

  const inBrowser = typeof window !== "undefined";
  const UA = inBrowser && window.navigator.userAgent.toLowerCase();
  const isEdge = UA && UA.indexOf("edge/") > 0;
  const isFirefox = UA && UA.indexOf("firefox") > 0;
  const isSafari = UA && UA.indexOf("chrome") < 0 && UA.indexOf("safari") > 0;
  const isChrome = UA && /chrome\/\d+/.test(UA) && !isEdge;

  let enableRemoveLongTimeDialog = false;
  let mouseInLongTimeDialog = false;

  let finalDuration = 0;//上报的视频时长,单位ms

  (function checkDevices() {
    triggerMediaChecked();
    if (navigator.mediaDevices) {
      navigator.mediaDevices.enumerateDevices().then(devices => {
        devices.forEach(device => {
          if (device.kind === "videoinput") hasCamera = true;
          if (device.kind === "audioinput") hasMicrophone = true;
        })

        const cameraContainer = document.querySelector(".camera-devices-list-container");
        if (cameraContainer) {
          if (cameraContainer.childElementCount === 0) {
            devices.filter(value => {
              return value.kind === "videoinput" && value.deviceId.length > 20;
            }).forEach((device, index, array) => {
              if (array.length > 1) {
                document.querySelector(".camera-device-title").classList.add("active");
              }
              buildDeviceListEl(device, cameraContainer);
            });
          }
        }


        const container = document.querySelector(".devices-list-container");
        if (container) {
          if (container.childElementCount === 0) {
            devices.filter(value => {
              return value.kind === "audioinput" && value.deviceId.length > 20;
            }).filter(value => {
              return value.label.toLowerCase().includes("Virtual".toLowerCase()) === false;
            }).reduce((prev, cur) => {
              let exist = prev.find(item => item.label === cur.label);
              if (!exist) {
                prev.push(cur);
              }
              return prev;
            }, []).forEach((device, index, array) => {
              if (array.length > 1) {
                document.querySelector(".device-title").classList.add("active");
              }
              buildDeviceListEl(device, container);
            });
          }
        }


        if (hasMicrophone) {
          //默认勾选麦克风
          useMic = true
          triggerMediaChecked();
          initMicDevice()
        }
      })
    }
  })()

  /**
   *
   * @param device {InputDeviceInfo}
   * @param container {HTMLElement}
   */
  function buildDeviceListEl(device, container) {
    if (device.label) {
      const li = document.createElement("li");
      li.classList.add("device-item");
      li.dataset.deviceId = device.deviceId;
      li.textContent = device.label;
      container.appendChild(li);
    }
  }

  function showDeviceList() {
    const container = document.querySelector(".devices-list-container");
    container.style.visibility = "visible";
  }

  function hiddenDeviceList(selector = ".devices-list-container") {
    const container = document.querySelector(selector);
    container.style.visibility = "hidden";
  }

  function toggleDeviceList(selector = ".devices-list-container") {
    const container = document.querySelector(selector);
    const status = getComputedStyle(container).visibility;
    if (status === "hidden") {
      container.style.visibility = "visible";
    } else {
      container.style.visibility = "hidden";
    }
  }

  $(".update-device").off("click").on("click", function (ev) {
    const container = document.querySelector(".devices-list-container");
    if (container.childElementCount > 1) {
      ev.stopPropagation();
      toggleDeviceList();
    }
  });

  $("#device-name").off("click").on("click", function (ev) {
    const container = document.querySelector(".devices-list-container");
    if (container.childElementCount > 1) {
      ev.stopPropagation();
      toggleDeviceList();
    }
  });

  $("#camera-item-device").off("click").on("click", function (ev) {
    const container = document.querySelector(".camera-devices-list-container");
    if (container.childElementCount > 1) {
      ev.stopPropagation();
      toggleDeviceList(".camera-devices-list-container");
    }
  });


  //点击空白区域关闭设备选择列表
  document.body.onclick = function () {
    hiddenDeviceList();
    hiddenDeviceList(".camera-devices-list-container");
  };

  $("#device-list").off("click").on("click", function (ev) {
    ev.stopPropagation();
    if (ev.target) {
      const isItem = ev.target.classList.contains("device-item");
      if (isItem) {
        const list = document.querySelector("#device-list");
        list.dataset.deviceId = ev.target.dataset.deviceId;
        const deviceName = document.querySelector("#device-name");
        deviceName.textContent = ev.target.textContent;
        hiddenDeviceList();
        useMic = true;
        triggerMediaChecked();
      }
    }
  });
  $("#camera-device-list").off("click").on("click", function (ev) {
    ev.stopPropagation();
    if (ev.target) {
      const isItem = ev.target.classList.contains("device-item");
      if (isItem) {
        const list = document.querySelector("#camera-device-list");
        list.dataset.deviceId = ev.target.dataset.deviceId;
        const deviceName = document.querySelector("#camera-device-name");
        deviceName.textContent = ev.target.textContent;
        hiddenDeviceList(".camera-devices-list-container");
        useCamera = true;
        triggerMediaChecked();
        changeUseCamera();
      }
    }
  });

  function initMicDevice() {
    const list = document.querySelector("#device-list");
    if (list && list.children[0] && Boolean(list.dataset.deviceId) === false) {
      list.children[0].click();
    }
  }

  function initCameraDevice() {
    const list = document.querySelector("#camera-device-list");
    if (list && list.children[0] && Boolean(list.dataset.deviceId) === false) {
      list.children[0].click();
    }
  }

  function logStep(msg) {
    console.log(msg)
  }

  if (!window["CDNDOMAIN"]) {
    window["CDNDOMAIN"] = "https://cdn.aoscdn.com";
  }

  function triggerMediaChecked() {
    if (useCamera) {
      $("#trigger-camera").addClass('active');
      $("#chooseCamera").addClass('active');
    } else {
      $("#trigger-camera").removeClass('active');
      $("#chooseCamera").removeClass('active');
    }
    if (useMic) {
      $("#trigger-microphone").addClass('active');
      $("#chooseMic").addClass('active');
    } else {
      $("#trigger-microphone").removeClass('active');
      $("#chooseMic").removeClass('active');
    }
    if (useAudio) {
      $("#trigger-sound").addClass('active');
      $("#chooseAudio").addClass('active');
    } else {
      $("#trigger-sound").removeClass('active');
      $("#chooseAudio").removeClass('active');
    }
  }

  $("#trigger-screen").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "trigger_screen_btn"
    })
    $("#getAppTip").css('display', 'flex');
    $("#getAppDefaultContent").show();
    $("#macAudioNotSupport").css('display', 'none');
    $("#noDisplayMedia").css('display', 'none');
  })
  $("#trigger-camera").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "trigger_camera_btn"
    })
    if (hasCamera) {
      if (cameraIniting) return;
      useCamera = !useCamera;
      if (useCamera) {
        initCameraDevice();
      }
      triggerMediaChecked();
      changeUseCamera();
    } else {
      $("#noCameraTip").css('display', 'flex');
    }
  })
  $("#trigger-sound").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "trigger_sound_btn"
    })
    useAudio = !useAudio;
    if (isMac && useAudio) {
      $("#getAppTip").css('display', 'flex');
      $("#getAppDefaultContent").css('display', 'none');
      $("#noDisplayMedia").css('display', 'none');
      $("#macAudioNotSupport").show();
    }
    triggerMediaChecked();
  })
  $("#trigger-microphone").off("click").on('click', async () => {
    trackUpload({
      event: "button_click",
      button_name: "trigger_microphone_btn"
    })
    if (hasMicrophone) {
      useMic = !useMic;
      if (useMic) {
        initMicDevice();
      }
      triggerMediaChecked();
    } else {
      $("#noMicTip").css('display', 'flex');
    }
  })
  $("#closeGetAppDialogIcon").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "close_app_dialog_btn"
    })
    $("#getAppTip").css('display', 'none');
  })

  function changeUseCamera() {
    if (useCamera) {
      initPreviewCamera();
      if ('pictureInPictureEnabled' in document) {
        cameraVideo.style.opacity = 0;
        cameraVideo.style.zIndex = -10;
        cameraVideo.style.display = 'none';
      } else {
        cameraVideo.style.opacity = 1;
        cameraVideo.style.zIndex = 1000;
        cameraVideo.style.display = 'block';
      }
    } else {
      cancelPreviewCamera();
    }
  }

  // if need double click camera view to enter picture-in-picture mode
  // $("#camera-video").dblclick(function() {
  // 	console.log('db')
  // })
  async function initPreviewCamera() {
    cameraIniting = true;
    try {
      const container = document.querySelector("#camera-device-list");
      const constraints = container.dataset.deviceId ? {
        video: {
          deviceId: container.dataset.deviceId
        }
      } : {video: true};
      cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
      cameraVideo.srcObject = cameraStream;
      cameraVideo.addEventListener('canplaythrough', () => {
        cameraIniting = false;
        if ('pictureInPictureEnabled' in document) cameraVideo.requestPictureInPicture();
      })
      // when enter picture-in-picture mode
      cameraVideo.addEventListener('enterpictureinpicture', function (event) {
        cameraVideo.style.opacity = 0;
        cameraVideo.style.zIndex = -10;
      });
      // when exit picture-in-picture mode
      cameraVideo.addEventListener('leavepictureinpicture', function (event) {
        cancelPreviewCamera();
      });
    } catch (e) {
      // 授权失败弹窗
      showNotAllowedTip();
      cameraIniting = false;
      useCamera = false;
      $("#trigger-camera").removeClass('active');
    }
  }

  function cancelPreviewCamera() {
    useCamera = false;
    $("#trigger-camera").removeClass('active');
    if (document.pictureInPictureElement) document.exitPictureInPicture();
    cameraVideo.style.display = 'none';
    cameraVideo.style.opacity = 0;
    cameraVideo.style.zIndex = -10;
    let stream = cameraVideo.srcObject;
    if (stream) {
      let tracks = stream.getTracks();
      for (let i = 0; i < tracks.length; i++) {
        let track = tracks[i];
        track.stop();
      }
    }
    cameraVideo.srcObject = null;
  }

  function disableHeadBgcWithMyVideoPage() {
    $(".header.video-header").addClass('active');
  }

  function enableHeadBgcWithMyVideoPage() {
    $(".header.video-header").removeClass('active');
  }


  function disableScrollInVideoPage() {
    document.body.style.overflow = "hidden"
  }

  function enableScrollInVideoPage() {
    document.body.style.overflow = "unset"
  }

  startView()

  //我的视频列表页面的录制按钮
  function startView() {
    const container = document.querySelector('#onlineScreenBox')
    if (container) {
      container.style.height = '100vh'
    }
    setTimeout(() => {
      $("#onlineScreenBox").css('position', 'static');
    }, 500)
    if (recordStatus === 'setting') {
      $("#onlineScreenBox").css('display', 'flex');
      disableHeadBgcWithMyVideoPage();
      $("#oldMainBox").hide();
    }
    $("#stop-record").removeAttr("disabled");
    $("#pause-record").removeAttr("disabled");

    disableScrollInVideoPage()
    _pluginDialog()
    _pluginReview()
  }

  //首页点击录制按钮 先检查登录情况
  $("#online-startRecording").off("click").on("click", () => {
    disableOverflowX()
    disableMyVideoInHomePage()

    _handleCheckStartRecording(function () {
      setTimeout(() => {
        $("#onlineScreenBox").css('position', 'static');
      }, 500)
      if (recordStatus === 'setting') {
        $("#onlineScreenBox").css('display', 'flex');
        $("#oldMainBox").hide();
      }
      $("#stop-record").removeAttr("disabled");
      $("#pause-record").removeAttr("disabled");
      $('.reccloud-animate.banner').hide()
    }).then(() => {
      _pluginDialog()
      _pluginReview()
    })
  })

  //首页不显示我的视频按钮
  function disableMyVideoInHomePage() {
    const myVideoDom = document.querySelector('.nav.video-nav > .personal-space.my-videos')
    if (myVideoDom) {
      myVideoDom.style.display = 'none'
    }
  }

  function enableMyVideoInHomePage() {
    const myVideoDom = document.querySelector('.nav.video-nav > .personal-space.my-videos')
    if (myVideoDom) {
      myVideoDom.style.display = 'block'
    }
  }

  function disableOverflowX() {
    const dom = document.querySelector('body.reccloud')
    if (dom) {
      dom.classList.add('disable-overflow-x')
    }
  }

  function removeOverflowX() {
    const dom = document.querySelector('body.reccloud')
    if (dom) {
      dom.classList.remove('disable-overflow-x')
    }
  }

  //检查是否登录
  async function _handleCheckStartRecording(callback, mustLogin = false) {
    if (!callback) return

    if (mustLogin === false) {
      callback()
      return
    }

    if (!isLoginRef.value) {
      await doLogin()
      const enable = await checkLimit()
      if (enable) {
        callback()
      }
    } else {
      const enable = await checkLimit()
      if (enable) {
        callback()
      }
    }
  }

  $("#subStartBtn").off("click").on("click", () => {
    if (recordStatus === 'setting') {
      $("#onlineScreenBox").css('display', 'flex');
      $("#oldMainBox").hide();
      startRecord().then();
    }
  })
  $("#start-record").off("click").on('click', async () => {
    trackUpload({
      event: "button_click",
      button_name: "start_record_btn"
    })
    if (isLoginRef.value === true) {
      await updateUserinfo()
    } else {
      await doLogin()
    }
    const enable = await checkLimit()
    if (enable === false) {
      return
    }

    console.log(isVipRef.value)
    if (isVipRef.value === true) {
      startRecord().then()
    } else {
      openNoVipLimitDialog()
    }
  })

  function openNoVipLimitDialog() {
    $('#noVip1MinDialogId').css('display', 'flex')

    $('#closeNoVip1MinDialog').off('click').on('click', () => {
      $('#noVip1MinDialogId').css('display', 'none')
    })

    $('#noVipStartRecordBtnId').off('click').on('click', () => {
      startRecord().then()
      $('#noVip1MinDialogId').css('display', 'none')
    })

    $('#noVipOpenVipPageId').off('click').on('click', () => {
      open(getVipPageUrl('no-vip-1-min-limit'), '_blank')
      $('#noVip1MinDialogId').css('display', 'none')
    })
  }

  //开始录制
  async function startRecord() {
    console.log('开始录制')
    userMediaOptions = {video: useCamera ? {width: width, height: height} : false, audio: useMic};
    displayMediaOptions = {
      audio: useAudio,
      video: {
        displaySurface: "monitor",
      }
    };
    if (useMic) {
      const ok = await initUserMedia();
      if (!ok) return;
      await initDisplayMedia(displayMediaOptions);
    } else {
      await initDisplayMedia(displayMediaOptions);
    }
  }

  async function initDisplayMedia(options) {
    try {
      if (isEdge || isChrome) {
        // show step tips
        const showedScreenGuide = Boolean(localStorage.getItem('showedScreenGuide'));
        if (!showedScreenGuide) {
          $(".screen-recorder-container .step-guides").fadeIn(400);
          localStorage.setItem('showedScreenGuide', 'true');
        }
      }
      gdmStream = await navigator.mediaDevices.getDisplayMedia(options);
      readyCountDown(gdmStream);
      // hide guide after share or cancel or some error
      hideStepGuides();
      gdmStream.getVideoTracks()[0].addEventListener('ended', () => {
        if (isReadyCounting) {
          $("#countdownBox").css('display', 'none');
          cancelPreviewCamera();
          clearInterval(countTimer);
          backToSetting();
          const countNum = document.getElementById('countdownNum');
          countNum.innerText = 3;
          isReadyCounting = false;
          recordedSeconds = 0;
        }
      })
    } catch (e) {
      // hide guide after share or cancel or some error
      hideStepGuides();
      console.log(e)
      console.log(e.name)
      console.log(e.message)
      if (e.name == 'NotFoundError' || e.message == 'The object can not be found here.' || e.name == 'TypeError') {
        $("#getAppTip").css('display', 'flex');
        $("#getAppDefaultContent").css('display', 'none');
        $("#noDisplayMedia").show();
        $("#macAudioNotSupport").css('display', 'none');
      } else {
        $(".light-top-tip").addClass('active');
        let tipTimer = setTimeout(() => {
          $(".light-top-tip").removeClass('active');
          clearTimeout(tipTimer);
        }, 2000)
      }
      cancelPreviewCamera();
      console.log('授权失败！')
    }
  }

  async function initUserMedia() {
    try {
      const container = document.querySelector("#device-list");
      const constraints = useMic ? {
        audio: {
          deviceId: container.dataset.deviceId
        }
      } : false;
      gumStream = await navigator.mediaDevices.getUserMedia(constraints);
      return true;
    } catch (e) {
      showNotAllowedTip();
      useMic = false;
      $("#trigger-microphone").removeClass('active');
      return false;
    }
  }

  function show1MinTip() {
    $(".one-min-tip").addClass('active');
    const tipTimer = setTimeout(() => {
      $(".one-min-tip").removeClass('active');
      clearTimeout(tipTimer);
    }, 10000);
  }

  function readyCountDown(stream) {
    if (isVipRef.value === true) {
      showLongTimeTip();
    } else {
      show1MinTip()
    }

    isReadyCounting = true;
    videoName = null;
    recordStatus = 'recording';
    recordingData = [];
    triggerMediaChecked();
    changeDisplayBlock();
    $("#countdownBox").css('display', 'flex');
    const countNum = document.getElementById('countdownNum');
    let start = 3;
    countTimer = setInterval(() => {
      if (start > 1) {
        start -= 1;
        countNum.innerText = start;
      } else if (start == 1) {
        start -= 1;
        $("#countdownBox").css('display', 'none');
      } else {
        clearInterval(countTimer);
        startMediaRecord(stream);
        countNum.innerText = 3;
        isReadyCounting = false;
      }
    }, 1000)
  }

  function enableLoading() {
    $('.progress-box .uploading-progress-box').css('display', 'none')
    $('.progress-box .loading').css('display', 'block')
  }

  function enableUploading() {
    $('.progress-box .loading').css('display', 'none')
    $('.progress-box .uploading-progress-box').css('display', 'block')
  }

  async function startMediaRecord(gdmStream) {
    recorderStream = gumStream ? mixStream(gumStream, gdmStream) : gdmStream;
    const options = isEdge || isChrome ? {mimeType: 'video/webm', codecs: 'H264'} : {mimeType: 'video/webm'};
    recorder = new MediaRecorder(recorderStream, options);

    recorder.start();
    recorder.ondataavailable = function (e) {
      if (e.data && e.data.size > 0) {
        recordingData.push(e.data);
      }
    };
    recorder.onstart = () => {
      $("#start-record").attr("disabled", true);
      $("#stop-record").removeAttr("disabled");
      $("#pause-record").removeAttr("disabled");
      startCountdown();
    };
    recorder.onstop = () => {
      if (document.pictureInPictureElement) document.exitPictureInPicture();
      stopCountdown();
      if (recordStatus == 'recording' || recordStatus == 'pause') {
        //记录完成录制的次数 key:plugin-rec-record-num
        let recordNum = localStorage.getItem("plugin-rec-record-num") || 0;
        localStorage.setItem("plugin-rec-record-num", (++recordNum).toString());

        recordStatus = 'preview';
        $("#onlineScreenBox").addClass('preview-main-box');
        // $("#recordOnlineTitle").hide();
        videoName = getVideoFilename() + '.webm';
        const blob = new Blob(recordingData, {type: 'video/webm'});

        if (isLoginRef.value === true) {
          //登录了直接上传
          $("#uploadingDialog").css('display', 'flex');
          enableLoading()
        } else {
          //未登录显示预览页
          disableCountingView()
          enablePreviewView()
        }

        logStep('停止录制,recorder.onstop ')
        finalDuration = recordedSeconds
        ysFixWebmDuration(blob, recordedSeconds).then(fixedBlob => {
          logStep('补时长完成,ysFixWebmDuration')
          enableUploading();
          fixedVideoBlob = fixedBlob;
          initPreviewVideo();
          if (isLoginRef.value === true) {
            startUpload()
          }
        }).catch(reason => {
          console.log('解析异常 ysFixWebmDuration', reason)
        })
         // $("#formatShadow").css('display', 'flex');
        // changeVideoType();
      }
      // $("#pause-record").removeClass("record-resume");
      // $("#pause-record").addClass("record-pause");
      // $(".pause-resume-word.resume").hide();
      // $(".pause-resume-word.pause").show();
      $("#saveAsMp4").hide();
      $("#downloadRecordVideo").show();
      $("#goBackDialog").css('display', 'none');
      recorder = null;
      cancelPreviewCamera();
      recordedSeconds = 0
      // changeDisplayBlock();
    };
    recorder.onpause = () => {
      stopCountdown();
    }
    recorder.onresume = () => {
      startCountdown();
    }
    recorderStream.addEventListener('inactive', () => {
      // console.log('Capture stream inactive');
    });
    recorderStream.getVideoTracks()[0].addEventListener('ended', () => {
      stopMediaRecord();
    })
  }

  function enablePreviewView() {
    $("#recordOnlineTitle").css('display', 'none')
    $('#recordPreview').css('display', 'block')
  }

  function _disableThird() {
  }


  function disablePreviewView() {
    $('#recordPreview').css('display', 'none')
  }

  function enableCountingView() {
    $('#recordCounting').css('display', 'block')
  }

  function disableCountingView() {
    $('#recordCounting').css('display', 'none')
  }

  //结束录制开始上传
  async function startUpload() {
    logStep('开始上传,startUpload')
    if (!fileUploading) {
      if (!isLoginRef.value) {
        await doLogin()
        getUserInfo().catch(reason => {
          logStep('获取用户信息失败,getUserInfo', reason)
          console.log(reason)
        });
      } else {
        logStep('开始获取用户信息,startUpload')
        getUserInfo().catch(reason => {
          logStep('获取用户信息失败,getUserInfo', reason)
          console.log(reason)
        });
      }
    }
  }

  function mixStream(gumStream, gdmStream) {
    let tracks = [];
    if (useMic && !useAudio) {
      if (gumStream.getAudioTracks().length > 0) tracks = tracks.concat(gumStream.getAudioTracks());
    } else if (useAudio && !useMic) {
      if (gdmStream.getAudioTracks().length > 0) tracks = tracks.concat(gdmStream.getAudioTracks());
    } else if (useAudio && useMic) {
      const ctx = new AudioContext();
      const dest = ctx.createMediaStreamDestination();
      if (gumStream.getAudioTracks().length > 0) ctx.createMediaStreamSource(gumStream).connect(dest);
      if (gdmStream.getAudioTracks().length > 0) ctx.createMediaStreamSource(gdmStream).connect(dest);
      tracks = tracks.concat(dest.stream.getTracks());
    }
    tracks = tracks.concat(gumStream.getVideoTracks()).concat(gdmStream.getVideoTracks());
    return new MediaStream(tracks)
  }

  //停止录制
  function stopMediaRecord() {
    $("#start-record").removeAttr("disabled");
    // $("#stop-record").attr("disabled", true);
    // $("#pause-record").attr("disabled", true);
    if (recorderStream) recorderStream.getTracks().forEach(track => track.stop());
    if (gumStream) gumStream.getTracks().forEach(track => track.stop());
    if (gdmStream) gdmStream.getTracks().forEach(track => track.stop());
    if (cameraStream) cameraStream.getTracks().forEach(track => track.stop());
    $("#preview-video").attr('controls', true);
  }

  $("#stop-record").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "stop_record_btn"
    })
    if (recorder && recorder.state !== "inactive") {
      stopMediaRecord()
    }
  });

  function initPreviewVideo() {
    let src = window.URL.createObjectURL(fixedVideoBlob);
    previewVideo.src = src;
  }

  function changeDisplayBlock() {
    switch (recordStatus) {
      case 'setting':
        $("#recordSetting").css('display', 'block');
        $('#recordCounting').css('display', 'none');
        $('#recordPreview').css('display', 'none');
        $('#closeOnlineRecorder').show()
        break;
      case 'recording':
        $("#recordSetting").css('display', 'none');
        $('#recordCounting').css('display', 'block');
        $('#recordPreview').css('display', 'none');
        $('#closeOnlineRecorder').hide()
        break;
      case 'pause':
        $("#recordSetting").css('display', 'none');
        $('#recordCounting').css('display', 'block');
        $('#recordPreview').css('display', 'none');
        break;
      case 'preview':
        $("#recordSetting").css('display', 'none');
        $('#recordCounting').css('display', 'none');
        $('#recordPreview').css('display', 'block');
        break;
      default:
        $("#recordSetting").css('display', 'block');
        $('#recordCounting').css('display', 'none');
        $('#recordPreview').css('display', 'none');
        break;
    }
  }

  function formatMilliseconds(ms) {
    if (ms < 100) {
      ms = 0
    }
    // 1- Convert to seconds:
    var seconds = ms / 1000;

    // 2- Extract hours:
    var hours = parseInt(seconds / 3600); // 3600 seconds in 1 hour
    seconds = parseInt(seconds % 3600); // extract the remaining seconds after extracting hours

    // 3- Extract minutes:
    var minutes = parseInt(seconds / 60); // 60 seconds in 1 minute

    // 4- Keep only seconds not extracted to minutes:
    seconds = Math.ceil(seconds % 60);

    // 5 - Format so it shows a leading zero if needed
    let hoursStr = ("00" + hours).slice(-2);
    let minutesStr = ("00" + minutes).slice(-2);
    let secondsStr = ("00" + seconds).slice(-2);

    return hoursStr + ":" + minutesStr + ":" + secondsStr
  }

  let recordingCountdown;
  const spanRecordingDuration = document.getElementById('recordedTime');

  function startCountdown() {
    const startedAt = (new Date()).getTime();
    let fnCountdown = function () {
      let current = (new Date()).getTime();
      recordedCountTime = (current - startedAt);
      let diff = formatMilliseconds(recordedSeconds + recordedCountTime);
      spanRecordingDuration.innerText = diff;
      if (isVipRef.value === false && recordedSeconds + recordedCountTime >= 60000) {
        //非vip用户录制超过1分钟就直接停止
        stopMediaRecord();
      }
    };
    recordingCountdown = self.setInterval(fnCountdown, 1000);
    fnCountdown();
  }

  function stopCountdown() {
    // 暂停时记录已经录制的时间加上新录制的时长
    recordedSeconds += recordedCountTime;
    clearInterval(recordingCountdown);
    recordedCountTime = 0;
  }

  //点击下载事件
  function handleDownload() {
    const url = window.URL.createObjectURL(fixedVideoBlob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = formatedVideoUrl || url;
    a.download = videoName;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      if ($("#saveAsMp4").length > 0) {
        $("#downloadRecordVideo").hide();
        $("#saveAsMp4").show();
      }
    }, 100);
  }

  $("#downloadRecordVideo").off("click").on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "upload_reccloud_btn"
    })
    _handleCheckStartRecording(() => {
      if (recordStatus === 'preview') {
        $("#uploadingDialog").css('display', 'flex');
        enableUploading();
        startUpload()
      }
    }, true).then()
  });

  function getVideoFilename() {
    let padTo2Digits = function (num) {
      return num.toString().padStart(2, '0');
    }

    let date = new Date();
    let time = [
      date.getFullYear(),
      padTo2Digits(date.getMonth() + 1),
      padTo2Digits(date.getDate()),
      padTo2Digits(date.getHours()),
      padTo2Digits(date.getMinutes()),
      padTo2Digits(date.getSeconds()),
    ].join('-');
    return "recording-" + time
  }

  $("#uploadRecordVideo").off("click").on("click", startUpload);

  async function getUserInfo() {
    try {
      fileUploading = true;
      const userId = userIdRef.value
      const apiToken = recToken();
      const {status} = await http.get(`/users/${userId}`)
      const ok = status === 200
      logStep('获取用户信息请求  $.req.get,结果: ', ok)
      if (ok) {
        logStep('获取用户信息请求成功')
        authorizationFile().catch(reason => {
          showUploadFailDialog()
          console.log("authorizationFile", reason)
        });
      } else {
        throw new Error(`status: ${ok}`)
      }
    } catch (e) {
      logStep('获取用户信息catch', e)
      showUploadFailDialog()
      console.log('getUserInfo', e)
    }
  }

  async function authorizationFile() {
    logStep('开始上传,uploadFileToOss')
    uploadingStatus = 'uploading';
    if (recordingData.length == 0) return

    try {
      const file = new File([fixedVideoBlob], videoName, {type: 'video/mp4'});

      const value = await uploadFile(file, uploadProgress)
      if (value && value.uniqid) {
        const onlyName = value.filename.substring(0, value.filename.toString().lastIndexOf('.'))
        await putVideoInfo(value.uniqid, onlyName, {
          app_id: 'Chrome reccloud Online Recorder'
        })
        await trackUpload({
          event: 'upload_file',
          source_from: getPageName(),
          filesize: (file.size / 1024 / 1024).toFixed(2),
          format: 'webm',
          time: await getDuration(file),
        })
        logStep('client.multipartUpload,上传成功')
        pointUploadSuccess(file).then()
        openVideoPage(value.uniqid).then()
        logStep('client.multipartUpload,埋点上报完成')
      } else {
        throw new Error('client.multipartUpload')
      }
    } catch (reason) {
      showUploadFailDialog()
      console.log('multipartUpload', reason)
    }
  }

  // reccloud的视频需要调一次编辑接口，才会在网站上显示出来
  async function openVideoPage(uniqid) {
    const href = `${getRegionPath()}/u/${uniqid}?api_token=${recToken()}`
    $("#uploadingDialog").css('display', 'none')
    const progressNum = document.getElementById('progressNum')
    $("#progressCircle").css('stroke-dashoffset', 372)
    progressNum.innerText = 0
    fileUploading = false;
    window.open(href, '_self')
  }

  //上传成功埋点
  function pointUploadSuccess(file) {
    const data = {
      upload_status: 'success',
      file_size: file.size / 1024 / 1024,
      file_time: finalDuration / 1000,
      file_format: '.webm',
      source_from: "reccloud_online_recorder",
      page_name: ''
    }
    return trackUpload(data)
  }

  function showUploadFailDialog() {
    fileUploading = false;
    uploadProgress(0)
    $("#uploadingDialog").css('display', 'none');
    $("#uploadFailBox").css('display', 'flex');
  }

  $('#uploadFailCancelBtn').off('click').on('click', (e) => {
    backToSetting();
    $("#uploadFailBox").css('display', 'none');
  })
  $('#uploadFailRetryBtn').off('click').on('click', (e) => {
    startUpload()
    $("#uploadingDialog").css('display', 'flex');
    $("#uploadFailBox").css('display', 'none');
  })

  function uploadProgress(progress) {
    let pro = Math.ceil(progress * 100);
    const base = 372;
    let pronum = base - (base / 100) * pro;
    $("#progressCircle").css('stroke-dashoffset', pronum);
    const progressNum = document.getElementById('progressNum');
    progressNum.innerText = pro;
    if (progress == 1) {
      uploadingStatus = 'successful';
      bindFn = null;
    }
  }

  // $("#saveAsMp4").off("click").on("click", saveAsMp4);
  async function saveAsMp4() {
    await $.loadScript("https://dev-cdn-apowersoft-com.aoscdn.com/ffmpeg/ffmpeg-mp4-web.js");
    window.prompt = () => 'video.webm';
    var fileReader = new FileReader();
    var fileData;
    fileReader.onload = function () {
      const outName = videoName.replace(".webm", ".mp4");
      fileData = this.result;
      const testData = new Uint8Array(fileData);
      const result = ffmpeg({
        MEMFS: [{name: "video.webm", data: testData}], arguments: ["-i", "video.webm", "-c:v", "copy", outName],
      });
      // download mp4
      const out = result.MEMFS[0];
      const blob = new File([out.data], outName, {type: 'video/mp4'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = outName;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }, 100);
    };
    fileReader.readAsArrayBuffer(fixedVideoBlob);
  }

  function hideStepGuides() {
    $(".screen-recorder-container .step-guides").fadeOut(50);
  }

  let tipTimer;
  let tipShowTime;

  function showLongTimeTip() {
    // show long time tips
    const showedLongScreenRecordTip = localStorage.getItem('showedLongScreenRecordTip');
    if (!showedLongScreenRecordTip) {
      tipShowTime = new Date().getTime();
      $(".long-time-tip").addClass('active');
      addTipTimer();
    }
  }

  $(".long-time-tip").hover(function () {
    clearTimeout(tipTimer);
  }, function () {
    const timeNow = new Date().getTime();
    if (timeNow - tipShowTime >= 10000) {
      $(".long-time-tip").removeClass('active');
    } else {
      let leftTime = 10000 - (timeNow - tipShowTime);
      addTipTimer(leftTime);
    }
  })

  function addTipTimer(duration) {
    tipTimer = setTimeout(() => {
      $(".long-time-tip").removeClass('active');
      clearTimeout(tipTimer);
    }, duration || 10000);
  }

  $("#closeLongTimeTip").off("click").on("click", hideLongTimeTip)

  function hideLongTimeTip() {
    $(".long-time-tip").removeClass('active');
    localStorage.setItem('showedLongScreenRecordTip', 'true');
  }

  function hideLongTimeTipWithNoNext() {
    $(".long-time-tip").removeClass('active');
    localStorage.setItem('showedLongScreenRecordTip', 'true');
  }

  $("#goBackDialog").css('display', 'none');
  $(".pause-resume-word.resume").hide();
  $("#goBackTitle").off("click").on("click", () => {
    trackUpload({
      event: "button_click",
      button_name: "return_title_btn"
    })
    $("#goBackDialog").show();
    $("#goBackDialog").css('display', 'flex');
  });

  $('#recordNewTitle').off('click').on('click', () => {
    trackUpload({
      event: "button_click",
      button_name: "record_new_btn"
    })
    backToSetting()
  })

  $("#cancelBack").off("click").on("click", () => {
    trackUpload({
      event: "button_click",
      button_name: "cancel_back_btn"
    })
    $("#goBackDialog").css('display', 'none');
  })

  $("#confirmBack").off("click").on("click", () => {
    trackUpload({
      event: "button_click",
      button_name: "confirm_back_btn"
    })
    $("#goBackDialog").css('display', 'none');
    $("#start-record").removeAttr("disabled");
    // $("#stop-record").attr("disabled", true);
    // $("#pause-record").attr("disabled", true);
    stopCountdown();
    backToSetting();
  })

  function backToSetting() {
    recordStatus = 'setting';
    $("#onlineScreenBox").removeClass('preview-main-box');
    $("#recordOnlineTitle").show();
    const spanRecordingDuration = document.getElementById('recordedTime');
    spanRecordingDuration.innerText = '00:00:00';
    previewVideo.src = null;
    $("#saveAsMp4").hide();
    $("#downloadRecordVideo").show();
    recordedSeconds = 0;
    recordedCountTime = 0;
    triggerMediaChecked();
    changeDisplayBlock();
    stopMediaRecord();
    $("#pause-record").removeClass("record-resume");
    $("#pause-record").addClass("record-pause");
    $(".pause-resume-word.resume").hide();
    $(".pause-resume-word.pause").show();
    $("#recordOnlineTitle").css('display', 'block')
  }

  $("#closeCameraTip").off("click").on("click", () => {
    $("#noCameraTip").css('display', 'none');
  })
  $("#closeMicTip").off("click").on("click", () => {
    $("#noMicTip").css('display', 'none');
  })
  $("#closeOnlineRecorder").off("click").on("click", () => {
    enableMyVideoInHomePage()
    removeOverflowX()
    $('.reccloud-animate.banner').show()
    enableHeadBgcWithMyVideoPage()
    $("#onlineScreenBox").css('display', 'none');
    $("#oldMainBox").show();
    backToSetting();
    gumStream = null;
    gdmStream = null;
    recorder = null;
    cameraStream = null;
    recorderStream = null;
    recordingData = [];
    enableScrollInVideoPage()
  })

  function showNotAllowedTip() {
    $("#notAllowedTip").css("display", "flex");
  }

  $("#hideNotAllowedTip").off("click").on("click", () => {
    $("#notAllowedTip").css("display", "none");
  })
  $("#closeStorageLimitTip").off("click").on("click", () => {
    $("#storageLimitTip").css('display', 'none');
  })
  $("#closeVideoNumLimitTip").off("click").on("click", () => {
    $("#videoNumLimitTip").css('display', 'none');
  })
  $(".to-clean-storage").off("click").on("click", () => {
    toUserPage();
  })
  $(".get-more-storage").off("click").on("click", () => {
    toVipPage();
  })

  function toUserPage() {
    $("#storageLimitTip").css('display', 'none');
    $("#videoNumLimitTip").css('display', 'none');
    open(getVipPageUrl('no-vip-storage-limit'), '_blank')
  }

  function toVipPage() {
    $("#storageLimitTip").css('display', 'none');
    $("#videoNumLimitTip").css('display', 'none');
    open(getVipPageUrl('no-vip-storage-limit'), '_blank')
  }

  $("#pause-record").off("click").on("click", () => {
    trackUpload({
      event: "button_click",
      button_name: "pause_recover_btn"
    })
    if (recordedCountTime + recordedSeconds > 0) {
    } else {
      return
    }
    if (recordStatus == "recording") {
      $("#pause-record").addClass("record-resume");
      $("#pause-record").removeClass("record-pause");
      $(".pause-resume-word.pause").hide();
      $(".pause-resume-word.resume").show();
      recordStatus = 'pause';
      recorder?.pause();
    } else if (recordStatus == "pause") {
      $("#pause-record").removeClass("record-resume");
      $("#pause-record").addClass("record-pause");
      recordStatus = 'recording';
      recorder?.resume();
      $(".pause-resume-word.resume").hide();
      $(".pause-resume-word.pause").show();
    }
  })
}


export default _recordInit

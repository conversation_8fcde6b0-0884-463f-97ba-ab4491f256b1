import TrackUtil from '@/track/_TrackUtil';
import BaseTrack from '@/track/BaseTrack';

export default class AiTranslateTrack extends BaseTrack {

  // 按钮点击
  static clickStartNow() {
    return TrackUtil.trackButtonClick('click_start_now')
  }

  static uploadFile(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('upload_file', {
      ...params
    })
  }

  static uploadFileSuccess(params: UploadFileSuccessParams & SpeakerParams) {
    return TrackUtil.trackButtonClick('upload_file_success', {
      ...params
    })
  }

  static startTranslateTrans(params: TranslateParams & SceneParams) {
    return TrackUtil.trackButtonClick('start_translate_trans', {
      ...params
    })
  }

  static realStartTranslate(params: TranslateParams & SceneParams) {
    return TrackUtil.trackButtonClick('real_start_convert', {
      ...params
    })
  }

  static clickStyle(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_style', {
      ...params
    })
  }

  static clickEnableSearch(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_enable_search', {
      ...params
    })
  }

  static clickReplace(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_replace', {
      ...params
    })
  }

  static clickReplaceAll(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_replace_all', {
      ...params
    })
  }

  static clickEdit(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_edit', {
      ...params
    })
  }

  // 点击导出
  static clickGenerateVideo(params: Record<string, any>) {
    return TrackUtil.trackButtonClick('click_generate_video', {
      ...params
    })
  }

  //真正开始导出
  static realGenerateVideo(params: ExportParams) {
    return TrackUtil.trackButtonClick('real_generate_video', {
      ...params
    })
  }

  static clickGenerateAudio(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_generate_audio', {
      ...params
    })
  }

  static clickViewList(params: ClickViewListParams) {
    return TrackUtil.trackButtonClick('click_view_list', params)
  }

  static clickDelete() {
    return TrackUtil.trackButtonClick('delete', {
      func_name: 'ai_translate'
    })
  }

  static clickView() {
    return TrackUtil.trackButtonClick('view', {
      func_name: 'ai_translate'
    })
  }

  static clickPlay() {
    return TrackUtil.trackButtonClick('play', {
      func_name: 'ai_translate'
    })
  }

  static clickNewTask() {
    return TrackUtil.trackButtonClick('click_new_task', {
      func_name: 'ai_translate'
    })
  }

  static clickRetryTranslate(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_retry_translate', {
      ...params
    })
  }

  //点击收藏
  static clickLikeVoice(name: string) {
    return TrackUtil.trackButtonClick('click_like_voice', {
      trans_voice: name
    })
  }

  //点击取消收藏
  static clickUnlikeVoice(name: string) {
    return TrackUtil.trackButtonClick('click_unlike_voice', {
      trans_voice: name
    })
  }

  static clickTranslateSubtitle(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_translate_subtitle', {
      ...params
    })
  }

  static clickSceneSelect(params: SceneParams) {
    return TrackUtil.trackButtonClick('click_scene_select', {
      ...params
    })
  }

  static clickTimestampsEdit(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_timestamps_edit', {
      ...params
    })
  }

  static clickDeleteItem(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_delete_item', {
      ...params
    })
  }

  static clickInsertItem(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_insert_item', {
      ...params
    })
  }

  static clickMargeItem(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_marge_item', {
      ...params
    })
  }

  static clickSpeakerManagement(params: SpeakerParams) {
    return TrackUtil.trackButtonClick('click_speaker_management', {
      ...params
    })
  }

  static clickHistoryUpdate() {
    return TrackUtil.trackButtonClick('click_history_update')
  }

  static realHistoryUpdate() {
    return TrackUtil.trackButtonClick('real_history_update')
  }

  // 翻译转换结果
  static convertResult(params: ConvertResultType) {
    return TrackUtil.trackEditResult({
      func_name: 'ai-translate',
      ...params
    })
  }

  //上传本地文件
  static uploadLocalFile(params: UploadFileParams & SpeakerParams) {
    return TrackUtil.trackUploadFile({
      page_name: 'ai-translate',
      ...params
    })
  }

  //合成视频结果
  static generateVideoResult(params: ConvertResultType) {
    return TrackUtil.trackCustomEvent('generate_video_result', {
      page_name: 'ai-translate',
      ...params
    })
  }

}

//必须包含result属性, 其他的任意
type ConvertResultType = {
  result: 'success' | 'fail',
  reason?: string,
} & Record<string, any>

interface UploadFileSuccessParams {
  filesize: number,
  format: string,
  time: number,
  filename: string,
  upload_method: 'click' | 'drag' | 'cloud' | 'space' | 'play',
  video_id: string,
}

interface UploadFileParams {
  source_from: 'ai-translate',
  filesize: string,
  time: number,
  format: string,
}

interface TranslateParams {
  origin_lang: string,
  trans_lang: string,
  trans_voice: string,
  voice_volume: number,
  voice_speed: number,
  filesize: number,
  format: string,
  time: number,
  video_id: string,
  model: 'single' | 'multi' | 'zero' | 'clone'
}

interface ClickViewListParams {
  source: 'home_page' | 'func_page'
}

interface SpeakerParams {
  model: 'single' | 'multi' | 'zero' | 'clone',
}

interface SceneParams {
  scene_name: 'video' | 'subtitle' | 'audio_text' | 'clone'
}

export enum AudioLangType {
  origin = 0,
  trans = 1
}

export enum SubtitleParamsType {
  close = 0,
  origin = 1,
  trans = 2,
  multi = 3
}

interface ExportParams {
  format: 'mp4' | 'mp3' | 'srt'
  audio_lang?: AudioLangType,
  subtitle?: SubtitleParamsType
}

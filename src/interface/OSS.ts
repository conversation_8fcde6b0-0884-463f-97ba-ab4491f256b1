export interface OssAuthResult {
  accessKeyId: string;
  accessKeySecret: string;
  stsToken: string,
  region?: string;
  endpoint: string;
  bucket: string;
  callback: Record<string, string>;
  objects: Record<string, string>
}

export type ProgressType = (
  progress: number,
  fileInfo: {
    file: File,
    fileSize: number,
    uploadId: string,
    [key: string]: any,
  },
  resource: Record<string, any>
) => void

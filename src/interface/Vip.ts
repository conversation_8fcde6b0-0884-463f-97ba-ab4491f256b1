export interface ProductInfoResultModel {
  goods: {
    commercial: Array<ProductItem>,
    personal: Array<ProductItem>,
    extend_1: Array<ProductItem>,
  }
}

//前端ui模版类型枚举
export enum WebTemplateType {
  free,
  normal,
  extend,
}

export enum ExtendTabType {
  tab1 = 'point',
  tab2 = 'space',
}

export interface ProductItem {
  webTemplateType: WebTemplateType,//用于判断展示怎样的ui
  readonly display_name?: string,
  readonly sort: number,
  readonly goods_id: string,
  readonly price_detail_desc: string,
  gift_plan_desc: string,
  product_desc: string,
  readonly short_name: string,
  readonly avg_price: string,

  readonly origin_price_text?: string,
  readonly price?: string,
  readonly price_text?: string,
  readonly gift_desc?: string,//应该不用了 待删除
  readonly currency_unit?: string,
  readonly selected?: number,//1就是默认选中
  readonly period_type: string | 'free' | 'extend',//用于埋点上报区分类型,vip->yearly/monthly 点数->lifetime free用于显示注册按钮
  readonly vip_quota?: number,//用于埋点上报区分类型  点数用这个字段区分数量
  readonly vip_quantity?: number,//用于埋点上报区分类型 vip用这个字段区vip分时长
  readonly recommend?: number,//是否推荐 1推荐
  readonly is_trial?: number,//是否试用 1试用
}

export interface TransactionResultModel {
  readonly display_name?: string,
  readonly goods_id?: string,
  readonly invoice_amount?: string,
  readonly invoice_amount_text?: string,
  readonly license_type?: string,
  readonly origin_price?: string,
  readonly origin_price_text?: string,
  readonly period_type?: string,
  readonly product_id?: number,
  readonly quantity?: number,
  readonly status?: number,
  readonly transaction_id?: string,
}


export enum PriceTabType {
  tab1 = 'tab1',
  tab2 = 'tab2',
}

export  type TabKey = 'vip' | 'point' | 'storage'

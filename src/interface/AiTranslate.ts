import {BaseTaskQueryType, SrtResultModel, VideoItemInfo} from '@/interface/OnlineEdit';
import {StylePosition} from '@/interface/AiSubtitle';
import {IsFavoriteType} from '@/interface/TextToSpeech';

export enum PreviewStatus {
  startSelectFile = 'startSelectFile',//选择文件
  detail = 'detail',//详情编辑页
  historyList = 'historyList',//历史记录列表
}

export enum SceneStatus {
  sceneWithVideo,
  sceneWithSubtitle,
  sceneWithCloneVoice,
  sceneWithText
}

export interface AiTranslateCheckFileItem {
  filename: string,
  ext: string,//后缀不带点
  size: number,//单位M
  from: 'click' | 'drag' | 'cloud' | 'space' | 'play',
  duration: number,//单位秒
  self: File | VideoItemInfo,
  existAudioTrack: boolean,
}

//添加文件错误信息
export enum AddFileErrorMsg {
  //时长太长
  durationLimit = 'durationLimit',
  //文件太大
  sizeLimit = 'sizeLimit',
  //格式不支持
  formatLimit = 'formatLimit',
  //音频轨道为空
  emptyAudioTrack = 'emptyAudioTrack',
  //网络异常
  networkError = 'networkError',
}

export interface LangItemType {
  title: string,
  lang: string
}

export interface VoiceItemType {
  icon: string,
  url: string,
  voice: string,
  voice_tag: string,
  id: number,
  favorite: IsFavoriteType
}

export interface StartFileItemType {
  filesize: number,
  format: string,
  time: number,
  uniqid: string,
  from: 'click' | 'drag' | 'cloud' | 'space' | 'play',
  videoUrl: string,
  filename: string,
  cover: string,
}

export interface SubtitleStyleModel {
  alignment: StylePosition,
  backcolor: string,
  font: number,
  font_bold: boolean,
  font_color: string,
  font_italic: boolean,
  font_size: number,
  font_underline: boolean,
  letter_spacing: number,
  vertical_position: number,
  stroke_color: string,
  stroke_width: number,
  enable_bg: boolean,
  enable_stroke: boolean,
  preset_style?: number
}


export enum Speaker {
  disable = 0,//不区分 视频翻译 单音色
  enable = 1,//区分 视频翻译    多音色
  zero = 2, // 字幕翻译        无音色
  clone = 3,// 克隆音色
}

export interface DetailSubtitleItemType extends BaseTaskQueryType {
  task_id: string,
  mp_task_id: string,
  origin_lang: string,
  origin_subtitles: Array<SubtitleItemModel>,
  translation_lang: string,
  translation_subtitles: Array<SubtitleItemModel>,
  subtitle_styles: SubtitleStyleModel,
  voice: string,
  speech_rate: number,
  volume: number,
  sample_audio_url: string,
  vod_url: string,
  duration: number,
  uniqid: string,
  translation_task_type: number,//是否是试用任务 >0 是试用
  height: number,//原视频高度
  speaker_identification: Speaker, //是否区分说话人
  speakers?: Array<SpeakerItemType>,
  subtitle_type?: SwitchSubtitleType,
  audio_type?: SwitchAudioType,
  bgm_url?: string,
  format?: string,
  title?: string,
  size?: number,
  subtitle_changed?: number,
}

export interface SubtitleItemModel {
  start: string,
  end: string,
  text: string,

  // 下面为剩余参数
  char_per_second?: number,
  rate?: number,
  speaker?: string,
  voice?: string,
  volume?: number,
}

export type RestParams = Omit<SubtitleItemModel, keyof SrtResultModel>


export interface SrtModel extends SrtResultModel {
  translateText: string,
  originRestParams?: RestParams,
  translateRestParams?: RestParams,
}

export interface AiTranslateTaskItemType {
  task_id: string,
  title: string,
  duration: number,
  progress: number,
  cover_url: string,
  state: number,
  step: number,
  uniqid: string,
  version: number,
  speaker_identification: Speaker,
}

export enum ReplaceType {
  origin = 'origin',
  translate = 'translate',
}


export interface AiTranslateBaseEquity {
  limit: number,
  used: number,
}

export interface AiTranslateEquity extends AiTranslateBaseEquity {
  price: number,
  limit_duration: number,
}


export interface EquityModel {
  merge_equity: AiTranslateEquity,
  copy_writing_translation_equity: AiTranslateBaseEquity
  copy_writing_speech_equity: AiTranslateBaseEquity & { free_sample_audio_quantity: number }
  translation_equity: AiTranslateEquity
  merge_audio_equity: AiTranslateBaseEquity & { price: number }
}

export enum RetryStatus {
  hidden = 'hidden',
  allowRetry = 'allowRetry',
  loading = 'loading',
  failed = 'failed',
  success = 'success',
  btnOnly = 'btnOnly',
}


export interface SpeakerItemType {
  speaker: string,
  title: string,
  voice: string,
  volume: number,
}

export enum ExportType {
  video = 'video',
  audio = 'audio',
  subtitle = 'subtitle'
}

export enum ExportVideoLangType {
  translate = 'translate',
  origin = 'origin',
}

export enum ExportSubtitleLangType {
  translate = 'translate',
  origin = 'origin',
  multi = 'multi'
}

export enum ExportCreateType {
  videoWithTransAudio = 0,
  audioWithTrans = 1,
  videoWithOriginAudio = 2
}

export enum ExportCreateSubtitleType {
  closeSubtitle = 0,
  originSubtitle = 1,
  transSubtitle = 2,
  multiSubtitle = 3
}

export enum SwitchAudioType {
  translate = 0,
  origin = 1,
}

export enum SwitchSubtitleType {
  none = 0,
  origin = 1,
  translate = 2,
  multi = 3,
}

export enum VersionType {
  old = 0,
  new = 1
}


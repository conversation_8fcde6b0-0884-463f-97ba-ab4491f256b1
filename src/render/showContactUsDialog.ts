/**
 * @unocss-include
 */
import {getLang} from '@central/i18n'
// @ts-ignore
import {popupContact} from '@central/contact-us/lib'
import {proId} from '@/services/constants'
//henry二维码
import qrcodeIcon from '@/assets/images/api/qrcode_v1.png'
//zeo二维码
import qrcodeIconV2 from '@/assets/images/api/qrcode_v2.png'

type ContactType = {
  imgUrl?: string,
  tel?: string,
}

export default function showContactUsDialog(params?: ContactType) {
  const tel1 = '15170247409'
  const tel2 = '13979131694'
  params = {
    tel: tel1,
    imgUrl: qrcodeIcon,
    ...params,
  }

  const lang = getLang()
  if (lang === 'zh') {
    _showChinaQrCode(params)
  } else {
    _showOverseasContact()
  }
}

function _showChinaQrCode(params: ContactType) {
  const div = document.createElement('div')
  div.innerHTML = `
  <div class="fixed w-full h-full top-0 left-0 z-[99999] ">
    <div class="h-full bg-[rgba(0,0,0,.5)]"></div>
    <div class="min-w-[332px] absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center bg-white pb-[48px] rounded-[20px]">
        <div class="self-end pr-[12px] pt-[14px] cursor-pointer text-[#999] hover:text-red-500" id="close-dialog">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_395_133" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                  height="16">
              <rect width="16" height="16" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_395_133)">
              <rect x="1.77734" y="2.90881" width="1.6" height="16" rx="0.8" transform="rotate(-45 1.77734 2.90881)"
                    fill="currentColor"/>
              <rect x="1.77734" y="13.0911" width="16" height="1.6" rx="0.8" transform="rotate(-45 1.77734 13.0911)"
                    fill="currentColor"/>
            </g>
          </svg>
        </div>
        <div class="mt-[16px] mb-[18px] px-[38px]">添加客户经理微信，为您一对一解答</div>
        <div class="w-[140px] h-[140px] border-1px border-[#EE] p-[10px]">
          <img src="${params.imgUrl}" alt="">
        </div>

        <div class="mt-[12px]">电话咨询：${params.tel}</div>
      </div>
  </div>
  `
  document.body.appendChild(div)
  const closeBtn = document.querySelector('#close-dialog') as HTMLElement
  closeBtn.onclick = () => {
    div.remove()
  }
}

function _showOverseasContact() {
  popupContact({
    enFormSet: {
      tab: 'individual',
      pro_id: proId,
    },
    publicOptions: {
      buttonClass: 'reccloud-primary-btn',
      dialogClass: 'contact-us-dialog',
    }
  })
}

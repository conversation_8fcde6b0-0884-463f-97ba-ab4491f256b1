const recAudioFormatList: string[] = ['mp3', 'm4a', 'flac', 'wav']


class AudioUtil {
  private constructor() {
  }

  /**
   * 判断是否是录咖支持的音频后缀
   * @param ext 不需要以点开始 [.mp4->X] [mp4 √]
   */
  public static isAudioExt(ext: string) {
    if (ext.startsWith('.')) {
      ext = ext.replace('.', '')
    }
    return recAudioFormatList.includes(ext)
  }

  /** 判断远程连接是否是录咖支持音频 */
  public static isAudioUrl(url: string) {
    if (Boolean(url) === false) {
      return false
    }
    const ext = this.getHTTPUrlExt(url)
    return this.isAudioExt(ext)
  }

  private static getHTTPUrlExt(url: string) {
    if (url.startsWith('http')) {
      const httpURL = new URL(url)
      const lastDotIndex = httpURL.pathname.lastIndexOf('.')
      return httpURL.pathname.substring(lastDotIndex + 1)
    } else {
      throw new Error(`url不合法,非HTTP URL ${url}`)
    }
  }


  /**
   * @desc 检查是否有音频轨道
   * 有点耗时
   * @param src
   */
  public static checkHasAudioTrack(src: string | Blob): Promise<boolean> {

    function _getHasAudio(videoEl: HTMLVideoElement) {
      //@ts-ignore
      const hasAudio = videoEl.mozHasAudio || Boolean(videoEl.webkitAudioDecodedByteCount) || Boolean(videoEl.audioTracks && videoEl.audioTracks.length);
      return hasAudio
    }

    const videoEl = document.createElement('video')
    videoEl.muted = true
    videoEl.crossOrigin = 'anonymous'
    videoEl.preload = 'metadata'
    // Add playsinline attribute for iOS Safari
    videoEl.setAttribute('playsinline', '')
    videoEl.setAttribute('webkit-playsinline', '')

    return new Promise((resolve, reject) => {

      // 清理函数
      const cleanup = () => {
        videoEl.onerror = null
        videoEl.oncanplay = null
        if (typeof src !== 'string' && videoEl.src) {
          URL.revokeObjectURL(videoEl.src)
        }
      }

      videoEl.onerror = (ev) => {
        resolve(false)
        //@ts-ignore
        console.error(ev.target.error)
        cleanup()
      }
      
      function handleDuration() {
        videoEl.currentTime = 0.99
      }

      videoEl.onloadedmetadata = handleDuration;
      videoEl.onloadeddata = handleDuration;
      videoEl.oncanplay = handleDuration;


      videoEl.onseeked = () => {
        resolve(_getHasAudio(videoEl))
        cleanup()
      }

      videoEl.src = typeof src === 'string' ? src : URL.createObjectURL(src)
      videoEl.load();
    })
  }

  public static getMediaDuration(src: string | Blob): Promise<number> {
    const videoEl = document.createElement('video')
    videoEl.muted = true
    videoEl.crossOrigin = 'anonymous'
    videoEl.preload = 'metadata'
    // Add playsinline attribute for iOS Safari
    videoEl.setAttribute('playsinline', '')
    videoEl.setAttribute('webkit-playsinline', '')

    return new Promise((resolve) => {
      // 设置超时，防止长时间无响应
      const timeout = setTimeout(() => {
        cleanup()
        resolve(-1)
      }, 5000)

      // 清理函数
      const cleanup = () => {
        clearTimeout(timeout)
        videoEl.onerror = null
        videoEl.onloadedmetadata = null
        videoEl.onloadeddata = null
        videoEl.oncanplay = null
        if (typeof src !== 'string' && videoEl.src) {
          URL.revokeObjectURL(videoEl.src)
        }
      }

      videoEl.onerror = () => {
        cleanup()
        resolve(-1)
      }

      // 处理成功获取时长的函数
      const handleDuration = () => {
        if (isNaN(videoEl.duration) || !isFinite(videoEl.duration)) {
          cleanup()
          resolve(-1)
        } else {
          const duration = videoEl.duration
          cleanup()
          resolve(duration)
        }
      }

      // 使用多个事件来确保在各种浏览器中都能获取到时长
      videoEl.onloadedmetadata = handleDuration;
      videoEl.onloadeddata = handleDuration;
      videoEl.oncanplay = handleDuration;

      try {
        videoEl.src = typeof src === 'string' ? src : URL.createObjectURL(src)
        // 显式调用load方法，在某些移动浏览器中可能需要
        videoEl.load();
      } catch (error) {
        console.error(error)
        cleanup()
        resolve(-1)
      }
    })
  }

}

export default AudioUtil

/**
 * 该文件用于设置常量
 */
import {isDev} from '@/utils/util'
import {getLang} from '@central/i18n'

//产品id
export const proId = 289

export const apiPricingProId = 553

///点数不足统一状态码
export const notEnoughPointCode = 19105
/**
 * 录咖相关url
 */
const devZhBaseUrl = 'https://devaw.aoscdn.com/app/reccloud/v2'
const devEnBaseUrl = 'https://devgw.aoscdn.com/app/reccloud/v2'
const proZhBaseUrl = 'https://aw.aoscdn.com/app/reccloud/v2'
const proEnBaseUrl = 'https://gw.aoscdn.com/app/reccloud/v2'

const apDevZhBaseURL = 'https://devaw.aoscdn.com/tech'
const apDevEnBaseURL = 'https://devgw.aoscdn.com/tech'
const apZhBaseURL = 'https://aw.aoscdn.com/tech'
const apEnBaseURL = 'https://gw.aoscdn.com/tech'


const payDevZhBaseURL = 'https://devaw.aoscdn.com/base/payment/v2'
const payDevEnBaseURL = 'https://devgw.aoscdn.com/base/payment/v2'
const payZhBaseURL = 'https://aw.aoscdn.com/base/payment/v2'
const payEnBaseURL = 'https://gw.aoscdn.com/base/payment/v2'

//提交工单url
const supportDevZhBaseUrl = 'https://devaw.aoscdn.com/base/support/v2'
const supportDevEnBaseUrl = 'https://devgw.aoscdn.com/base/support/v2'
const supportZhURL = 'https://aw.aoscdn.com/base/support/v2'
const supportEnURL = 'https://gw.aoscdn.com/base/support/v2'

//用户中心登录url
const passportZhBaseURL = 'https://aw.aoscdn.com/base/passport/v2'
const passportEnBaseURL = 'https://gw.aoscdn.com/base/passport/v2'
const passportDevZhBaseURL = 'https://devaw.aoscdn.com/base/passport/v2'
const passportDevEnBaseURL = 'https://devgw.aoscdn.com/base/passport/v2'

//中台vip会员
const centerVipZhBaseURL = 'https://aw.aoscdn.com/base/vip/v2'
const centerVipEnBaseURL = 'https://gw.aoscdn.com/base/vip/v2'
const centerVipDevZhBaseURL = 'https://devaw.aoscdn.com/base/vip/v2'
const centerVipDevEnBaseURL = 'https://devgw.aoscdn.com/base/vip/v2'

/** ApRec下载链接*/
export const downloadApRecUrl = 'https://download.apowersoft.com/down.php?softid=apowerrec-saas-reccloud'

/** 录咖轻编辑下载url */
export const downloadBeecutUrl = 'https://download.aoscdn.com/down.php?softid=lighteditor'

/** 录咖win端下载url */
export const downloadWindowsUrl = 'https://download.aoscdn.com/down.php?softid=reccloud'
/** RecCloud下载url */
export const downloadRecCloudUrl = 'https://download.apowersoft.com/down.php?softid=reccloud-saas-reccloud'
/**
 * ApowerRec客户端协议链接
 */
export const apowerRecClientDeepLink = 'ApowerRec://'

/**
 * 多屏录咖客户端协议链接
 */
export const recCloudClientDeepLink = 'MultiRecCloud://'

/**
 * 移动端下载链接
 */
const mobileClientDownloadUrl = 'https://download.aoscdn.com/down.php?softid=reccloud'

export function baseUrl(): string {
  if (isDev()) {
    return getLang() === 'zh' ? devZhBaseUrl : devEnBaseUrl
  }

  if (getLang() === 'zh') {
    return proZhBaseUrl
  }
  return proEnBaseUrl

}

export function apBaseUrl(): string {
  if (isDev()) {
    return getLang() === 'zh' ? apDevZhBaseURL : apDevZhBaseURL
  }

  if (getLang() === 'zh') {
    return apZhBaseURL
  }
  return apEnBaseURL

}

export function payBaseUrl(): string {
  if (isDev()) {
    return getLang() === 'zh' ? payDevZhBaseURL : payDevEnBaseURL
  }

  if (getLang() === 'zh') {
    return payZhBaseURL
  }
  return payEnBaseURL

}

export function supportBaseUrl(): string {

  if (isDev()) {
    return getLang() === 'zh' ? supportDevZhBaseUrl : supportDevEnBaseUrl
  }

  if (getLang() === 'zh') {
    return supportZhURL
  }
  return supportEnURL
}

export function passportBaseUrl(): string {
  if (isDev()) {
    return getLang() === 'zh' ? passportDevZhBaseURL : passportDevEnBaseURL
  }
  if (getLang() === 'zh') {
    return passportZhBaseURL
  }
  return passportEnBaseURL
}

export function centerVipBaseUrl(): string {
  if (isDev()) {
    return getLang() === 'zh' ? centerVipDevZhBaseURL : centerVipDevEnBaseURL
  }
  if (getLang() === 'zh') {
    return centerVipZhBaseURL
  }
  return centerVipEnBaseURL
}

export function getMobileClientDownloadUrl(): string {
  return mobileClientDownloadUrl
}

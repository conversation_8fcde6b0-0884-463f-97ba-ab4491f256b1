/**
 * @unocss-include
 */
import loginLocale from '@central/loginer/src/locales/en'
import contactUsLocale from '@central/contact-us/src/locales/en'
import accountLocale from '@central-legos/account/src/locales/en'


export default {
  ...contactUsLocale,
  ...loginLocale,
  ...accountLocale,
  "createAccount": "Create a new RecCloud Account",
  "freeRecording": "Free Recording",
  "pause": 'Pause',
  'resume': 'Resume',
  "002745": "Online video editing",
  'stopRecord': 'Stop',
  'freeOnlienRecorder': 'Free Online Recording',
  'screen': 'Screen',
  'camera': 'Webcam',
  'systemSound': 'System Sound',
  'microphone': 'Microphone',
  'back': 'Back',
  'recording': 'Recording',
  'newRecord': 'Start a New Video',
  'save': 'Save',
  'video_upload': 'Upload to cloud',
  'moreFeatures': 'Please download the desktop version for customized recording, video editing, and more advanced features.',
  'sureToBack': 'Please note that the video won\'t be saved if you go back to the mainboard now.',
  'stop': 'Stop Recording',
  'continue': 'Continue Recording',
  'noCamera': 'The system camera cannot be detected.',
  'ok': 'OK',
  'noMic': 'The system sound input device cannot be detected.',
  'noAudio': 'Sorry, the current Mac version doesn\'t support recording system sound online, please download desktop version instead. Thank you for your understanding.',
  'download': 'Free Download',
  'noPremission': 'Please allow relevant system permissions at the navigation bar.',
  'authorize': 'Click here to authorize',
  'chrome': 'Here is an example of Chrome',
  'privilege': 'Privileges',
  'shareVideo': 'Share video easily',
  'team': 'Team collaboration',
  'storage': 'Save local storage space',
  'dataSafe': 'Save videos at anytime',
  'uploadFail': 'Upload failed, please try again.',
  'uploading': 'Saving...Please do not leave this page.',
  'cancelUpload': 'Cancel',
  'uploadAgain': 'Retry',
  'uploadSuccess': 'Upload successfully! You can go to cloud platform and manage the videos now.',
  'goNow': 'Go now',
  'storageFull': 'The cloud storage is full, please upgrade or clear the space.',
  'clearStorage': 'Clear storage',
  'upgradeStorage': 'Upgrade storage',
  'freeVideoLimit': 'You have reached <span id="video-limit-num"></span> free videos storage limit, please upgrade storage or delete video.',
  'deleteVideo': 'Delete video',
  'upgradePermission': 'Upgrade ',
  'startRecording': 'Start Recording',
  'chromePlease': 'Sorry, this browser doesn\'t support online recorder, please try another one (Chrome is suggested), or download the desktop version.',
  'noScreen': 'Please grant screen recording permission and try again.',
  'noInstall': 'One-click to start online recording without installation',
  'clickStart': 'Click "Start Recording"',
  'chooseMode': 'Choose the screen you want to record and click "Share"',
  'downloadUpload': 'Save the video to local disk, or upload to cloud',
  'useOnline': 'Online Recorder Without Download',
  'apOnlineRecorder': 'Apowersoft free online recorder can capture browser activities, including both screen and webcam.',
  'easyToUse': 'Easy-to-use',
  'oneClickToUse': 'Any online video you can think of, Apowersoft free online screen recorder can help you to record it with just one simple click.',
  'freeUnlimited': 'Free & Unlimited Recording',
  'timeUnlimited': 'Enjoy unlimited recording time for free! No need to worry what if you cannot record the entire video since we offer limitless recording length for you.',
  'desktopCamera': 'Record Desktop & Webcam',
  'singleorBoth': 'You can record desktop activity, webcam, or record both at the same time.',
  'multipleModes': 'Multiple Recording Modes',
  'picInPic': 'With our free online screen recorder, you can easily make a tutorial video with screen, sound, and even under picture-in-picture mode, perfectly recording all kinds of courses.',
  'screenAndSound': 'Screen and Sound Synchronization',
  'synchronization': 'Apowersoft online screen recorder supports recording screen, system sound, and microphone with screen & audio synchronization.',
  'pipSupport': 'Picture-in-picture Recording',
  'appsSupport': 'Support recording chatting or meeting apps such as Skype, Google Meet, Zoom meeting, etc. And support recording customized areas.',
  'saveandShare': 'Video Managing & Sharing',
  'shareToOthers': 'The video can be saved on the local disk. Or it can be uploaded to the cloud platform and shared on social media or be shared with others.',
  'localDIsk': 'Save to local disk',
  'freeSaveFile': 'The video will be automatically saved to the output folder on the local disk once clicked the Save button.',
  'cloudPlatform': 'Upload to Cloud Platform',
  'cloudManagement': 'Apowersoft provides a professional cloud management platform for you to upload, share and manage your own videos.',
  'amazingRecorder': 'This app is really amazing! And it has absolutely helped me record videos for future plans on being a YouTuber.',
  'moocer': 'It is very nice to use this tool to make tutorials. Thank you Apowersoft!',
  'twiceaWeek': 'It works so well and easy-to-use. I am now using it twice a week for my gameplay and Vlogs.',
  'worksWell': 'This app works very well. It has good sound quality and records the video smoothly. I am now very satisfied with the app and hope you guys can keep the good work up. <3',
  'shareSystemAudio': 'Please select "Share system audio" to record the screen with sound.',
  "Search ShowMore": "Search",
  "Start Recording": "Start Recording",
  "Record": "Record",
  "Share": "Share",
  "View More": "View More",
  "Sorry... The page se...": "Sorry... The page seems to be missing.",
  "We can not find /404...": "We can not find /404/. It might be an old link or maybe it moved.",
  "Maybe you will find ...": "Maybe you will find something even more awesome and helpful, have a try...",
  "Home Page": "Home Page",
  "Our Blog": "Our Blog",
  "Help Center": "Help Center",
  "Download Page": "Download Page",
  "About:": "About: ",
  "ArticleCount": "%s Articles, ",
  "CommentCount": "%s Comments",
  "PostByWhom": "Posts by %s",
  "ContinueReading": "Continue reading »",
  "NotFound": "Not Found",
  "SorryForNotFound": "Sorry, but you are looking for something that isn't here.",
  "Comment": "Comment",
  "MustLogin": "You must %slog in%s to post a comment.",
  "LeaveAComment": "Leave a Comment",
  "Name": "Name",
  "InvalidName": "Please input your name!",
  "Email": "Email",
  "InvalidEmail": "Please input a valid email address!",
  "WriteYourReview": "Write your review",
  "InvalidReview": "Please input review content!",
  "Submit": "Submit",
  "CancelReply": "Cancel Reply",
  "CommentAwaiting": "Your comment is awaiting moderation.",
  "CommentReply": "Reply",
  "CommentClosed": "Comments are closed.",
  "Contact Us": "Contact Us",
  "Privacy Policy": "Privacy Policy",
  "FOLLOW US": "Follow Us",
  "Copyright": "Copyright © 2019 ShowMore All Rights Reserved.",
  "Home": "Home",
  "Blog": "Solution",
  "Video on Fire": "Video on Fire",
  "Search": "Search",
  "Sorry!": "Sorry!",
  "NoPostMatched": "No posts matched your criteria. Wanna search instead?",
  "Related articles": "Related articles",
  "Category": "Category",
  "Ads": "Ads",
  "Posted by": "Posted by",
  "on ...": 'on %s',
  "to": 'to',
  "More": "More",
  "Share this video": "Share this video",
  "More Videos": "More Videos",
  "Categories": "Categories",
  "Views": "Views",
  "Come and record your...": "Pass on Your Message Instantly with RecCloud",
  "Video about ": "Video about ",
  "Recently SHOWS": "Recently SHOWS",
  "Loading": "Loading",
  "Popular videos": "Popular videos",
  "DateType": "F j, Y",
  "Follow me on Twitter": "Follow me on Twitter",
  "Want to join these c...": "Want to join these categories?",
  "Back to top": "Back to top",
  "report": "Report",
  "choose_your_language": "Choose Language",
  "last_updated_on_by": "Last updated on {0} by {1}",
  "last_updated_on": "Last updated on {0}",
  "full_comma": ".",

  "upload": "Upload",
  "record": "Record",

  //以下为首页，头部，底部新增字段
  //首页
  //first
  "title": "Video Conveys More",
  "banner_descript": "ShowMore allows you to record, upload, host and share videos for multiple purposes.",

  "video_content_descript": "We've Got Everything You Need",
  "screen_recording": "Screen Recording",
  "screen_recording_info": "The screen recorder included with ShowMore lets you record video and audio on a computer within a matter of minutes. Additionally, you will also have access to a range of annotations while recording. This includes text, shapes, arrows, and more.",
  "video_hosting": "Video Hosting",
  "video_hosting_info": "ShowMore also serves as a video hosting platform which gives you the ability to instantly upload videos to its own cloud storage for easy access and management. With its security protection, files stored in ShowMore are well protected.",
  "video_sharing": "Video Sharing",
  "video_sharing_info": "With the video sharing function, you are enabled to share the direct video link to others for instant viewing or even embed the uploaded videos to your website/blog. Plus, you can also share what you have uploaded to Facebook, Twitter and Google+, etc.",
  "learn_more": "Learn More",
  //second
  "videos_created": "Videos Created",
  "satisfied": "Satisfied Users Worldwide",
  "trusted_brand": "Trusted Brand",
  "years": "10 Years",
  //third
  "create_purpose": "We Create It for Any Purposes",
  "education_training": "For Education and Training",
  "education_training_info": "Educators and students can make full use of ShowMore for e-learning video production or saving any useful educational videos for further learning. And those who wish to create training videos are included as well.",
  "entertainment": "For Personal Entertainment",
  "entertainment_info": "There are times you may desire to capture amazing gameplay achievements, save wonderful video chat moments with your family members, or record live streaming videos for back up. ShowMore makes all these tasks easy.",
  "business_work": "For Business and Work",
  "business_work_info": "From walk-through demonstrations of an application to customer support, ShowMore provides the easiest way to create intuitive videos, upload the videos securely, and even collaborate with your colleagues.",
  //forth
  "tip_trick": "Tips and Tricks",

  //头部
  "VIP Benefits": "Pricing",
  "Help": "Help",
  //底部
  "About ShowMore": "About ShowMore",
  "Tutorials": "Tutorials",
  "Resources": "Resources",
  "Terms": "Terms",
  "Security": "Security",
  "Cookies Policy": "Cookies Policy",
  "Privacy": "Privacy",
  "COMPANY": "Company",
  "footer_blog": "Help and Resources",
  //
  "Reviews": "Reviews",
  "Watch Video Guide": "Watch Video Guide",

  //vip
  "vip_title": "Enjoy More Benefits Than Ever Before",
  "vip_payment": "Ways of Payment",
  "vip_secure": "Secure",
  "vip_refund": "Money-Back Guarantee",
  "vip_refund_info": "All of our products come with a 30-day money back guarantee (7 days for Monthly license).",
  "vip_support": "Support Service",
  "vip_support_info": "Please <a href='/contact'>contact us</a> for pricing options regarding:<br>Volume pricing & Site licenses & Multi-user license.",

  //right-bar
  "Support": "Support",
  "Review": "Review",


  //这里是RECCLoud的翻译
  "RecCloud": "RecCloud",
  "FAQ": "FAQ",
  //rec banner
  "rec_banner_title": "Record a video,<br>send it to the world",
  "rec_banner_desc": "RecCloud allows you to upload, share and manage videos online as well as to experience video collaboration.",
  "rec_banner_start_tips": "Start Free with RecCloud",
  //feature
  "rec_feature_title1": "Create video with ease",
  "rec_feature_desc1": "Record all your screen activities with system sound or your own voice to make the video more intriguing.",
  "rec_feature_title2": "Upload video in seconds",
  "rec_feature_desc2": "Upload all your video files to the cloud space and save more of your local storage space. Meanwhile, you can set exclusive password for them and keep the private content to yourself only.",
  "rec_feature_title3": "Share video with one link",
  "rec_feature_desc3": "Copy the direct video link and share the videos online to others, or publish them to different social media platform, such as Facebook, Twitter, and YouTube.",
  "rec_feature_title4": "Manage playlist  with others",
  "rec_feature_desc4": "Add your family members, friends, or colleagues as the playlist collaborators, and you will be able to manage the playlist together!",
  //analyze
  "rec_analyze_title": "RecCloud has helped <b>1,000,000</b><b>+users</b>.",
  "rec_analyze_desc": "Check out their thoughts about this tool!",
  "rec_analyze_star": "Highly Recommend",
  "rec_analyze_recommend1": "I video chat with my girlfriend every Thursday night via Skype. I was searching for a program to record the whole chitchat until found reccloud. Nice program, hope you guys can keep doing the excellent work.",
  "rec_analyze_recommend2": "Just what I was looking for! I have a really high demand for recording my WebEx meetings, well, almost everyday. And now it seems like I have found a convenient tool which not only lets me record the meetings, but also lets me save them on cloud directly. Thanks for whoever made this program! ",
  "rec_analyze_recommend3": "Basically, I’m using it to record my live streams everyday. I can now insert my videos to the site by using its embedded code and share the playlist to my friends through video collaboration. I’ll give it five stars. It deserves it.",
  //usage
  "rec_usage_title": "How to Maximize the Use of RecCloud",
  "rec_usage_name1": "Record Video Call",
  "rec_usage_name2": "Record Game",
  "rec_usage_name3": "Record Online Course",
  "rec_usage_name4": "Record Online Videos",
  "rec_usage_name5": "Record Meeting",
  "rec_download": "Download App",
  //footer
  "About RecCloud": "About RecCloud",
  "RecCloud Copyright": "Copyright © 2025 RecCloud All Rights Reserved",
  "Popular Topics": "Popular Topics",

  //share 分享到其他地方的翻译
  "RECCLOUD.COM": "RECCLOUD.COM",
  "This video is password-protected": "This video is password-protected",

  //联系我们表单(RecCloud Online Form)翻译
  "RecCloud Online Form": "RecCloud Online Form",
  "required fields": "required fields",
  "Your Full Name": "Your Full Name",
  "Email Address": "Email Address",
  "Phone Number": "Phone Number",
  "Message": "Message",
  "Anti-spam question": "Anti-spam question",
  "Please answer the simple question below. This to prevent spam bots from submitting this form": "Please answer the simple question below. This to prevent spam bots from submitting this form",


  "My video": "My Space",
  "Video List": "Playlists",

  "002900": "Step One: Choose the screen you want to record.",
  "002901": "Step Two: Click \"Share system audio\".",
  "002902": "Step Three: Click \"Share\" and start recording.",
  "002903": "It's suggested to use the desktop version for recording longer than 30mins, for you may fail to save extra long recordings online directly.",
  "upload_fail_click_to_retry": 'Upload failed, please try again.',
  "click_to_retry": 'Retry',
  "000918": "Share video",
  "000919": "Link",
  "000920": "Password",
  "000921": "Copy link and password",
  "000922": "Invalid video ID!",
  "000923": "Failed to get link and password!",
  "000924": "Link has been copied to clipboard.",
  "Delete": "Delete",
  "000927": "Videos",
  "000928": "Title / Date",
  "000930": "Status",
  "000931": "Options",
  "000932": "Previous",
  "000933": "Next",
  "000935": "Category",
  "000936": "Private",
  "000937": "Public",
  "000938": "Password:",
  "000939": "This field is required.",
  "000940": "Cancel",
  "000941": "Title",
  "Description": "Description",
  "000943": "Upload your video",
  "000944": "Drop video to upload",
  "000945": "Drag & drop videos here",
  "000946": "or",
  "000948": "Others",
  "000949": "Save",
  "000950": "Account Settings",
  "000951": "Upload an image",
  "000952": "User Name",
  "000955": "Video is uploading, are you sure you want to cancel?",
  "000956": "Yes",
  "000957": "Failed to load category!",
  "000958": "User Center",
  "000959": "Approved",
  "000960": "Unapproved",
  "000961": "Pending",
  "000962": "Transcoding...",
  "000963": "Failed to load the video information.",
  "000964": "Submitted successfully!",
  "000965": "Failed to submit!",
  "000966": "Failed to upload!",
  "000967": "Upload successfully!",
  "000968": "The file you are uploading may not be a valid video file.",
  "000970": "Updated profile successfully!",
  "000971": "Old password is not correct!",
  "000972": "Failed to save profile!",
  "000974": "Deleted successfully!",
  "000975": "Error while deleting the video.",
  "000976": "Search",
  "000978": "Video title",
  "000979": "Please add description",
  "000980": "Old Password",
  "000981": "New Password",
  "000982": "Confirm Password",
  "000983": "This video is not available.",
  "000984": "Incorrect password, please try again:",
  "000985": "Please enter your password",
  "000986": "Liked!",
  "000987": "Failed to like!",
  "Copy link": "Copy link",
  "000989": "Uploading",
  "000990": "Are you sure you want to delete the selected videos?",
  "000991": "Cancel uploading",
  "000992": "OK",
  "000993": "Upload video",
  "001001": "Technical Support",
  "001002": "Log out",
  "001003": "You haven’t added any videos yet.",
  "001004": "Edit Video",
  "001005": "All",
  "001006": "Change Avatar",
  "001007":
    "This video may contain violent, sexual or copyright-protected content.",
  "001009": "Privacy",
  "001010": "Publish",
  "001064":
    "The videos will be uploaded to ShowMore which is a reliable cloud platform. Learn More...",
  "001069": "Only the author can view it.",
  "001070": "Upload pending...",
  "001088": "The video can be seen by anyone who visits ShowMore.",
  "001089": "Unlisted",
  "001090": "Only people who know the video link can view it.",
  "001091": "Encrypted",
  "001092": "Only people who know the video link and password can view it.",
  "001094": "Profile updated successfully.",
  "001159":
    "We're sorry, but we cannot display this content because it has been labelled private by its author.",
  "001165": "Copy",
  "001166": "Login",
  "002749": "My Account",
  "001580": "Share Link",
  "001581": "Embed Code",
  "001583": "Playlists",
  "001584": "Edit",
  "001586": "Share",
  "Actions": "Actions",
  "001588": "Privacy settings",
  "001590": "Add to Playlist",
  "001591": "Sort",
  "001592": "Date added",
  "001593": "Views",
  "001594": "Protected",
  "001596": "New Playlist",
  "001597": "You haven’t created any playlists yet",
  "001598": "Back to playlist",
  "001599": "Empty playlist",
  "001600": "Add all to",
  "001601": "Choose file",
  "001602": "Add selected videos to the following playlist",
  "001603": "Playlist Title",
  "001604": "Create new playlist",
  "001605": "Submit",
  "Add": "Add",
  "001608": "No videos",
  "001610": "Are you sure you want to submit privacy settings?",
  "001611": "Once submitted, video information will be updated.",
  "001612": "Privacy settings completed!",
  "001613": "Delete",
  "001614": "The files deleted cannot be recovered. ",
  "001617": "Delete playlist",
  "001618": "Videos won’t be deleted.",
  "001619": "Delete playlist successfully!",
  "001620": "Delete video from playlist.",
  "001622": "Add to playlist successfully!",
  "001623": "Create a playlist successfully!",
  "001624": "Edit playlist successfully!",
  "001628": "Fail to create a playlist.",
  "001629": "Fail to edit playlist.",
  "001631": "You haven't selected any videos yet.",
  "001632": "It won't delete videos in the playlist.",
  "001633": "This video already exists.",
  "001634": "Copied",
  "001635": "Likes",
  "001636": "New playlist",
  "001650": "Playlist Name",
  "001651": "Categories",
  "001738": "Report video",
  "001739": "Describe the reason of reporting...",
  "001740": "Sexual content",
  "001741": "Violent or repulsive content",
  "001742": "Hateful or abusive content",
  "001743": "Harmful dangerous acts",
  "001744": "Child abuse",
  "001745": "Promotes terrorism",
  "001746": "Spam or misleading",
  "001747": "Infringes my rights",
  "001748": "Captions issue",
  "001749": "Report",
  "001750": "Play",
  "001751": "Like",
  "001752": "Unlike",
  "001753": "Save to...",
  "001868": "Leave your comment here",
  "001871": "Comments",
  "001872": "Load more",
  "001873": "{0} days ago",
  "001874": "{0} day ago",
  "001875": "{0} hour ago",
  "001876": "{0} minutes ago",
  "001877": "{0} minute ago",
  "001878": "Just now",
  "001879": "Comment successfully",
  "001880": "{0} hours ago",
  "001920": "Views",
  "002006": "Recorder Launched",
  "002007": "Download ShowMore Online Launcher",
  "002008": "Please install ShowMore Online Launcher to enable recording",
  "002009": "Please allow to open ShowMore Online Launcher",
  "002010":
    'Please <a class="download-btn" href="javascript:void(0)">redownload it</a> if application cannot be launched',
  "002011": "Click to install",
  "002021": "Start Recording",
  "002022": "Record",
  "002023": "Full Screen",
  "002024": "Pause",
  "002025": "Mute",
  "002026": "Sound",
  "002027": "Quality",
  "002028": "Speed",
  "002054": "Free Account",
  "002055": "Free",
  "002056": "Save recordings to local disk",
  "002057": "Sign up",
  "002058": "Free Trial",
  "002059": "Annual VIP",
  "002060": "Download or save videos from ShowMore",
  "002061": "Faster technical support",
  "002062": "Monthly VIP",
  "002063": "Please finish the payment in the new window.",
  "002064": "Please don't close this window until the payment is done.",
  "002065":
    "Please click the button below basing on your situation after the payment.",
  "002066": "Thanks for your report!",
  "002067": "Payment Success!",
  "002068": "Payment is done!",
  "002069": "Requery",
  "002070": "Pay again",
  "002071": "Inquiring your payment information...",
  "002072": "{0}/mo",
  "002073": "billed annually",
  "002074": "billed monthly",
  "002075": "Record video with watermark",
  "002076": "No watermark attached",
  "002077": "Unlimited recording time",
  "002078": "{0} storage for hosting videos",
  "002079": "Buy Now",
  "002080": "Payment success!",
  "002081": "ShowMore <span>VIP</span> Account",
  "002082": "Enjoy more features and extra storage space",
  "002083": "Upgrade",
  "002084":
    "Used:<span class='used-size'></span>/<span class='all-size'></span>",
  "002098": "Enjoy More Benefits Than Ever Before",
  "002099": "Download",
  "002104": "<b>{0}GB</b> storage for hosting videos",
  "002105": "Please input the password",
  "002108": "<em>{{checkLength}}</em> videos selected",
  "002109": "<em>{{checkLength}}</em> selected",
  "date": "Date",
  "002111": "Settings",
  "002112": "Type password to view",
  "002113": "{0} videos",
  "002114":
    'After <a href="javascript:video(0)" @click="newPlayList">creating the playlist</a>, you can use it to manage the videos and invite other people to manage it together.',
  "002115": 'You can add videos by <a href="/" target="_blank">recording</a>.',
  "002116": "Password protected",
  "002117": "This video is password-protected",
  "002119": "{{list.video_count}} videos",
  "002121": "Please add a description for the playlist",
  "002136": "View Profile",
  "002137": "Support",
  "002138": "Exit",
  "002139": "Settings",
  "002140": "Basic",
  "002141": "Collaboration",
  "002142": "Team members",
  "002144": "Please enter a valid email",
  "002145": "Upload",
  "002146": "Close",
  "002147": "There's no enough storage for the video, please upgrade the space",
  "002149": "Back",
  "Creator": "Creator",
  "002151": "Please enter the email",
  "002152": "Collaborative playlist",
  "002153": "Collaborator added successfully!",
  "You cannot add yourself": "You cannot add yourself",
  "002155": "The account does not exist under this email",
  "The member has been added": "This member already exists",
  "002157": "You are not the creator of this playlist",
  "002158": "You can add 10 collaborators at most",
  "002159": "Collaborator removed successfully!",
  "002160": "This member doesn't exist",
  "002161": "Please enter the name of the playlist",
  "002162": "Select the playlist",
  "002163": "{0} playlists",
  "002164": "Uploader",
  "002165": "Add collaborator",
  "002166": "Set a password for this playlist",
  "002167": "Playlist Settings",
  "002168":
    'You can add videos by&thinsp;<a href="/" target="_blank">uploading</a>',
  "002169": "The playlist is empty",
  "002170":
    'You can select videos from <a href="javascript:void(0)">My Videos</a> or add other ShowMore videos to the playlist',
  "002206": "Visitor",
  "002207": "Record 3 minutes only",
  "002208": "Upload recordings to ShowMore",
  "002209": "Upload local videos to ShowMore",
  "002210": "Video storage space",
  "002211": "Collaboration (10 team members)",
  "002212": "No Ads",
  "Legal": "Legal",
  "002214": "Terms",
  "002216": "Cookies Policy",
  "002217": "Company",
  "002218": "About Us",
  "002219": "Contact Us",
  "002220": "Help",
  "002221": "Language",
  "Random thumbnail": "Random thumbnail",
  "002223": "Refresh",
  "002224": "Transcode successfully",
  "002225": "This playlist is unavailable",
  "002226": "This playlist is password-protected",
  "Change thumbnail": "Change thumbnail",
  "002228": "Random",
  "No search results": "No search results",
  "002230": "VIP Account",
  "Settings saved successfully!": "Settings saved successfully!",
  "002232": "Failed to save settings!",
  "002233": "Have an account?",
  "002235": "Copyright © {0} ShowMore All Rights Reserved",
  "002236": "No thumbnail",
  "002261": "{0} members",
  "002262": "Fail to delete the playlist.",
  "002278": "Continue",
  "002280": "Please allow to open RecCloud Online Launcher",
  "002281": "Download RecCloud Online Launcher",
  "002282": "Please install RecCloud Online Launcher to enable recording",
  "002283": "Download videos from RecCloud",
  "002284": "Enjoy More Benefits Than Ever Before",
  "002285": "Upload recordings to RecCloud",
  "002286": "Upload local videos to RecCloud",
  "Copyright Year RecCloud All Rights Reserved": "Copyright © 2025 RecCloud All Rights Reserved",
  "002288": "RecCloud <span>VIP</span> Account",
  "002289":
    'You can select videos from&thinsp;<a href="javascript:void(0)">My Videos</a>&thinsp;or add other RecCloud videos to the playlist',
  "002297":
    "Here is the description. For example: The 10th Annual Meeting Of WangxuTech.",
  "002307": "LD",
  "002308": "HD",
  "002309": "Ultra HD",
  "002327": "Basic",
  "002328": "Plus",
  "002329": "Pro",
  "002330": "Premium",
  "002331": "Choose Your VIP Plan",
  "002332": "会员",
  "002333": "Plus会员",
  "002334": "Pro会员",
  "002335": "Premium会员",
  "002336": "{0} GB/week",
  "002337": "Unlimited traffic",
  "002338": "单个用户",
  "002339": "{0} Team members",
  "002340": "Get Basic",
  "002341": "Get Plus",
  "002342": "Get Pro",
  "002343": "Get Premium",
  "002344": "Unlimited recording time and no watermark",
  "002345": "Save, upload and download RecCloud videos",
  "002346": "Save, upload and download ShowMore videos",
  "002347": "RecCloud Yearly Subscription (Basic-Plus)",
  "002348": "ShowMore Yearly Subscription (Basic-Plus)",
  "002349": "RecCloud Yearly Subscription (Basic-Pro)",
  "002350": "ShowMore Yearly Subscription (Basic-Pro)",
  "002351": "RecCloud Yearly Subscription (Basic-Premium)",
  "002352": "ShowMore Yearly Subscription (Basic-Premium)",
  "002353": "RecCloud Yearly Subscription (Plus-Pro)",
  "002354": "ShowMore Yearly Subscription (Plus-Pro)",
  "002355": "RecCloud Yearly Subscription (Pro-Premium)",
  "002356": "ShowMore Yearly Subscription (Pro-Premium)",
  "002357": "RecCloud Yearly Subscription (Plus-Premium)",
  "002358": "ShowMore Yearly Subscription (Plus-Premium)",
  "002359": "TRIAL",
  "002360": "Select VIP Subscription",
  "002361": "VIP membership privileges",
  "002362": "Save videos",
  "002363": "Download videos",
  "002364": "Add team members",
  "002365": "<i>50</i>% OFF",
  "002366": "No order is found",
  "002393": "Auto play",
  "002394": "OK",
  "002395": "Manage video",
  "002396": "next video",
  "002397": "Lauching",
  "002398": "Installing...",
  "002399": "Updating...",
  "002400": "Choose file",
  "002401": "Waiting",
  "002402": "Failed",
  "002407": "Size",
  "002560":
    "Sorry! We only support uploading videos of Mp4 format. Please convert the video to Mp4 and upload again.",
  "002561": "Got it",
  "002562": "Convert video",
  "002563": "Network request timeout,please {0}refresh{1} page.",
  'next': "Next",
  'one_day_ago': "one day ago",
  'one_hour_ago': "one hour ago",
  'one_minute_ago': "one minute ago",
  'one_month_ago': "one month ago",
  'one_second_ago': "one second ago",
  'one_week_ago': "one week ago",
  'one_year_ago': "one year ago",
  'prev': "Prev",
  "002700": "Title length is not less than 2",
  "002701": "Password length is not less than 6",
  "002702": "You can add 3 collaborators at most ",
  "002704":
    "1.Please purchase or upgrade to VIP and enjoy more cloud storage space.",
  "002703": "Free users can only store up to # files, please upgrade VIP to unlock. ",
  "002711":
    "The cloud storage is full, please upgrade to VIP for more storage space.",
  "002712":
    "Sorry, this video cannot be played due to it's prevented from sharing.",
  "002734": 'Up to <span style="color: #AA7A40;">20GB</span> Cloud Backups',
  "002739": 'Up to <span style="color: #AA7A40;">200GB</span> Cloud Backups',
  "002740": "Valid until:",
  "002741": "Recommendations",
  "002742": "Advanced recording",
  "002743":
    "Sorry, this playlist cannot be viewed due to it's prevented from sharing.",
  "002744": "This account (id: {2}) is frozen, please contact {0}customer service{1} directly.",
  "002755": "Record Online ",
  "002756": "Screen",
  "002757": "Webcam",
  "002758": "System Sound",
  "002759": "Microphone",
  "002760": "Back",
  "002761": "Recording",
  "002762": "Start a New Video",
  "002764": "Save",
  "002765": "Upload to cloud",
  "002772":
    "Please download the desktop version for customized recording, video editing, and more advanced features.",
  "002774":
    "Please note that the video won't be saved if you go back to the mainboard now.",
  "002763": "Stop Recording",
  "002775": "Continue Recording",
  "002776": "The system camera cannot be detected.",
  "002777": "OK",
  "002778": "The system sound input device cannot be detected.",
  "002779":
    "Sorry, the current Mac version doesn't support recording system sound online, please download desktop version instead. Thank you for your understanding.",
  "002773": "Free Download",
  "002791": "Please allow relevant system permissions at the navigation bar.",
  "002792": "Click here to authorize",
  "002793": "Here is an example of Chrome",
  "002766": "Privileges",
  "002767": "Share video easily",
  "002768": "Team collaboration",
  "002769": "Save local storage space",
  "002770": "Save videos at anytime",
  "002807": "Saving...Please do not leave this page.",
  "002800": "The cloud storage is full, please upgrade or clear the space.",
  "002801": "Clear storage",
  "002802": "Upgrade storage",
  "002803":
    'You have reached <span id="video-limit-num"></span> free videos storage limit, please upgrade storage or delete video.',
  "002804": "Delete video",
  "002805": "Upgrade ",
  "002771": "Start Recording",
  "002790":
    "Sorry, this browser doesn't support online recorder, please try another one (Chrome is suggested), or download the desktop version.",
  "002806": "Please grant screen recording permission and try again.",
  "002808": "Pause",
  "002809": "Resume",
  "002810": "Stop",
  "002811": "Free recording",
  "002841":
    "The video is unable to play as a result of a suspected content violation. </br>You can <span class='violation highLight' @click='handleViolation'>request for an auto video review</span>",
  "002842":
    "The video is unable to play as a result of a suspected content violation.</br> You can <span class='violation highLight' @click='handleViolation'>apply for manual review.</span>",
  "002843":
    "The video is unable to play as a result of a suspected content violation,</br> and it's currently under manual review.",
  "002852":
    "Please read <a href='https://reccloud.com/terms' target='_blank' style='color:#AA7A40';'>Accepted Terms of Use</a> carefully for safeguarding information security.",
  "002904": "Upload failed, please try again.",
  "002905": "Retry",
  "002906": "Cancel",
  "Notice": "Notice",
  "002908": "New Folder",
  '002909': 'Trim Video',
  '002910': 'Free Online Video Trimmer',
  '002911': 'Maximum size:',
  '002912': '. Download Pro version for larger files.',
  "002913": "Add file from local ",
  '002914': 'Are you sure you want to delete this video?',
  '002915': 'Processing',
  '002916': 'Done',
  '002917': 'Failed, Please try again!',
  '002918': 'loading',
  '002919': 'Please update your broswer to watch this video',
  "002920": "Undo",
  '002921': 'Video cutting',
  '002922': 'Remove the Part',
  '002923': 'Try Pro version',
  '002924': 'Start trimming',
  '002925': 'Free video editing',
  "002926": "Trim Video",
  "002927": "Crop Video",
  '002928': 'Free video clipping',
  '002929': 'Widescreen',
  '002930': 'Instagram',
  '002931': 'Portrait',
  '002932': 'Traditional',
  '002933': 'Original',
  '002934': 'Custom',
  '002935': 'Minimum length is 1 second!',
  '002936': 'Start processing',
  '002937': 'Re-add',
  '002938': 'Change Log',
  'welcomeTitle': 'Thanks for your interest in RecCloud and hope you enjoy using the service provided by us. If you have any puzzles or problems related to RecCloud, you can feel free to contact us via the following contact information.',
  'ticketSupport': 'Ticket Support',
  'submitForm': 'Submit the issue you encountered via the online form below, and wait for our prompt reply.',
  'questionType': 'Question Type',
  'selectQuestionType': 'Select Problem Type',
  'theme': 'Subject',
  'buyAndSafe': 'Purchase and security',
  'regCode': 'Registration and key code',
  'softUse': 'Using the product',
  'techQus': 'Technical issue',
  'refund': 'Refund',
  'feedback': 'Suggestion or Complaints',
  'cooped': 'Business Cooperation',
  'attachment': 'Attachments',
  'reset': 'Reset',
  'addAttachment': 'Add file',
  "001167": "My Account",
  "001168": "Exit",
  "001169": "Forgot password",
  "001170": "Password-less login",
  "001171": "Sign up",
  "001172": "Get",
  "001173": "Password login",
  "001174": "Reset Password",
  "001175": "You can reset your password through cell phone or mailbox.",
  "001176": "Binding",
  "001177": "Skip",
  "001178":
    "After binding phone number, you can use it to login or retrieve the password.",
  "001179":
    "After binding Email, you can use it to login or retrieve the password.",
  "001181": "Change Password",
  "001182": "Activate",
  "001183": "Log out",
  "001184": "Upgrade to VIP",
  "001185": "Renew",
  "001186": "FAQ",
  "001187": "Community",
  "001188": "Buy now",
  "001189": "Learn more",
  "001190": "Activation code",
  "001191":
    "Please input the activation code in the mail to activate the software.",
  "001192": "Your software has been activated.",
  "001193": "You can enjoy more benefits afer signup and login.",
  "001194": "Login & Singup",
  "001196": "You have completed the registration.",
  "001197": "Please return to the software to continue using",
  "001198": "Close the page",
  "001200": "Phone Number/Email",
  "001201": "Password",
  "001202": "or",
  "001203": "Login with other accounts",
  "001204": "Phone Number",
  "001205": "Verification Code",
  "001206": "Email",
  "001207": "Confirm",
  "001208": "Version",
  "001209": "License type",
  "001210": "Valid until",
  "001211": "No activation code?",
  "001212": "Please input email address",
  "001213": "Please input password",
  "001214": "Your last login is expired, please login again!",
  "001215": "An unknown error occurred!",
  "001216": "Please input your email address or phone number",
  "001217": "Please input a valid email address or phone number",
  "001218": "Verification Code",
  "001219": "Verification code",
  "001220": "Incorrect account or password",
  "001221": "Please input phone number",
  "001222": "Please input verification code",
  "001223": "Invalid verification code",
  "001226":
    "Your phone number has been in our record, please {0}sign in{1} directly.",
  "001227": "Your email has been in our record, please {0}sign in{1} directly.",
  "001230": "This phone number has been used to sign up, please {0}Login{1}",
  "001231":
    "This email has been bound to another account, please change a new one.",
  "001232": "Phone number has been bound!",
  "001233": "Email has been bound!",
  "001234": "Expired",
  "001235": "{0} days later",
  "001236": "Please input activation code",
  "001237": "Invalid activation code",
  "001238": "Activations overrun",
  "001239": "The activation code has expired",
  "001240": "Failed to get activation info!",
  "001241": "Personal",
  "001242": "Commercial",
  "001243": "Trial",
  "001244": "Daily",
  "001245": "Quarterly",
  "001246": "Yearly",
  "001247": "Lifetime",
  "001248": "Anonymous",
  "001249": "Failed to change avatar!",
  "001250": "Failed to upload avatar!",
  "001251": "{0} account has been bound!",
  "001252": "Failed to bind to third party accounts!",
  "001253": "Failed to login with third party accounts!",
  "001255": "Account",
  "001256": "Software has been activated",
  "001257": "Registered",
  "001258": "Information",
  "001259": "Orders",
  "001260": "Tickets",
  "001261": "Ticket",
  "001262": "Submit Ticket",
  "001263": "Personal information",
  "001264": "My Orders",
  "001265": "My Tickets",
  "001266": "Change password",
  "001267": "Change Avatar",
  "001268": "Modify",
  "001269": "Binding Account",
  "001270": "Upload avatar",
  "001271": "Save",
  "001272": "Male",
  "001273": "Female",
  "001274": "Unspecified",
  "001275": "Order Number",
  "001276": "Time",
  "001277": "Download",
  "001278": "Forum",
  "001279": "No orders",
  "001280": "Order Now",
  "001281": "Used",
  "001282": "Valid until",
  "001284": "Theme",
  "001285": "Product",
  "001286": "Status",
  "001287": "Submit ticket",
  "001288": "Add attachments",
  "001289": "Record",
  "001290": "All Tickets",
  "001291": "Close ticket",
  "001292": "Submit",
  "001293": "Back",
  "001294": "New Password",
  "001295": "Confirm Password",
  "001296": "Nickname",
  "001297": "Phone Number",
  "001298": "First name",
  "001299": "Last name",
  "001300": "Gender",
  "001301": "Birthday",
  "001302": "Company",
  "001303": "Industry",
  "001305": "Level of Education",
  "001306": "Address",
  "001307": "City",
  "001308": "Postal Code",
  "001309": "Country",
  "001310": "Question Type",
  "001311": "Select Problem Type",
  "001312": "Subject",
  "001314": "Attachments",
  "001315": "Select Product",
  "001317": "Repeat the password",
  "001318": "Bind",
  "001319": "Please input your nickname",
  "001320": "Nickname has been changed!",
  "001321": "Failed to change nickname!",
  "001322": "Profile saved!",
  "001323": "Failed to save profile!",
  "001324": "Year",
  "001325": "Month",
  "001326": "Date",
  "001327": "View {0} codes",
  "001328": "Failed to get orders!",
  "001329": "All",
  "001330": "Unused",
  "001331": "Failed to load activation codes!",
  "001332": "Replied",
  "001333": "Pending",
  "001334": "Resolved",
  "001335": "Pending",
  "001336": "Replied",
  "001337": "Resolved",
  "001338": "Purchase and security",
  "001339": "Registration and key code",
  "001340": "Using the product",
  "001341": "Technical issue",
  "001342": "Refund",
  "001343": "Suggestion or Complaints",
  "001344": "Business Cooperation",
  "001345": "Others",
  "001346": "Failed to get tickets!",
  "001347": "Submit ticket successfully!",
  "001348": "Failed to submit ticket!",
  "001349": "Failed to get ticket detail!",
  "001350": "Failed to reply ticket!",
  "001351": "Ticket is closed!",
  "001352": "Failed to close ticket!",
  "001353": "The two password fields didn't match.",
  "001354": "Password is changed!",
  "001355": "Failed to change password!",
  "001360": "This phone number is already registered, please use another one.",
  "001361": "This Email is already registered, please choose another one.",
  "001362": "Loading...",
  "001363": "Not publicly disclosed",
  "001366": "Create an account",
  "001367": "Already have an account?",
  "001368": "Or Continue with",
  "001369": "New account?",
  "001377":
    "The Account does not exist under this Email address. You can {0}Register Now{1}",
  "001378":
    "The Account does not exist under this phone number. You can {0}Register Now{1}",
  "001379":
    "You can request verification code under an Email address 5 times per day.",
  "001380":
    "You can request verification code under a phone number 5 times per day.",
  "001381": 'You\'ve bound to the phone number, click "Skip" below to continue',
  "001382":
    'You\'ve bound to the email address, click "Skip" below to continue',
  "001383": "The {0} account has bound to another account!",
  "001384": "Avatar saved!",
  "001385": "About Us",
  "001386": "Contact Us",
  "001387": "Promotion",
  "001388": "Guide",
  "001389":
    "Users enter the registration process means that they accepted {0}Terms of Service{1}, {2}Privacy Policy{3} and {4}Cookies Policy{5}",
  "001390": "Accepted {0}Terms of Service{1}, {2}Privacy Policy{3}",
  "001391": "Please check the Terms of Service",
  "Account doesn’t exist": "Account doesn’t exist",
  "001399": "Without binding",
  "001400": "Apowersoft Support Team",
  "001401": "Settings",
  "001403": "Unbind account",
  "001404": "Delete Account",
  "001405": "The operation will not be reverted. Please think twice!",
  "001406": "Are you sure you want to unbind {{name}}?",
  "001407": "After unbinding, you won't be able to login with this account.",
  "001408": "Unbind",
  "001409": "Please enter the password to delete your account",
  "001410": "OK",
  "001411": "Password error",
  "001413":
    "The account has been deleted, and you will automatically be logged out.",
  "001414":
    "Deleting your account information, personal information and all relevant data",
  "001415": "I have known that all the deleted data will not be reverted.",
  "001416":
    "We will delete your account information and all associated data, you won't be able to use products and services any more.",
  "001417": "Unbind successfully",
  "001418": "Number",
  "001419": "Bind phone number",
  "001420": "Bind email",
  "001422": "Login success",
  "001423": "Register successfully",
  "001424": "Go to Account Center",
  "001427": "Please input activation code",
  "001428": "You have completed the login!",
  "001429": "You can close the page and continue using software",
  "001430":
    "After skipping the binding, you won't receive any Email regarding software activation information.",
  "001431": "Confirm again",
  "001433": "Click to view more",
  "001434": "Bind phone number for account secure",
  "001435": "Bind email for account secure",
  "001527": "Monthly",
  "001528": "Quarterly",
  "001538": "Please finish the payment in the new window",
  "001539": "Please don’t close this window until the payment is done",
  "001540":
    "Please click the button below basing on your situation after the payment",
  "001541": "Thanks for your report!",
  "001542": "Payment Success!",
  "001543": "Payment is done",
  "001544": "Requery",
  "001545": "Pay again",
  "001546": "Inquiring your payment information...",
  "001689":
    "Special Offer, {0}One-click{1} installer for {2}14+ products{3} including ApowerMirror, ApowerREC, ApowerEdit, ApowerManager and more.",
  "001690":
    "Free item does not support FREE lifetime upgrades, Please{0}Order Now{1}",
  "001710": "VIP",
  "001711": "Please remember your VIP account.",
  "001712": "How to activate your product?",
  "001713":
    "Download and install your software. If already installed, ignore it.",
  "001714":
    "Inside your software, find “Log in & Sign up”, then input your ID and password.",
  "001715":
    "If requested to enter an activation code, click on username and “Activate”, then use the code listed above.",
  "001716": "Product Name",
  "001717": "Discount",
  "001718": "Quantity",
  "001719": "Actual Price",
  "001720": "Special Offer",
  "001721": "Limited Time Offer",
  "001722":
    '"Apowersoft Unlimited" includes all products released on apowersoft.com.',
  "001723": "Upgrade Now",
  "001724": "30-day Money Back Guarantee",
  "001725": "Need Help?",
  "001726": "Other contact details for Apowersoft",
  "001727": "Congrats! You are the VIP member of {0}",
  "001728": "{0}DAYS{1}HRS{2}MINS{3}SECS",
  "001729": "Upgrade to {0}Apowersoft Unlimited{1} at Only $19.95/year",
  "001730":
    "Check our {0}order FAQ{1} for more information about order issues.",
  "001731":
    "Find your answer in quick assistance. Apowersoft {0}Support Center{1} is here to help you out.",
  "001732":
    "Contact us if you have any other questions: {0}contact Support team{1}. Apowersoft Support Team will try their best to get back to you within 24 hours during workdays.",
  "001754": "If you do not bind your Email, there is a risk of account loss.",
  "001755":
    "If you do not bind your phone number, there is a risk of account loss.",
  "001756": "Please input the valid Email address",
  "001757": "Verification code sent successfully!",
  "001758": "Failed to send verification code",
  "001759": "Please wait for a while!",
  "001760": "Failed to bind Email",
  "001761": "Email has been bound!",
  "001762":
    "This email has been bound to another account, please change a new one.",
  "001763": "This Email is already registered, please choose another one.",
  "001764": "Please input the valid Email address",
  "001765": "Please input valid phone number!",
  "001766": "Please choose country and region",
  "001767": "Phone number has been bound!",
  "001768":
    "This phone number has been bound to another account, please change a new one.",
  "001769":
    "This phone number has been used to sign up, please use another phone number or login.",
  "001770": "Failed to bind phone number!",
  "001771": "Successfully Registered!",
  "001772": "Your signup failed, please try again!",
  "001773": "This phone number is already registered, please login.",
  "001774": "The password length should not be less than 6 digits!",
  "001775": "Failed to change password!",
  "001776": "No data found",
  "001777": "Tel",
  "001778": "Change",
  "001779":
    "Apowersoft will maintain ongoing communications with you after subscription",
  "001780": "Logout successfully",
  "001781": "Unlimited VIP Exclusive Privileges",
  "001782": "Please wait for a while",
  "001783": "Next",
  "001784": "Find Password",
  "001785": "Find by Email",
  "001786": "Find by Phone",
  "001787": "Verification code has been sent.",
  "001788": "10 seconds left to resend the code",
  "001789": "Resend",
  "001790": "Term of validity",
  "001791": "Download now",
  "001792": "Order now",
  "001793": "Failed to get your order",
  "001794": "The request timeout, please refresh and try again",
  "001795": "Phone",
  "001796": "Repeat the password",
  "001797": "Two passwords do not match.",
  "001798": "Change",
  "001799": "You've logged out the account.",
  "001800": "Get premium of Unlimited membership",
  "001801": "VIP Privilege",
  "001802": "Enjoy the rights of free use for all Apowersoft online products",
  "001803": "Enjoy the rights of free use for Apowersoft Unlimited",
  "001804": "5 GB of space for secure storage and easy sharing service",
  "001805": "Enjoy 50% discount for all Apowersoft Products and services",
  "001806": "Member Benefits",
  "001807": "Get Unlimited VIP to enjoy 20% discount for OCR service",
  "001808": "Get Unlimited VIP to enjoy 20% discount for LightMV",
  "001809": "Get Unlimited VIP to enjoy 20% discount for LightPDF",
  "001810": "Get VIP",
  "001811": "Submit ticket",
  "001812": "My Tickets",
  "001813": "Failed to get tickets",
  "001814": "The request timeout, please refresh and try again.",
  "001815": "Successfully changed password! Logging out!",
  "001845": "Welcome! Respectful Unlimited VIP member",
  "001846": "Check",
  "001847": "Accept Membership Terms of Service",
  "001848": "Pay Now",
  "001860": "Unlimited VIP has exclusive {0}FOUR PRIVILEGES, Join Now>>{1}",
  "001942": "Login with other accounts",
  "001943": "Extend License Activation",
  "002030":
    "The phone number has been already registered, please use another valid number or {0}Login{1}.",
  "002031":
    "The Email has been already registered, please use another valid Email or {0}Login{1}",
  "002032": "The account has been registered",
  "002041": "Thank you!",
  "002042": "Thanks for your payment!",
  "002043": "Your ID",
  "002044": "Initial password",
  "002045":
    "The order is processing, we will deliver the account information to {0}, please check your mailbox (Spam/junk folder included)",
  "002046": "Order No.{0}",
  "002132": "Checking data",
  "002133": "Retrieve VIP",
  "002134": "Help",
  "002374": "Basic",
  "002375": "Plus",
  "002376": "Pro",
  "002377": "Premuim",
  "002378": "Activate Code",
  "002424": "Purchase Lollies",
  "002426": "Lollies",
  "002428": "OK",
  "002429": "Notice:",
  "002430":
    "1. Lollies, the virtual money, can only be used to purchase services on LightMV. Lollies are valid permanently, but are non-refundable, non-withdrawable, and non-transferable.",
  "002431": "2. You can request a refund when website crashes.",
  "002432": "{0} Lollies：{1} {2}{3}{4}",
  "002433": "Insufficient Lollies, please purchase more first",
  "002435": "Accept {0} Purchase Agreement {1}",
  "002436": "Free offer {0} Lollies",
  "002437": "Invalid product ID",
  "002441": "Order Refunded",
  "002442": "Order Closed",
  "002443": "My Invoice",
  "002444": "Account status:",
  "002445": "Production priority:",
  "002446": "Add",
  "002447": "Maximum number of files for Flexible Template:",
  "002448": "Increase",
  "002449": "Maximum number of saved videos:",
  "002450": "Maximum simultaneous production tasks:",
  "002451": "Consumption record",
  "002452": "Purchase record",
  "002453": "Date",
  "002454": "Details",
  "002455": "Consume Lollies",
  "002456": "No consumption record",
  "002457": "Order number",
  "002458": "Payment method",
  "002459": "Amount",
  "002461": "No purchase record",
  "002462": "Increase Now",
  "002463": "Your video will be produced with priority after you purchase it!",
  "002464": "This item can be accumulated!",
  "002465": "You can save more videos after you purchase it!",
  "002466":
    "You can produce multiple videos simultaneously after you purchase it!",
  "002467": "This item cannot be accumulated!",
  "002468": "Purchase {0} video(s) to archive",
  "002469": "Purchase {0} time(s) for production priority",
  "002470": "Purchase {0} simultaneous production tasks",
  "002471": "Download video: {0}",
  "002472": "{0} Lollies：{1} {2}{3}{4}{5}{6}",
  "002473": "Insufficient Lollies! Please purchase more first!",
  "002474": "Purchase successful",
  "002475": "Purchase failed",
  "002476": "Production Priority",
  "002477": "Number of saved videos",
  "002478": "Maximum Simultaneous Production Tasks",
  "002479": "You lack {0} Lollies. Please purchase more Lollies first!",
  "002485": "Templates",
  "002501": "Special offer",
  "002502": "Click to buy",
  "002504": "Account Details",
  "002505": "No free offer",
  "002553": "The system is being upgraded, please try again later.",
  "002554":
    "1. Lollies is the virtual money. It can be used to download videos and to increase the number of files uploaded to a template.",
  "002555":
    "2. Lollies are non-refundable, non-withdrawable, and non-transferable.",
  "002556": "3. You can request a refund for Lollies when website crashes.",
  "002558": "Remaining times",
  "002559": "Increase",
  "002564": "Redemption code",
  "002565": "Please input your redemption code",
  "002566": "Redeem successfully!",
  "002567": "Invalid code, please check and input again.",
  "002568": "You have used this redemption code.",
  "002569": "{0} has been registered or binded.",
  "002570": "If you need to re-bind account, please delete the old account.",
  "002571": "Your previous account will become invalid after deletion.",
  "002572": "Delete",
  "002573": "Check the verification code too many times",
  "002600": "Auto-Renewal",
  "002601":
    "You can keep using the subscription with the auto renewal option enabled.",
  "002602": "Cancel Auto-Renewal",
  "002603": "Are you sure you want to cancel the auto-renewal?",
  "002604":
    "If you cancel the auto-renewal, you will lose access to the subscription.",
  "002605": "Turn off",
  "002606": "Not Now",
  "002607": "Your subscription order has been cancelled successfully!",
  "002608": "Sorry, failed to cancel the subscription!",
  "002609": "Try again",
  "group name": "Group Name",
  "number of people": "Members",
  "Come and create your group~": "Create your own group to share videos now!",
  "new group": "Add new group",
  "My group": "My Groups",
  "search group": "Search group",
  "group": "Group",
  "Created group successfully": "Add group successfully!",
  "add member": "Add member",
  "delete group": "Delete",
  "group created": "My Group",
  "group joined": "Joined Group",
  "Are you sure to delete the group?": "Do you want to delete this group?",
  "Are you sure to remove the member?": "Do you want to remove this member from group?",
  "Are you sure to cancel sharing?": "Do you want to cancel group sharing?",
  "deleted group successfully": "Group deleted successfully!",
  "Link Invitation": "Invite link",
  "Account Invitation": "Invite account",
  "Join through the link, which needs to be approved and confirmed": "Require verification before joining the group.",
  "Please enter your email or mobile number": "Please enter account email or phone number",
  "Add member successfully": "Add member successfully",
  "Exit group": "Exit group",
  "Group setting": "Settings",
  "Exit successfully": "Exit group successfully",
  "Change avatar": "Edit avatar",
  "inviting you to join the reccloud group": "Inviting you to join RecCloud group",
  "members in total": "All {0} members",
  "Join the group now": "Join now",
  "Join successfully": "Joined successfully",
  "Apply successfully": "Applied successfully",
  "The audit function is enabled for the group, and it will be automatically added to the group after passing the audit.": "Please apply to join the group. You'll enter the group once the request is approved by the group owner.",
  "Apply to join": "Apply now",
  "Applied, group leader is reviewing": "Apply submitted. It's now pending...",
  "Member management": "Members",
  "Video management": "Space Management",
  "Search Member": "Search members",
  "Member name": "Name",
  "role": "Role",
  "Joining date": "Joined Date",
  "Viewers": "Viewer",
  "agree": "Approve",
  "disagree": "Reject",
  "Remove successfully": "Removed successfully",
  "Rejected to join": "Reject successfully",
  "Cancel sharing": "Cancel sharing",
  "Cancel succeeded": "Canceled successfully",
  "The video list managed by group is empty": "The file list of group management is empty",
  "Group is being approved": "Pending...",
  "002746": "Add file from cloud",
  'speechToText': "Speech to Text",
  'easyEdit': 'Edit Video in Simple Steps',
  'easyDesc': 'Everyone can create great videos in minutes',
  'selectEditFunc': 'Select the function that you\'ll use',
  'uploadDesc': 'Click "Upload" to add video footages',
  'editVideos': 'Render Video',
  'editVideosDesc': 'Get video in high quality with several simple clicks',
  "onlineSpeechToText": "AI Speech to Text",
  'onlySupport1h': 'This feature supports audio/video file under 1hr currently.',
  'convertLang': 'Convert to',
  'convertFormat': 'Format',
  'configDelVideo': 'Do you want to delete this audio/video?',
  'selectLangs': 'Please choose the language you want to convert to.',
  'downloadFile': 'Download File',
  'selectAll': 'Select All',
  'convertDefLang': 'Original',
  'onlyVip': 'Please upgrade to VIP and enjoy more advanced features.',
  'convertNow': 'Generate',
  'importMediaFile': 'Upload media file',
  'desc1': 'Select the files and upload to the web.',
  'desc2': 'Select the language of your media file.',
  'saveTxt': 'Save texts',
  'desc3': 'Automatically generate text file in TXT format.',
  'English': 'English',
  'Chinese': 'Chinese',
  'Japanese': 'Japanese',
  'French': 'French',
  'Taiwan': 'Traditional Chinese',
  'German': 'German',
  'Portuguese': 'Portuguese',
  'Spanish': 'Spanish',
  'Afrikaans': 'Afrikaans',
  'Amharic': 'Amharic',
  'Arabic': 'Arabic',
  'Assamese': 'Assamese',
  'Azerbaijani': 'Azerbaijani',
  'Bashkir': 'Bashkir',
  'Belarusian': 'Belarusian',
  'Bulgarian': 'Bulgarian',
  'Bengali': 'Bengali',
  'Tibetan': 'Tibetan',
  'Breton': 'Breton',
  'Bosnian': 'Bosnian',
  'Catalan': 'Catalan',
  'Czech': 'Czech',
  'Welsh': 'Welsh',
  'Danish': 'Danish',
  'Greek': 'Greek',
  'Estonian': 'Estonian',
  'Basque': 'Basque',
  'Persian': 'Persian',
  'Finnish': 'Finnish',
  'Faroese': 'Faroese',
  'Galician': 'Galician',
  'Gujarati': 'Gujarati',
  'Hausa': 'Hausa',
  'Hawaiian': 'Hawaiian',
  'Hebrew': 'Hebrew',
  'Hindi': 'Hindi',
  'Croatian': 'Croatian',
  "Haitian Creole": 'Haitian Creole',
  'Hungarian': 'Hungarian',
  'Armenian': 'Armenian',
  'Indonesian': 'Indonesian',
  'Icelandic': 'Icelandic',
  'Italian': 'Italian',
  'Georgian': 'Georgian',
  'Kazakh': 'Kazakh',
  'Khmer': 'Khmer',
  'Kannada': 'Kannada',
  'Korean': 'Korean',
  'Latin': 'Latin',
  'Luxembourgish': 'Luxembourgish',
  'Lingala': 'Lingala',
  'Lao': 'Lao',
  'Lithuanian': 'Lithuanian',
  'Latvian': 'Latvian',
  'Malagasy': 'Malagasy',
  'Maori': 'Maori',
  'Macedonian': 'Macedonian',
  'Malayalam': 'Malayalam',
  'Mongolian': 'Mongolian',
  'Marathi': 'Marathi',
  'Malay': 'Malay',
  'Maltese': 'Maltese',
  'Burmese': 'Burmese',
  'Nepali': 'Nepali',
  'Dutch': 'Dutch',
  'Norwegian Nynorsk': 'Norwegian Nynorsk',
  'Norwegian': 'Norwegian',
  'Occitan': 'Occitan',
  'Punjabi': 'Punjabi',
  'Polish': 'Polish',
  'Pashto': 'Pashto',
  'Romanian': 'Romanian',
  'Russian': 'Russian',
  "Saudi Arabia": 'Saudi Arabia',
  'Sindhi': 'Sindhi',
  'Sinhala': 'Sinhala',
  'Slovak': 'Slovak',
  'Slovenian': 'Slovenian',
  'Shona': 'Shona',
  'Somali': 'Somali',
  'Albanian': 'Albanian',
  'Serbia': 'Serbia',
  'Sundanese': 'Sundanese',
  'Sweden': 'Sweden',
  'Tamil': 'Tamil',
  'Telugu': 'Telugu',
  'Tajik': 'Tajik',
  'Thailand': 'Thailand',
  'Turkmenistan': 'Turkmenistan',
  'Tagalog': 'Tagalog',
  'Turkey': 'Turkey',
  'Tatar': 'Tatar',
  'Ukraine': 'Ukraine',
  'Urdu': 'Urdu',
  'Uzbekistan': 'Uzbekistan',
  'Vietnam': 'Vietnam',
  'Yiddish': 'Yiddish',
  'Yoruba': 'Yoruba',
  'upgradeToYear': 'Monthly VIP member can transfer the media file into %d languages, while yearly VIP member can choose all the languages.',
  "MergeVideo": "Merge Video",
  "VideoToGif": "Video to GIF",
  "ChangeVideoSpeed": "Change Video Speed",
  'ExtractAudio': 'AI Vocal Remover',
  "003100": "Subtitles not yet generated",
  "003101": "Subtitles",
  "003102": "AI-powered subtitles",
  "003103": "AI translation",
  "003104": "Bilingual subtitles",
  "003105": "Search",
  "003107": "Extracting subtitles...",
  "003108": "You have unsaved changes. Are you sure you want to exit?",
  '003109': 'This feature supports audio/video file under 2hr currently.',
  "003110": "Turn on the subtitles",
  "003111": "Turn off the subtitles",
  "003112": "AI-powered subtitles",
  "ptv": "Image to Video",
  "aiSubtitle": "AI Subtitle",
  "durationLimitNoVip": "3-minute free trial only, please upgrade to VIP.",
  "downloadSubtitle": "Export subtitles",
  "toSubtitle": "Auto subtitles",
  "advancedEditTip": "Supports multiple formats & provides more stable, efficient operation!",
  "003118": "Remaining time for AI speech & text conversion:",
  "003119": "(Each word usually takes about 0.36s)",
  "003120": "Extra 10hrs/month for Monthly VIP",
  "003121": "Extra 25hrs/month for Yearly VIP",
  "003124": "hrs",
  "003125": "mins",
  "003126": "secs",
  "003128": "Extra Pro editor VIP",
  "003129": "Extra Advanced recorder VIP",
  "003130": "Extra 10hrs AI speech & text per month ",
  "003131": "Extra 50hrs AI speech & text per month ",
  "003132": "Support multiple-screen recording, customized recording, auto-stop and exclude window recording, etc.",
  "003200": "Satisfied with our app? Rate us!",
  "003133": "Trial version supports video file under 100mb, please upgrade to VIP for more.",
  "apiTitle": "RecCloud Video Creating & Editing API",
  "apiDesc": "RecCloud provides professional video recording and editing API services. Standard and detailed API documentation is provided for convenient integration. RecCloud API can empower your application with various video functions, including the world's leading AI functions like <span>AI voice-to-text</span>, <span>AI subtitle recognition</span>, <span>AI translation</span>, and powerful <strong>video recording and editing features, such as video cutting, merging, converting, screen cropping, rotating, audio extraction, muting</strong> and more. Therefore, it can meet the diverse needs of different scenarios such as online courses, live streaming, meetings and games.",
  "apiDocBtn": "Get Free Key",
  "apiContactBtn": "Contact Expert",
  "key11": "Cooperation",
  "key12": "Partners",
  "key21": "Function",
  "key22": "Lists",
  "key31": "Video",
  "key32": "Demos",
  "tryNow": "Start Now",
  "key41": "Usage ",
  "key42": "Scenarios",
  "videoEdit": "Video Editing",
  "audioEdit": "Audio Conversion",
  "videoConversion": "Video Conversion",
  "aiDubbing": "AI Dubbing",
  "key51": "Our",
  "key52": "Advantages",
  "aIRecognition": "AI Recognition",
  "aIRecognitionDesc": "Through advanced model optimization such as adaptive speech enhancement, multi-speaker separation and speech emotion recognition, it helps AI achieve anthropomorphic thinking so that AI can eliminate useless tone words and repetitive phrases on its own, thus greatly improving conversion accuracy.",
  "SimpleFuncs": "Simple and efficient functions",
  "simpleFuncsDesc": "RecCloud API covers basic yet comprehensive video recording and editing functions with simple operations. It offers proven solutions, standardized documentation and full technical support, making it easy and efficient for developers to access.",
  "goodExpandability": "Good expandability",
  "goodExpandabilityDesc": "With good scalability and stability, it can meet the needs of multi-formats, high concurrency and large-scale video editing, providing strong support for the digital transformation and upgrading of the audio and video industry.",
  "key61": "Contact",
  "key62": "Us",
  "APIInterface": "API Interface",
  "APIInterfaceDesc": "No additional development! One click to call the interface! Integrate video recording & editing functions easily!",
  "freeUse": "Check Documenation",
  "freeUseNum": "Get 50 credits for free",
  "businessUse": "Business Inquiry",
  "businessUseDesc": "One-on-one customer service",
  "contactBusiness": "Submit Ticket",
  "getApiKey": "Get API KEY",
  "reccloudApiKey": "RecCloud API KEY",
  "apiKeyNum": "(used/remaining)",
  "devService": "For Developers (API)",
  "itemDesc1": "Easily cut video and remove redundant clips",
  "itemDesc2": "Freely crop video frame size",
  "itemDesc3": "Merge multiple video clips with one click",
  "itemDesc4": "Convert video to GIF with one click",
  "itemDesc5": "Split vocal and instrumental",
  "itemDesc6": "Intelligently convert audio to text with AI",
  "itemDesc7": "Generate and translate subtitles with AI",
  "itemDesc8": "Adjust speed simply",
  "newVideo": "New File",
  "becomeVip": "Become VIP",
  "only3Video": "Non-VIP users can only use 3 times, please upgrade VIP to extract more. ",
  "chatVideoPlaceholder": "You can ask me any questions about the file",
  "summary": "Summary",
  "extracting": "Extracting...",
  "readAll": "Read full article",
  "scanQRAI": "Scan the QR code to join AI communication group",
  "regenerate": "Regenerate",
  "confirmDelConversation": "Are you sure you want to permanently delete the chat?",
  "contentDetail": "Content",
  "subtitleList": "Subtitle",
  "aiTimeoutFail": "Request timed out, generation failed",
  "aiLoading": "Session is being generated, please operate later",
  "analysisFailed": "Analysis failed, please try again!",
  "003135": "We promise a full refund within 7 days if not satisfied (within 3 days for monthly members). Please refer to the <strong class='refund-tag'>refund policy</strong> for details.",
  "003136": "Summarize, AI talk to video and audio",
  "onlySupport30s2h": "Only supports files from 30 seconds to 2 hours, please choose again!",
  "uploadingTryLater": "Uploading, please try again later!",
  "noSupportFormat": "The file is not supported, Please choose again!",
  "noFileConversation": "This file has been deleted by the original publisher. Do you want to remove this record?",
  "featList": "Functions",
  "tools": "Tools",
  "mainKeyword": "<span class=\'text-primary-color\'>AI-Powered</span>",
  "mainTitle": "Multimedia Service Platform",
  "mainDesc": "Integrates AI video chat, AI subtitles, screen recording, editing, GIF/audio conversion, as well as cloud storage and sharing.",
  "anyVideo": "Edit Online",
  "handleFile": "Versatile Video Processing",
  "specialist": "Specialist",
  "handleFileItemDesc1": "Click-to-record, no download needed",
  "handleFileItemDesc2": "AI summarize and chat",
  "handleFileItemDesc3": "Auto generate subtitle",
  "handleFileItemDesc4": "Seamless video trimming",
  "handleFileItemDesc5": "Universal aspect ratios",
  "handleFileItemDesc6": "Intelligently convert voice to text",
  "handleFileItemDesc7": "Combine clips quickly",
  "moreTools": "More Tools",
  "handleFileItemDesc8": "Discover more other features",
  "why": "Why",
  "chooseRecCloud": "Choose RecCloud",
  "whyTitle1": "AI-Powered",
  "whyDesc1": "Efficiency-enhancing, intelligent processing\nComprehensive AI for video creation capabilities",
  "whyTitle2": "User-friendly",
  "whyDesc2": "No download required, use online\nIntuitive interface that is easy to navigate",
  "whyTitle3": "Secure and Convenient",
  "whyDesc3": "Cloud storage allows for one-click sharing\nGuaranteed data protection, rest assured",
  "whyItem1": "Education",
  "whyItemDesc1": "ChatVideo boosts efficiency by extracting key information from video",
  "whyItem2": "Game",
  "whyItemDesc2": "Provides audio and screen recording, ideal for game capture, commentary",
  "whyItem3": "Finance",
  "whyItemDesc3": "Supports multi-screen recording for financial report analysis and sharing",
  "whyItem4": "Medical",
  "whyItemDesc4": "Enables private groups for medical seminars and training",
  "forDev": ": Empowering Developers!",
  "forDevDesc": "RecCloud provides professional recording and editing API services, supplemented with thorough API documentation and easy-to-follow integration processes. Our services include AI video chat, generating subtitles with AI video creation software, and speech-to-text conversion. Additionally, we offer video processing features like recording, editing, merging, converting, cropping, etc. These services can be used in various fields including online education, live streaming, conferences, game and so on.",
  "userAreaTitle": "Hear from Our",
  "userAreaTitleLight": "Million+ Satisfied Users!",
  "userComment1": "I am delighted to have found RecCloud, a free and user-friendly screen recording tool. Whether I\'m taking online classes or sharing my live gaming experiences, RecCloud does the job well and really impresses me. It is the best screen recording software I have ever used.",
  "userComment2": "It has an intutitive interface and many useful online video editing functions, such as trimming, cropping, mering, etc. And the processing speed is fast, which saves me a  amount of time.",
  "userComment3": "Initially, I just wanted to use it to quickly create some videos, but I was pleasantly surprised by its powerful AI video chat feature, which allows me to effortlessly comprehend the key points of the video content. In addition, the AI subtitle translation feature makes my videos look more professional. I will definitely recommend its AI tools for video creation to my friends.",
  "userComment4": "RecCloud allows me to share videos on social media with just a click. It also provides a range of storage options, enabling me to decide whether to save locally or upload to the cloud, which allows me to access my videos across various devices. ",
  "forUserDesc": "Boost Your Video Creation with RecCloud\'s All-In-One Solutions!",
  "videoManageDesc1": "Store, share anytime!",
  "videoManageDesc2": "Manage, share privately!",
  "freeOnlineRecorder": "Free Online Recorder",
  "videoManageDesc3": "Record, no download!",
  "videoManageDesc4": "Upload, store easily!",
  "aIProcessVideo": "AI Process Video & Audio",
  "speechToTextAi": "Smart voice-to-text",
  "freeProcessVideo": "Process Video & Audio Free",
  "videoToGifDesc": "Instant video-to-GIF",
  "downloadClient": "Desktop Version",
  "advancedFeatDesc": "Pro screen recorder",
  "advancedVideoDesc": "Pro video editor",
  "multiScreenRecorder": "Multi-Screen Recorder",
  "advancedRecorderDesc": "Pro multi-screen recorder",
  "aiChatVideo": "AI Video/Audio Summarizer",
  "aiChatIngent": "AI Chat",
  "aiChatDoc": "AI Docs Chat",
  "aiGitmind": "AI Mind Map",
  "aiNavTool": "AI Tools",
  "aiImgProcess": "AI Photo Editor",
  "003113": "Our <span>Chrome extension</span> is now available",
  "003114": "Add to Chrome",
  "003115": "3-minute free trial only, please upgrade to VIP.",
  '003201': 'Please review us on the Chrome Store, this is very important to us, thank you for your support!',
  '003202': 'Go Review',
  "indexKey1": "Our",
  "indexKey2": "Partners",
  "advanceEditor": "Advanced Editor",
  "aiVideoTranslate": "AI Video Translator",
  "speakerGender": "Speaker",
  "female": "Female",
  "useFreeAPI": "Access to API",
  "translating": "Translating",
  "synthesizing": "Compositing",
  "limitMax1GB": "The maximum upload file size is 1GB.",
  "transVideo": "Translate video",
  "transVideoDesc": "Make a multilingual video with single clicks.",
  "aiVideoTranslateDesc": "Translate videos easily",
  "zh-CN": "Chinese (Mainland China)",
  "zh-HK": "Chinese (Hong Kong, China)",
  "zh-TW": "Chinese (Taiwan China)",
  "en-CA": "English (Canada)",
  "en-US": "English (US)",
  "en-GB": "English (UK)",
  "en-AU": "English (Australia)",
  "en-NZ": "English (New Zealand)",
  "en-IN": "English (India)",
  "fr-FR": "French (France)",
  "fr-CA": "French (Canada)",
  "fr-CH": "French (Switzerland)",
  "de-DE": "German (Germany)",
  "de-CH": "German (Switzerland)",
  "de-AT": "German (Austria)",
  "ja-JP": "Japanese (Japan)",
  "es-ES": "Spanish (Spain)",
  "es-MX": "Spanish (Mexico)",
  "es-AR": "Spanish (Argentina)",
  "es-CO": "Spanish (Colombia)",
  "pt-PT": "Portuguese (Portugal)",
  "pt-BR": "Portuguese (Brazil)",
  "all": "All",
  "male": "Male",
  "making": "Making",
  "saving": "Saving",
  "onlineLimit1Min": "1min recording time for non-vip",
  "startRecordingTitle": "Recording",
  "lightEditor": "RecCloud LightEditor",
  "onlineDownloadClient": "Download App Now",
  "downloadApp": "Download App",
  "standardModel": "Standard Model",
  "standardTitle": "Precision suitable for most everyday tasks",
  "standardSubtitle": "Available for free and premium users",
  "advancedModel": "Advanced Model",
  "advancedTitle": "Higher precision offering superior results",
  "advancedSubtitle": "Exclusively for premium users",
  "noVipOnlySupportLimit": "Non-VIPs can only convert the first # minutes of a file",
  "autoGenerateSubtitle": "Generate captions automatically",
  "selectTransSubtitle": "Subtitle your video in a new language",
  "subtitleSelectLangs": "Choose language",
  "selectLangsTitle": "Which language do you want to translate to?",
  "clearAndRetry": "Retry will erase the current language caption. Confirm regeneration?",
  "satisfactionEvaluation": "Satisfaction evaluation",
  "singeSubtitle": "Single language subtitles",
  "addLangs": "Add language",
  "shareDuration": "Remaining duration for sharing",
  "aiSpeechToTextDuration": "AI Speech to Text duration",
  "buyDuration": "Purchase duration",
  "safariNotSupport": "Safari does not support this page, please switch to Chrome browser to open.",
  "saveSuccess": "Save successful.",
  "lastSaveTime": "Recently saved",
  "aiSubtitleComplete": "Complete",
  "youCanToggleLangs": "You can choose to add or switch subtitle language and download subtitles.",
  "understand": "Understood",
  "aiSubtitleAutoSave": "Subtitle content will be saved automatically.",
  "free30Online": "Free Online",
  "aiTitleGener": " AI Subtitle Generator",
  "aiCaption": "Generate subtitles with one click for free, AI accurately translates among a variety of languages including Chinese, English, Japanese, German, and 99 other languages. ",
  "threeGener": "Generate Subtitles in Three Simple Steps",
  "uploadTitleFile": "Upload File",
  "uploadTitleFileDesc": "Upload any cloud or local file directly without external transfers.",
  "generTitleSub": "Generate Subtitles",
  "generTitleSubDesc": "Experience 99% accuracy with our AI Subtitle Generator for creating or translating subtitles.",
  "editTitleSub": "Edit Subtitles",
  "editTitleSubDesc": "Command your subtitles. Modify them to sync perfectly with your video.",
  "support99Lang": "Supports More than 99 Languages",
  "support99LangDesc": "Our free online subtitle generator, without watermark, can generate and translate subtitles in more than 99 languages. Localization has never been easier!",
  "baseNlpTitle": "AI Subtitle Generation",
  "baseNlpDesc": "Based on the latest NLP model, our software can accurately identify your video file and generate subtitles automatically with a 99% accuracy rate.",
  "popularSubtitle": "AI Subtitle Translation",
  "popularSubtitleDesc": "Not only can it automatically generate subtitles, but it can also translate them into various popular languages around the world. You can freely choose to add bilingual or original language subtitles, allowing your audience to understand your video content without barriers.",
  "searchEditSubtitle": "Subtitle Search and Editing Features",
  "searchEditSubtitleDesc": "Our intuitive search function allows you to easily locate specific subtitle content. Our free AI subtitle generator offers user-friendly editing features, including the addition or deletion of subtitles and adjustment of timestamps, among others. All changes are automatically saved, enabling real-time previews to ensure the video meets your expectations.",
  "whyChooseRec": "Why Choose RecCloud to Generate Subtitles?",
  "freeOnlineUse": "Free to use online",
  "freeOnlineUseDesc": "No need to download, generate and translate subtitles online.",
  "editTimestamp": "Timestamp Editing",
  "editTimestampDesc": "Flexibly control the start and end time of subtitles.",
  "shareFunction": "Sharing Functionality",
  "shareFunctionDesc": "Share link feature, share the finished video with one click. ",
  "quickLocation": "Quick Location",
  "quickLocationDesc": "Intelligent search can quickly match subtitles and voices. ",
  "easyToUseDesc": "Generate or translate subtitles with a few clicks.",
  "ConfidentialityTitle": "Confidentiality",
  "ConfidentialityTitleDesc": "We strictly protect all files to ensure your information remains confidential.",
  "millionsUsersTest": "RecCloud: ",
  "userComment5": "Easy to operate, convenient and fast, generate subtitles online without downloading or installing any software. This is a life-saver for computer novices!",
  "userComment6": "As a short video blogger, using the free automatic subtitle generator has significantly improved the efficiency of my video production. I don't have to stay up late editing videos anymore.",
  "userComment7": "I have tried and compared many subtitle software on the internet, and the recognition accuracy of this software is very high, which satisfies me.",
  "userComment8": "The AI subtitle translation function is very practical. In the past, I had to manually translate and add it to the video sentence by sentence. Now, with just a few clicks, I can create excellent bilingual subtitles. This is fantastic!",
  "IntelligentUseEasy": "AI Subtitle Generator: Intelligent & Easy-to-Use",
  "IntelligentUseEasyDesc": "Don't wait! Start creating accurate subtitles with RecCloud's free AI video caption generator today!",
  "video1.2Process": "Videos Processed",
  "customer370Happy": "Satisfied Customers",
  "Subtitle1Minute": "Generate 1-Minute Subtitles",
  "faster10Gener": "Generate Subtitles 10x Faster",
  "orDropFile": "Drop a File",
  "fasterReliable": "Faster and More Reliable",
  "ratedContinu": "Praised by Millions",
  "formalChina": "Chinese",
  "formalEnglish": "English",
  "moreMore": "And More...",
  "billionAdd": "M+",
  "millionAdd": "M+",
  "secondAdd": "Secs",
  "localOrCloud": "Cloud/Local",
  "downloadSubtitleVideoTitle": "Select subtitle language before download to merge with video.",
  "noSubtitleDownloadVideo": "No subtitles for this video yet. Try our one-click AI subtitle feature.",
  "mergeVideoWaiting": "Generating subtitled video, please wait.",
  "downloadSuccess": "Download successful",
  "noFreeDownloadCount": "Sorry, your free downloads has ran out, please upgrade to VIP.",
  "userComment5Name": "Julian Bennett",
  "userComment6Name": "Robert Smith ",
  "userComment7Name": "Evelyn Harper",
  "userComment8Name": "Oliver Armstrong",
  "originContent": "Original Content",
  "smartParagraphing": "Intelligent Paragraphing",
  "clickToSummary": "One-Click Summary",
  "copyAll": "Copy All",
  "smartParagraphingDesc": "Click to neatly structure the original text, enhancing the reading experience",
  "tryNowNew": "Try Now",
  "clickToSummaryDesc": "Summarize and present the central ideas of the content with one click.",
  "setTotalNum": "Set summary word count: #words",
  "setTotalNumDesc": "(10-2000 words range)",
  "chatVideoDesc": "Efficiently extract summaries, details, and subtitles from videos, answering all your queries about video/audio content.",
  "startChatVideoDesc": "Deeply analyzes your video/audio contents, presenting information in a user-friendly manner to enhance your convenience.   ",
  "boostEfficiency": "Boost Your Work Efficiency",
  "filterInformation": "Filter Crucial Information",
  "filterInformationDesc": "No matter the video - a meeting, lecture, movie, or game commentary, AI Video/Audio Summarizer can quickly summarize video to text. It intelligently understands your queries about the video and provides accurate answers.",
  "learnResearch": "Learning and Research",
  "learnResearchDesc": "AI Video/Audio Summarizer aids students, researchers, and professionals in intelligently capturing key video information, thereby enhancing learning and research productivity.",
  "smartChatbot": "Smart Video Chatbot",
  "smartChatbotDesc": "With the chatbot, you can hand over repetitive tasks to save more time for meaningful things, improving the quality of your life and work efficiency.",
  "chatGPTSummarize": "ChatGPT Summarize",
  "chatGPTSummarizeDesc": "This free online AI Video Summarizer can smartly summarize your videos with OpenAI GPT model, helping you to quickly understand the core content of the video.",
  "detailsSubtitle": "Details and Subtitles",
  "detailsSubtitleDesc": "Intelligently analyze videos, automatically presenting detailed information and subtitles, ensuring you don't miss any details.",
  "chatWithVideo": "Interact with Your Video",
  "chatWithVideoDesc": "Ask any questions related to your video, and the tool will efficiently retrieve useful information, getting you the answers you want effortlessly.",
  "analysisInterpretation": "Analysis & Interpretation",
  "analysisInterpretationDesc": "Automatically extract foreign movie subtitles for learning, extract key points from educational videos, or fetch important information from meeting videos.",
  "enhanceCapabilities": "Enhance Capabilities",
  "enhanceCapabilitiesDesc": "Quickly extract key information from videos, save time, increase efficiency, and expedite your learning and growth.",
  "inspireCreativity": "Inspire Creativity",
  "inspireCreativityDesc": "Ignite creativity through video content analysis. Support sharing and exchange information with others for co-learning, making both learning and working a delightful experience.",
  "frequentlyQuestions": "Frequently Asked Questions",
  "howUseChatVideo": "How to Use AI Video/Audio Summarizer Properly?",
  "howUseChatVideoAns": "Upload a video and ask your question clearly. GPT will provide precise answers. There's no need for you to understand the complex analysis process. Just focus on your question, and AI will handle the rest.",
  "howEnsuredSecurity": "How is the Security of Your File Ensured? ",
  "howEnsuredSecurityAns": "We strictly adhere to user information security regulations and do not save your uploaded files on our server. Your file security is 100% guaranteed as we respect and protect each user's privacy.",
  "whatPlatSupport": "What Platforms does RecCloud Support?",
  "whatPlatSupportAns": "RecCloud AI Video/Audio Summarizer is a free online application that operates via a browser. Whether you're using Windows or a Mac, as long as you have an internet connection, you can effortlessly use our service.",
  "startChatVideoTitle": "<span class=\'start-chatVideo chatVideo-feat-title-light\'>AI Summarize Video/Audio </span>Now!",
  "aiChatNeeds": "Best <span class=\'aiChat-needs chatVideo-feat-title-light\'>AI Video/Audio Summarizer</span>",
  "featChatVideoTitleLeft": "AI Video/Audio Summarizer ",
  "featChatVideoTitleRight": "with AI Chatbot",
  "featOnePlatLeft": "One-stop ",
  "featOnePlatCenter": "Multimedia Processing",
  "featOnePlatRight": "Platform",
  "purchaseAnnouncement": "RecCloud Purchase Adjustment Announcement",
  "recCloudMembers": "Dear RecCloud Members,",
  "recCloudMembersDesc": "We appreciate your support and aim to offer the best user experience. We\'re thrilled to announce an exciting update to our credits and membership benefits, giving you greater flexibility and cost-efficiency when using our AI features.",
  "creditsConversion": "1. Credits Conversion",
  "creditsConversionMain": "We understand you have been using our platform with purchased minutes. Don\'t worry, your remaining balance will still be valuable. We will convert your remaining minutes into credits as follows:",
  "creditsConversionShare": " ",
  "creditsConversionAiSubtitle": "AI voice subtitle minutes: Each minute converts to 1 credit.",
  "creditsConversionDesc": "These credits allow you to enjoy more services, such as AI subtitles, voice-to-text, video translation, and video downloads.",
  "membershipBenefits": "2. Membership Benefits Update",
  "membershipBenefitsDesc": "To improve your experience even more, we have updated our VIP membership benefits. When you purchase a VIP membership from us, you will receive extra credits as a bonus.",
  "announcementContactUs": "3. Contact Us",
  "announcementContactUsDesc": "If you have any questions about these updates, feel free to contact our customer support team. You can click the \"LiveChat\" button at the bottom of the page or reach out to us through submitting ticket <a href=\"https://reccloud.com/contact\" class=\"center-container-this\">（https://reccloud.com/contact）</a>. We\'ll be happy to assist you.",
  "announcementSorry": "Thank you for choosing RecCloud. We look forward to continuing to serve you better.",
  "bestRegards": "Best regards, ",
  "recCloudTeam": "The RecCloud Team",
  "announcementDate": "November 29rd, 2023",
  "announcementUnderstand": "I understand",
  "updateAnnouncement": "Announcement",
  "purchasePlans": "RecCloud Pricing Plans",
  "memberPurchase": "Membership",
  "creditsPurchase": "Pay As You Go",
  "businessCustomizationContact": "Business inquiries, please contact us",
  "bulkOrdersContact": "More credits, please contact us",
  "purchaseFreeAccount": "Free Account",
  "signUpTrial": "Try RecCloud for Free",
  "purchaseFreeRegistration": "Sign Up",
  "twoGBfreeCloud": "2GB free cloud space",
  "StoreFiveFilesFree": "Store 5 files for free",
  "tenFreeCredits": "10 free credits",
  "buyFreeThreeYears": "Buy 3 years, get 3 free",
  "LimitedTime300Credits": "Limited-time extra 300 credits",
  "MultiScreenVIPAccess": "Multi-Screen Recorder VIP access",
  "LightEditorVIPAccess": "LightEditor VIP access",
  "LifetimeValidityPurchase": "Lifetime validity after purchase",
  "creditsRedeemable": "Credits are redeemable for AI services, downloads, etc.",
  "activatedAfterPayment": "Membership and credits are instantly activated after payment.",
  "pricingDetails": "Compare <span class=\"text-primary-color\">Plans</span>",
  "purchaseFeatures": "Features",
  "purchaseMembershiBenefits": "Privileges",
  "purchaseFreeUsers": "Free",
  "purchaseMonthlyMembers": "Monthly Members",
  "purchaseAnnualMembers": "Annual Members",
  "purchaseStorageSpace": "Storage Space",
  "storageSpaceUnit": "xxGB",
  "purchaseNumberFiles": "File limit",
  "numberFilesUnit": "xx",
  "purchaseUnlimited": "Unlimited",
  "AudioVideoProcessing": "Audio/Video Edit",
  "processingM": "Files up to xx MB",
  "processingG": "Files up to xx GB",
  "purchaseVideoEditing": "Video editing: cut, crop, merge, <br>adjust speed, make GIFs, extract audio.",
  "bonusCredits": "Bonus Credits",
  "bonusCreditsUnit": "xx credits",
  "creditsDuringVip": "Gift credits are valid during the VIP period",
  "multiScreenDesktop": "Multi-Screen Recorder VIP",
  "vipCompatible": "VIP compatible",
  "multiScreenDesktopDesc": "A more professional and stable screen recorder",
  "lightEditorDesktop": "LightEditor VIP",
  "lightEditorDesktopDesc": "Offers simple audio/video editing functions, video conversion and compression.",
  "creditDeduction": "Pricing",
  "purchaseDownloadFiles": "Download files",
  "purchaseShareFiles": "Share Files",
  "purchaseMoreAiTools": "AI Subtitle<br>AI Speech-to-text<br>AI Summarize",
  "purchaseChatVideo": "ChatVideo",
  "intelligentParagraphing": "Auto Paragraph",
  "oneClickSummary": "AI Summary",
  "creditsEveryG": "xx credits/GB",
  "creditsEveryMint": "xx credit/minute",
  "creditsEveryMints": "xx credits/min",
  "creditsEveryTime": "xx credit/chat",
  "creditsEveryTimes": "xx credits/time",
  "refundPolicy": "Refund Policy",
  "refundPolicyDesc": "7-day money-back guarantee <a href='https://reccloud.com/refund-policy' class='text-primary-color'>Learn more</a>.",
  "securePayment": "Secure Payment",
  "securePaymentDesc": "You are safe and secure with us",
  "helpService": "Help Service",
  "helpServiceDesc": "Live chat and Tickets available <a href='https://reccloud.com/contact' class='text-primary-color'>Contact us</a>",
  "purchaseAskQuestion": "Frequently <span class=\"text-primary-color\">Asked Questions</span>",
  "pricingQaqQuestions1": "What Are Credits on RecCloud? ",
  "pricingQaqAnswer1": "<p>RecCloud has introduced \"credits\" as a new unit for using AI-related features on the platform. Here's a quick guide to understanding credits:</p><p class='mt-[12px]'>Earning Credits</p><p class='mt-[12px]'>By purchasing an annual or weekly RecCloud membership, you'll receive credits based on the membership type. Check the \"RecCloud Pricing Plans\" section for details.</p><p>If you need additional credits after becoming a member, you can buy more through the \"Upgrade Credits\" option.</p><p>Your credits balance updates in real-time upon purchase and can be checked in <a class='underline hover:text-primary-color' target='_blank' href='/account?subroute=my-account'>your account</a> on the RecCloud website.</p><p class='mt-[12px]'>Using Credits<p class='mt-[12px]'>Credits are deducted based on the features you use and the credit consumption rules.</p><p>For detailed information, refer to the \"Credits Deduction Rules\" in the membership benefits section.Examples include:</p><p class='mt-[12px]'>● AI Subtitles/Speech-to-Text: Free for the first minute, then 1 credit per minute. Files shorter than a minute still cost 1 credit.</p><p>● Text-to-Speech: Free for the first 200 characters, then 1 credit per 500 characters. Texts shorter than 500 characters still cost 1 credit.</p><p>● VIP users can download files up to 100MB for free. Exceeding 100MB or downloading files as a non-VIP user costs 6 credits per GB.</p><p class='mt-[12px]'>After completing a task, the system automatically deducts the appropriate number of credits from your account.</p><p class='mt-[12px]'>This flexible credit system allows you to top up as needed, enjoy personalized services, and unlock advanced features.</p>",
  "pricingQaqQuestions2": `Are Purchased Credits Permanent?`,
  "pricingQaqAnswer2": `If you buy credits through the "Upgrade Credits" option after becoming a RecCloud member, these credits are permanent and never expire.<br/>However, credits included with RecCloud membership plans are only valid during the membership period. Once your membership expires, these bonus credits will also expire and can no longer be used.`,
  "pricingQaqQuestions3": `Is Membership Universal?`,
  "pricingQaqAnswer3": `When you purchase a RecCloud membership, your VIP account can be used across all platforms—mobile, tablet, and desktop—unlocking all corresponding VIP features.`,
  "pricingQaqQuestions4": `Data Privacy and Security`,
  "pricingQaqAnswer4": `RecCloud guarantees a virus-free and ad-free experience. The platform employs data encryption, access control, and identity authentication technologies to ensure the utmost security of your files and data, with no risk of leaks.<br/>If you encounter any suspected virus issues, it is likely a false alarm caused by external factors. Please report the issue immediately through the official contact page <a href='https://reccloud.com/contact' class='text-primary-color'>(https://reccloud.com/contact)</a>. The RecCloud team will respond swiftly to resolve the problem for you.`,
  "pricingQaqQuestions5": "How Can I Obtain an Invoice? ",
  "pricingQaqAnswer5": "After making a purchase, you can directly obtain an electronic invoice from the payment platform. If you encounter any problems in acquiring the invoice, please feel free to contact us by submitting a support ticket <a href='https://reccloud.com/contact' class='text-primary-color'>(https://reccloud.com/contact)</a>. We will ensure to send it to you as soon as possible.",
  "pricingQaqQuestions6": "How Can I Request a Refund? ",
  "pricingQaqAnswer6": "If you\'ve successfully subscribed but need a refund due to special circumstances, we welcome your refund request. The refund amount will be calculated according to our refund policy <a href='https://reccloud.com/refund-policy' class='text-primary-color'>(https://reccloud.com/refund-policy).</a>",
  "purchaseContactUs": "If you require customized software or bulk purchasing, please contact our business manager.",
  "downloadFilesCredits": "Downloading files will consume credits based on size (xx credits per GB), would you like to continue?",
  "remainingCredits": "Remaining credits: ",
  "accountCredits": "Credits: ",
  "freeUse30Mint": "Free use of source language for the first 30 minutes.",
  "preMinutesCredits": "Credits will be consumed based on duration xx credit per minute).",
  "aiSubtitleCredits": "The AI Subtitle will consume credits based on duration (xx credit per minute), would you like to continue?",
  "notShowAgain": "Do not show this again",
  "creditsContinue": "Continue",
  "aiChatCredits": "The {ChatVideo} will consume {xx} credit, would you like to continue?",
  "creditsNotEnough": "Insufficient credits, deduction failed, please purchase more credits before trying again.",
  "aITranslationCostCredits": "AI Translation will consume credits based on duration (xx credits/min). Do you wish to continue? ",
  "aISpeechtoTextCostCredits": "AI Speech-to-Text will consume credits based on duration (xx credits/min). Do you wish to continue?",
  "aIntelligentParagraphCostCredits": "AI Intelligent Paragraphing will consume xx credits. Do you wish to continue?",
  "aISummarizeCostCredits": "AI Summarize will consume xx credits. Do you wish to continue?",
  "chatVideoCostCredits": "ChatVideo will consume credits based on duration (xx credits/min). Do you wish to continue?",
  "aIDialogueCostCredits": "AI Dialogue will consume xx credits per use. Do you wish to continue?",
  "purchaseCredits": "Purchase credits",
  "try30min": '30-minute trial',
  "speechFreeOnlineTitle": "Free Online",
  "speechFreeOnlineTitleMobile": "Free ",
  "speechToTextTitle": " AI Speech to Text",
  "speechFreeOnlineDesc": `Transcribe audio and video to text instantly with AI. Get smart summaries and translations for all your needs!`,
  "Avideo1.3File": "Audio/Video File Processed",
  "speechToTextCustomers": "Satisfied Customers",
  "speech99Lang": "Support 99+ languages",
  "speech10Seconds": "Turn 1min speech to text",
  "SpeechToText3Steps": "Speech-to-Text in 3 Steps",
  "speechUploadFile": "Upload File",
  "speechUploadFileDesc": "Select to upload files from cloud or local disk.",
  "speechStartConver": "Start the Conversion",
  "speechStartConverDesc": "Click \"Transcribe\" to turn speech to text.",
  "speechOneClick": "One-click Copy",
  "speechOneClickDesc": "Copy the transcribed text in one click.",
  "speechSupportLang": "Support Multiple Languages",
  "speechSupportLangDesc": "Leveraging advanced speech recognition AI technology, we accurately recognize and process multiple international mainstream languages, delivering an unparalleled free AI speech-to-text experience.",
  "speechParagraphSummary": "AI Paragraph and Summary",
  "speechParagraphSummaryDesc": "With RecCloud, not only can you transcribe speech to text, but you can also effortlessly automate the paraphrasing and summarization of your converted text, further optimizing your time and streamlining your workflow for improved efficiency.",
  "speechParagraphSummaryDescMobile": " ",
  "speechPrivacy": "Data Privacy Guaranteed",
  "speechPrivacyDesc": "During transcription, we enforce strict encryption for data transmission and storage, ensuring your files and information are never shared with third parties. We regularly update our encryption measures to keep your information security our top priority.",
  "speechPrivacyDescMobile": " ",
  "SpeechTextAnywhere": "Speech-to-Text Anywhere, Anytime",
  "speechMeetingSummary": "Meeting Summary",
  "speechBroadcastScript": "Broadcast Script",
  "speechVideoText": "Video to Text",
  "speechAudioText": "Audio to Text",
  "speechInterviewScript": "Interview Script",
  "speechReccloudMillons": "Speech-to-Text on RecCloud",
  "speechReccloudLove": "<span class='mr-2'>People Love</span>",
  "userComment9": "I often need to handle various project video and audio files, and this free online voice-to-text tool helped me save a lot of time. Its recognition accuracy is high, and it can also be converted into many languages, greatly improving my efficiency.",
  "userComment9Name": "Tessa Fox",
  "userComment10": "The operation is quite simple, just a few clicks and the audio-to-text conversion is done. The speed is also fast, which has really improved my usual speed of organizing materials.",
  "userComment10Name": "Hector S.",
  "userComment11": "I have tried many free AI speech-to-text tools, but RecCloud\'s recognition accuracy is even higher, making it convenient and effortless to use. It can also handle multiple languages and is very user-friendly!",
  "userComment11Name": "Mariah Rocha",
  "userComment12": "This free audio to text converter is really convenient, guys. The one-click copy text feature is also very useful, and the efficiency is undeniable.",
  "userComment12Name": "Kaitlyn Osborne",
  "readyForSpeech": "Now you are ready for AI Speech to Text!",
  "readyForSpeechDesc": "Instantly transcribe audio and video into text, with a single click to replicate the whole content.",
  "aiSubtitleStyle": "Style",
  "aiSubtitleFontStyle": "Font",
  "aiSubtitleFontColorStyle": "Color",
  "aiSubtitleBgColorStyle": "Background",
  "aiSubtitleAlignmentStyle": "Alignment",
  "aiSubtitleVerticalStyle": "Vertical",
  "aiSubtitleDragDesc": "Drag to move text in the left screen",
  "aiSubtitleColorMore": "More",
  "aiSubtitleStyleSave": "Save",
  "aiSubtitleStyleCancel": "Cancel",
  "aiSubtitleStyleDefault": "Default",
  "aiSubtitleStyleDefaultFont": "Default Font",
  "aiSubtitleStyleSongTi": "SongTi",
  "aiSubtitleStyleSimHei": "SimHei",
  "aiSubtitleStyleSimKai": "SimKai",
  "aiSubtitleStyleLishu": "Lishu",
  "aiSubtitleStyleYaHei": "Microsoft YaHei",
  "aiSubtitleStyleSourceHan": "SourceHans",
  "aiSubtitleStyleBold": "Bold",
  "aiSubtitleStyleItalic": "Italic",
  "aiSubtitleStyleUnderline": "Underline",
  "aiSubtitleStyleLeftAlign": "Left align",
  "aiSubtitleStyleCenterAlign": "Center align",
  "aiSubtitleStyleRightAlign": "Right align",
  "chatVideoCopy": "Copy",
  "chatVideoFileAgain": "File information is insufficient, please reselect the file!",
  "notSupportExt": "Sorry, this format is not currently supported",
  "aiTextToSpeech": "All-in-One AI Voice Generator",
  "aiTextToSpeechDesc": "Smart text to voice",
  "aiTextToSpeechPlaceholder": "Enter your text here or click below to upload the file",
  "uploadFile": "Upload file",
  "voiceType": "Voice Type",
  "voiceTypeMale": "Male",
  "voiceTypeFeMale": "Female",
  "aiTextToSpeechLangs": "Language",
  "aiTextToSpeechTryDuration": "Test # secs",
  "aiTextToSpeechWillReplace": "Conversion will overwrite existing audio. Please ensure to save your audio before proceeding.",
  "aiTextToSpeechUsePointTitle": "All-in-One AI Voice Generator will consume credits based on characters (# character/credit). Do you want to continue?",
  "onlyTXT": "Only TXT files are supported.",
  "aiTextToSpeechDownloadFile": "Download",
  "notSupportFormat": "Sorry, this format is not supported.",
  "aiTextToSpeechConverting": "Processing",
  "aiTextToSpeechListening": "Testing",
  "aiSubtitleReplacePlaceHolder": "Replace text",
  "aiSubtitleReplaceSingle": "Replace",
  "aiSubtitleReplaceAll": "All",
  "aiSubtitleReplaceAllTip": "Replace all",
  "textToSpeechTitle": "Free Online AI Voice Generator <span class=\"text-primary-color bg-style\">Text to Speech</span>",
  "textToSpeechDesc": "Instantly convert text to voice intelligently with multilingual translation.",
  "textQuickEfficient": "Quick & Efficient",
  "textOneClickTitle": "One-Click Operation",
  "textOneClickDesc": "Add your text and with just one click, you can convert text to speech online for free.",
  "textFastTitle": "Fast Audition",
  "textFastDesc": "Listen to the AI-generated audio in real time, experiencing the natural and clear pronunciation.",
  "textAccurateTransTitle": "Accurate Translation",
  "textAccurateTransDesc": "Supports intelligent translation in multiple languages, including Spanish, French, German and more.",
  "TextIntroductionTitle": "Introduction to AI Text-to-Speech",
  "TextIntroductionDesc": "AI Text-to-Speech (TTS) technology is a digital tool that converts written text into spoken words through intelligent AI algorithms. These algorithms can accurately predict word pronunciations and perform real-time voice synthesis with natural-sounding AI voices.",
  "TextHowConvert": "Convert Text to Audio Online",
  "textAddTextTitle": "Add Text",
  "textAddTextDesc": "Type in your text or upload your text file.",
  "textSelectVoiceTitle": "Select Voice",
  "textSelectVoiceDesc": "Choose the voice type and target translation language.",
  "textGenerateAudioTitle": "Generate Audio",
  "textGenerateAudioDesc": "Efficiently and precisely produce high-quality audio files.",
  "textApplicationScene": "Application Scenarios",
  "textAudioBookDesc": "<span class=\"font-semibold text-[#2D2D33]\">Audiobooks: </span>Swiftly transcribe text into audible content.",
  "textLangLearnDesc": "<span class=\"font-semibold text-[#2D2D33]\">Language Learning: </span>Enhance language skills through auditory exercises.",
  "textVideoDubDesc": "<span class=\"font-semibold text-[#2D2D33]\">Video Dubbing: </span>Quickly add a variety of voices to videos.",
  "textGlobalExpanDesc": "<span class=\"font-semibold text-[#2D2D33]\">Global Expansion: </span>Localize marketing for global audiences.",
  "textAudioBookTitle": "Audiobooks",
  "textLangLearnTitle": "Language Learning",
  "textVideoDubTitle": "Video Dubbing",
  "textGlobalExpanTitle": "Global Expansion",
  "textOutstandingAds": "Outstanding Advantages",
  "textBoostTitle": "Efficiency Boost",
  "textBoostDesc": "The intelligent voice generator significantly improves work efficiency.",
  "textConsistencyTitle": "Quality Consistency",
  "textConsistencyDesc": "Ensures consistency in voice quality across all projects.",
  "textServiceTitle": "Global Service",
  "textServiceDesc": "Multilingual translation meets the needs of global users.",
  "textCostTitle": "Cost-Effective",
  "textCostDesc": "Saves a considerable amount of costs compared to traditional voiceovers.",
  "userComment13": "I\'m quite impressed with how easy and efficient the online text-to-speech generation tool is. It\'s very suitable for someone like me who\'s new to computers.",
  "userComment13Name": "Candy, Short Video Blogger",
  "userComment14": "After I enter the text, it helps me quickly convert it into audio, which is so convenient. Now it only takes a few minutes to complete podcast production!",
  "userComment14Name": "Jack, Podcast Blogger",
  "userComment15": "My students are becoming more and more interested in my classes now. It\'s really fun to read poems to them using different voices.",
  "userComment15Name": "Kevin, Teacher",
  "userComment16": "Reading text can sometimes be tiring, but listening to it through AI text-to-speech makes it much more relaxed, and occasionally I even learn other languages.",
  "userComment16Name": "Sandy, Student",
  "textLastTitle": "All-in-One AI Voice Generator",
  "textLastDesc": "Try RecCloud All-in-One AI Voice Generator today – it's fast, efficient, and available for free!",
  "notSupportFile": "This file is not supported, please select again.",
  "chatVideoTitle": "AI Video/Audio Summarizer",
  "indexChatVideoTitle": "AI Summarizer",
  "extractionAIAudio": "AI Audio Extraction",
  "extractContent": "Extract Content",
  "extractOriginalAudio": "Original Audio",
  "extractVocals": "Vocals",
  "extractAccompaniment": "Accompaniment",
  "extractionStart": "Start Extraction",
  "extractAudioDesc": "With our AI-powered audio extraction technology, you can quickly extract audio from various video formats, accurately separate vocals and accompaniment, and maintain high fidelity audio effects without compromising sound quality.",
  "originalAudio": "Original Audio",
  "vocals": "Vocals",
  "accompaniment": "Accompaniment",
  "extractMoreAudioTools": "More AI Audio and Video Processing Features",
  "hotAIFeat": "Hot AI Features",
  "baseAiFeat": "Basic AI Features",
  "moreFreeFeat": "More Free Features",
  "moreFreeFeatDesc": "Video Editing, Convert to GIF, etc.",
  "areYouSatisfiedWithResult": "Are you satisfied with the result?",
  "aiSatisfied": "Satisfied",
  "notSatisfied": "Not Satisfied",
  "userGroup": "User Group",
  "submit": "Submit",
  "yourFeedbackHelpsUsImprove": "Your feedback helps us improve...",
  "thankYouForYourFeedback": "Thank you for your feedback",
  "getMoreInfoAnd247Support": "Get more info and 24/7 support",
  "discord": "Discord",
  "slowProcessing": "Slow processing",
  "informationMissing": "Information missing",
  "grammarErrors": "Grammar errors",
  "longSentences": "Long sentences",
  "outOfSync": "Out of sync",
  "wrongLanguage": "Wrong language",
  "satisfiedEasyToUse": "Easy to use",
  "instantSync": "Instant sync",
  "accurateSegmentation": "Accurate segmentation",
  "accurateTranslation": "Accurate translation",
  "fastConversion": "Fast conversion",
  "slowLoading": "Slow loading",
  "grammarMistakes": "Grammar mistakes",
  "unrecognizedJargon": "Unrecognized jargon",
  "missingText": "Missing text",
  "conversionPrecise": "Conversion precise",
  "efficientAndFast": "Efficient and fast",
  "operationSimple": "Operation simple",
  "multilingualSupport": "Multilingual support",
  "summaryIncomplete": "Summary incomplete",
  "incorrectAnswers": "Incorrect answers",
  "subtitleMistakes": "Subtitle mistakes",
  "misunderstandings": "Misunderstandings",
  "interfaceLag": "Interface lag",
  "accurateAnswers": "Accurate answers",
  "accurateSummaries": "Accurate summaries",
  "userFriendly": "User-friendly",
  "efficient": "Efficient",
  "quickResponses": "Quick responses",
  "unnaturalSpeech": "Unnatural speech",
  "imbalancedPace": "Imbalanced pace",
  "translationErrors": "Translation errors",
  "conversionFailed": "Conversion failed",
  "monotoneVoice": "Monotone voice",
  "adequateQuality": "Adequate quality",
  "preciseTranslations": "Precise translations",
  "naturalPronunciation": "Natural pronunciation",
  "moderateRate": "Moderate rate",
  "excellentQuality": "Excellent quality",
  "slowSpeed": "Slow speed",
  "inaccurateTranslation": "Inaccurate translation",
  "unnaturalDubbing": "Unnatural dubbing",
  "limitedLanguages": "Limited languages",
  "fewDubbingOptions": "Few dubbing options",
  "translationAccurate": "Translation accurate",
  "dubbingNatural": "Dubbing natural",
  "highDefinition": "High definition",
  "fastSpeed": "Fast speed",
  "unclearRecognition": "Unclear recognition",
  "wrongSummary": "Wrong summary",
  "slowOperation": "Slow operation",
  "incorrectConversion": "Incorrect conversion",
  "accurateRecognition": "Accurate recognition",
  "effectiveSummarization": "Effective summarization",
  "preciseConversion": "Precise conversion",
  "smoothOperation": "Smooth operation",
  "backgroundNoise": "Background noise",
  "poorQuality": "Poor quality",
  "inaccurateExtraction": "Inaccurate extraction",
  "downloadFailed": "Download failed",
  "fewFormats": "Few formats",
  "quickExtraction": "Quick extraction",
  "highQuality": "High quality",
  "accurateSeparation": "Accurate separation",
  "manyFormats": "Many formats",
  "operationEasy": "Operation easy",
  "slowProcessingSpeed": "Slow processing speed",
  "textMainTitle": `RecCloud AI Video <span class="main-title">Generator</span>`,
  "textMainDesc": `Create stunning videos from text/images with AI, powered by a model rivaling Sora, Veo 3, Kling and Luma.`,
  "textContactUs": `Contact Us`,
  "textNow": `Start Now`,
  "textMainSubtitle": `All videos on this page were created directly by RecCloud AI Video Generator`,
  "textVideoListMainTitle": `Best <span class="gradient-color">AI Video Creator</span> for Various Scenarios`,
  "textVideoPart1Title": `<span class="gradient-color">Advertising</span> & <span class="gradient-color">Marketing</span> Videos`,
  "textVideoPart1List1": `Product Demos: AI highlights product features and usage with quality visuals.`,
  "textVideoPart1List2": `Brand Stories: AI crafts emotionally engaging videos sharing a brand\'s history and values.`,
  "textVideoPart1List3": `Social Ads: Custom short videos or animations on platforms like YouTube and TikTok boost brand awareness.`,
  "textVideoPart2Title": `<span class="gradient-color">Educational</span> & <span class="gradient-color">Training</span> Videos`,
  "textVideoPart2List1": `Online Courses: Videos on subjects with animations, board writing, and virtual experiments for engaging learning.`,
  "textVideoPart2List2": `Public Education: Health education and community service to increase awareness and involvement.`,
  "textVideoPart2List3": `Corporate Training: Orientation, skill training, and safety education to improve employee adaptation and skills.`,
  "textVideoPart3Title": `Professional <span class="gradient-color">Film</span> & <span class="gradient-color">Television</span> Video Clips`,
  "textVideoPart3List1": `Special Effects: AI enhances movie/TV effects (e.g., weather, historical scenes) for efficiency and cost reduction.`,
  "textVideoPart3List2": `Character Animation: AI creates virtual characters/animations, enriching content.`,
  "textVideoPart3List3": `Video Game Trailer: AI can assist in creating captivating game teaser trailers to attract user attention.`,
  "textContactTitle": `AI Generate <span class="gradient-color">Video From Text</span> Easily`,
  "textContactSubtitle1": `Powered by World's Top AI Models`,
  "textContactSubtitleDesc1": `RecCloud uses the most advanced AI models. With it, you can create astonishing and complex videos from prompts with perfect details that are almost the same as the ones existing in the physical world.`,
  "textContactSubtitle2": `No Need for Capturing & Editing Videos Anymore`,
  "textContactSubtitleDesc2": `Turn text & image to video with AI instantly. Say goodbye to complicated video shooting and editing processes. Video making cannot be any easier.`,
  "textContactSubtitle3": `Generate Video up to 1 Minute Long`,
  "textContactSubtitleDesc3": `RecCloud allows you to convert text to video up to 1 minute long, which can save you tons of time and money to create a video with the best length.`,
  "textQATitle": `Common FAQ for RecCloud AI Video Generator`,
  "textQ1": `How to make AI generated videos?`,
  "textA1": `With RecCloud, you can easily generate AI videos with AI model. Let AI to turn your imagination into videos.`,
  "textQ2": `How much does RecCloud AI Video Generator cost?`,
  "textA2": `For the pricing details, please <button class="text-primary-color contact-us-btn">contact us</button>.`,
  "textQ3": `What AI model does RecCloud use?`,
  "textA3": `RecCloud's AI model matches top performers like Sora, Veo 3, Kling and Luma, generating 1-minute high-quality videos with realistic movement.`,
  "textQ4": `Can I use RecCloud AI Video Generator commercially?`,
  "textA4": `Yes, you can use the AI generated videos for commercial purpose.`,
  "textQ5": `How can I ensure my data is secure?`,
  "textA5": `We ensure your data\'s security by employing advanced encryption, strict access controls, and continuous monitoring.`,
  "textAiOptionsTitle": `More Amazing AI Video Making Tools`,
  "textAiOptionsBtnText": `Learn More`,
  "textUseTitle": `<span class="gradient-color">Create Video from Text with AI Now!</span>`,
  "textToVideoTitle": "AI Video Generator",
  "textToVideoDesc": "AI Text/Image to Video",
  "prompt1": "A futuristic KI-Coaching scene in a virtual reality setting, a digital realm with holographic displays, a sense of limitless possibilities, clients and coaches connected through advanced technology, Digital artwork, VR headset.",
  "prompt2": "Perfume is stationary, water flows, wind blows green leaves and flowers, depth of field, light changes.",
  "prompt3": "Slightly floating with a makeup brush in hand. Girl opens her eyes. The wind blew off the petals of the flowers.",
  "prompt4": "Perfume is stationary, blue water and daisy float in the pond, product in the water , vray tracing, blurry details , sabattier filter, functionality emphasis, light sky blue and yellow, high details.",
  "prompt5": "A young woman sitting with makeup tools in front of a table, natural makeup, fair and smooth skin, in the style of video, uhd image, associated press photo, american barbizon school, soft pastels, miniaturecore, studio portrait.",
  "prompt6": "An actor dressed as a motorcycle rider rides a motorcycle through the snow, in the style of beeple, dark gold and gray, 32k uhd, i can\'t believe how beautiful this is, back button focus, unconventional use of space, rtx.",
  "prompt7": "Picture book style, In a dense forest, a kind old man is collecting firewood, happycore, The creatures of the forest live in harmony with him, 3d, Disney Style, Pixar.",
  "prompt8": "A beautiful female anchor in a white suit is doing news broadcast in front of a large LCD screen, studio lights, bright.",
  "prompt9": "Chibi, A handsome anchor is broadcasting news, the background of the news live broadcast room,smile,  happy, studio lights, bright, Disney style, Pixar, 3D, Redshift, Unreal Engine 5, Clay --ar 16:9.",
  "prompt10": "A very cute boy, in a forest, The wind blows through the forest, at night, 3d art, c4d, octane render, ray tracing, popmart blind box, clay material, Pixar trend, animation lighting, depth of field, ultra detailed, Fireflies glow, lights flash.",
  "prompt11": "A man is being interviewed by a reporter on camera, in the style of texture-rich, deutscher werkbund, smooth and polished, smilecore, sleek metallic finish, handheld, multiple styles --s 750 --v 6.0 --ar 16:9.",
  "prompt12": "Chibi, A handsome anchor is broadcasting news, the background of the news live broadcast room,smile, studio lights, bright, Disney style, Pixar, 3D, Redshift, Unreal Engine 5, Clay.",
  "prompt13": "With a broad perspective and super-wide angle, an aerial photo captures the construction of a planetary engine base from an exceedingly wide viewpoint. At the heart of the frame sits a colossal planetary engine, spewing high-temperature flames, designed to propel the Earth itself, creating a scene that is pure science fiction and utterly awe-inspiring. High resolution, 8K. --ar 16:9.",
  "prompt14": "Breathtaking sichuan jiuzaigou in fall, natural light, Water flows, fog drifts, style of National Geographic.",
  "prompt15": "A combat armored wyvern, War rages.",
  "prompt16": "Tropical fish, corals and fish underwater by tropical wallpaper for desktop, in the style of 32k uhd, red and indigo, birds-eye-view, colorized, ultra hd, high resolution, martiros saryan.",
  "prompt17": "There are many sparks falling from the sky, Future city, science fiction background, robots, aliens, alien spaceships, futuristic, Chris Foss style.",
  "prompt18": "The depiction showcases a vibrant and colorful fantasy battle scene. At the core is a monstrous character, armed with a colossal axe and ready to launch an attack, their eyes brimming with the resolve for combat. Facing them is a soldier clad in blue and gold armor, wielding a shield and hammer. The painting is vivid, filled with a sense of movement and the tension of battle. The entire scene is enveloped by an epic atmosphere. Unreal Engine 5, game CG, CG visuals, extreme detail, high definition 8K.",
  "prompt19": "Prompt:",
  "enterTheYouTube": "Please enter the YouTube video link.",
  "yourYouTubeUrlNotValid": "The provided URL is not a valid YouTube video link. Please provide a correct URL.",
  "availableSubtitles": "This video does not have available subtitles and cannot be summarized. Please try another video.",
  "onlineScreenRecorderTitle": "Online Screen Recorder",
  "audioExtractTitle": "Free Online AI Vocal Remover",
  "enableYoutubeTitle": "Instantly summarize YouTube videos with a URL click!",
  "youtubeListItem1": "YouTube Video Summarizer",
  "youtubeListItem2": "Want to summarize a YouTube video? Just enter the video\'s URL, and the AI will instantly provide a summary. It\'s user-friendly and efficient for quick insights!",
  "aboutTitle": "Create Videos Easily with RecCloud Platform",
  "contactUsTitle": "Contact Us",
  "pricingTitle": "RecCloud\'s Price Details",
  "textToVideoWrongTheme": "Wrong Theme",
  "textToVideoUnnaturalVoiceover": "Unnatural Voiceover",
  "textToVideoSceneMismatch": "Scene Mismatch",
  "textToVideoSlowCreation": "Slow Creation",
  "tooShortLong": "Too Short/Long",
  "themeMatch": "Theme Match",
  "HDQuality": "HD Quality",
  "realisticDetails": "Realistic Details",
  "goodLength": "Good Length",
  "fastCreation": "Fast Creation",
  "textToVideoPrompt": "Prompt",
  "textToVideotTextPrompt": "Use a text prompt to generate a video.",
  "samplePrompt": "Try sample prompt",
  "textToVideoAspectRatio": "Aspect Ratio",
  "textToVideoAudio": "Audio",
  "soundEffects": "Sound Effects",
  "subtitleFont": "Subtitle Font",
  "subtitleStroke": "Subtitle Stroke",
  "generationHistory": "Generation History",
  "generate": "Generate",
  "credits": "X Credits",
  "generating": "Generating...",
  "generationFailed": "Generation Failed",
  "textToVideoRetry": "Retry",
  "free": "Free",
  "InvalidPrompts": "Please revise as it contains irrelevant or inappropriate content.",
  "noneGeneratedVideos": `No videos generated yet.`,
  "textToVideoFree": `Free 5 times`,
  "fiveCredits": "FiveCredits",
  "randomTopic": `Random theme`,
  "AIHelpMeWrite": "AI Write",
  "textToVideoPlaceholderText": `Enter script or use AI to auto-generate`,
  "textToVideoPlaceholderSubjectText": `Enter theme, auto-generate script`,
  "containsIrrelevant": `Invalid content, please modify and retry`,
  "textToVideoVideoDuration": `Video length`,
  "voiceoverSound": `Voiceover`,
  "backgroundMusic": `Background music`,
  "textToVideoBorders": `Border`,
  "textToVideoPosition": `Position`,
  "generatingVideoScript": `Generating script, please wait`,
  "testToVideoGenerate": "Generate",
  "testToVideoTextLimitTip": `Please ensure script ≤ # words`,
  "settingVideo": "Video",
  "settingAudio": "Audio",
  "settingSubtitle": "Subtitle",
  "settingShort": "Short",
  "settingMedium": "Medium",
  "settingLong": "Long",
  "settingAutoMatch": "Auto-match",
  "settingLocalAudio": "Local audio",
  "settingNone": "None",
  "settingBottom": "Bottom",
  "settingCenter": "Center",
  "settingTop": "Top",
  "bannerCookieInfo": `This website uses cookies that are essential for the operations of this website and its core functions. Other cookies will only be placed with your consent. For more details visit our`,
  "bannerCookieBtn": `Cookies Policy.`,
  "bannerDecline": `Decline`,
  "bannerAccept": `Accept All Cookies`,
  "textToVideoLimitTip": "Today's free trial attempts (# attempts) have been used up. Welcome to come back tomorrow~",
  "audioPreview": "Preview",
  "randomStory": "Random",
  "uploadTxt": "Upload",
  "originFreeCharacters": "First # original characters free",
  "noFreeNotice": "Every # characters consume:",
  "voiceName": "Voice Name",
  "voicePreview": "Voice 01",
  "audioCloneTitle": "Welcome to RecCloud AI Voice Cloning. Please read this example:",
  "audioCloneContent": "I agree to RecCloud's AI Voice Cloning, will follow rules, and take responsibility.",
  "audioCloneDesc": "Your voice\'s tone and emotion will be cloned. Please record in your preferred tone for best results, in a quiet place without background noise like multiple voices, music, or non-speech sounds.",
  "audioStartBtn": "Start",
  "audioEndBtn": "Stop",
  "audioCloneRecording": "Recording",
  "recorder": "Rerecord",
  "audioCloneAgree": "I have read and agree to the《#》",
  "audioServiceAgreement": "RecCloud Voice Cloning Service Agreement",
  "saveVoice": "Save Voice",
  "audioCloneNoFreeTip": "Exceeding 1000 characters costs credits (1 credit per # characters). Proceed with full conversion?",
  "audioCloneFreeTip": "Try # characters",
  "startConvertAll": "Convert Fully",
  "aiTextPlaceholder": "Enter keywords, use AI Write service to generate complete story content.",
  "customCloneTitle": "Custom clone",
  "aiCloneErrorTitle": "The spoken text does not match, please re-record according to the provided spoken text.",
  "aiCloneErrorTitle2": "Please keep the recording duration between 5 to 10 seconds.",
  "aiCloneAgree": "Please read and check the agreement",
  "audioCloneLimitDialogTitle": "Reached the free voice cloning limit of 3, consume # credits/to continue creating voices.",
  "generatingAllScript": "Generating script, Please try again later.",
  "vipExtendTitle": "Buy",
  "vipCreditsTab": "Credits",
  "vipSpaceTab": "Space",
  "onlyVipCanUse": "Exclusive to VIPs",
  "vipYear": "Annual",
  "vipMonth": "Monthly",
  "vipSaleTitle": "80% OFF",
  "vipBuyNow": "Subscribe Now",
  "vipCreditsExtentTitle": "Upgrade Credits",
  "vipBasic": "Basic",
  "vipPro": "Pro",
  "vipBusiness": "Business",
  "vipDeductionRules": "Deduction Rules",
  "vipOnlineScreenRecording": "Online recording duration",
  "vipLightEditorBenefits": "LightEditor Membership Benefits",
  "vipCredits": "# Credits/Month",
  "vipCreditsSecond": "# Credits/Secs",
  "vipAISummarizer": "AI Chat",
  "vipBenefits": 'Plans <span class="text-primary-color">Comparison</span>',
  "vipMultiScreenRecorderBenefits": "Multi-Screen Recorder Membership Benefits",
  "vipRecordMinute": `# min`,
  "vipRecordMinutes": `# Minutes`,
  "vipCredits100Words": "# credits/@ characters",
  "vipCredit100Words": "# credit/@ characters",
  "textToVideoProModel": `Advanced Model`,
  "textToVideoProModelTitle": `Generate AI images or videos by keywords`,
  "textToVideoProModelDesc": `Each time #credits (extra deduction for image-to-video)`,
  "textToVideoBasicModel": `Basic Model`,
  "textToVideoBasicModelTitle": `Match materials by keywords to create videos`,
  "textToVideoBasicModelDesc": `Free # times`,
  "textToVideoStyle": `Visual Style`,
  "textToVideoSceneLoadingTitle": `Generating Scene`,
  "textToVideoSceneLoadingDesc": `Re-enter in {0}generated record{1}`,
  "textToVideoProRecords": `Advanced Model Records`,
  "textToVideoBasicRecords": `Basic Model Records`,
  "textToVideoPreStep": `Previous Step`,
  "textToVideoDelScene": `Confirm delete current scene?`,
  "textToVideoSceneNum": `Scene #`,
  "textToVideoAllImgToVideo": `All images to video`,
  "textToVideoGeneratingVideo": `Generating Video`,
  "textToVideoImgToVideoTitle": `Convert to Video`,
  "textToVideoReplaceTitle": `Replace Material`,
  "textToVideoReplaceItemTitle1": `Prompt`,
  "textToVideoReplaceItemTitle2": `Style`,
  "textToVideoReplaceToImg": `AI Image`,
  "textToVideoReplaceToVideo": `AI Video`,
  "textToVideoReplacePrompt": `Enter prompt e.g., Ducks at sunset`,
  "textToVideoReplaceNoInput": `Please enter a prompt first`,
  "textToVideoReplaceRegenerate": `Regenerate`,
  "textToVideoReplaceOk": `Confirm`,
  "textToVideoReplaceCloseConfirm": `Exiting will lose generated content, continue?`,
  "headerAiSubtitleTitle": `AI Subtitle Generator`,
  "priceTextToVideoBasic": `Basic model: # credits/video`,
  "priceTextToVideoPro": `Advanced model: # credits/video`,
  "priceTextToVideoImgToVideo": `Frame to video: # credits`,
  "priceTextToVideoTextToImg": `Text to video frame: # credit/frame`,
  "textToVideoCreateVideoLoading": `Generating Video`,
  "textToVideoCreateSceneLoading": `Creating Scene`,
  "downloadAppTitle": `Download APP`,
  "headerDownloadTitle": `Download`,
  "scanToDownloadApp": `Scan to download APP`,
  "headerDownloadWin1": `Click to download Multi-Screen Recorder`,
  "headerDownloadWin2": `Click to download LightEditor`,
  "downloadAppDesc1": `RecCloud: AI Media`,
  "downloadAppDesc2": `Better Experience`,
  "apiAiFuncTitle": `AI Features`,
  "apiNormalTitle": `Basic Functions`,
  "textToVideoAllImgToVideoConfirmTitle": `Convert all images to video? Estimated deduction: #credits`,
  "textToVideoAllImgToVideoConfirmSubtitle": `Deduction rule: #credits per video`,
  "guideAdvancedDesc": `Create AI videos`,
  "guideAdvancedDesc1": `Text to Image`,
  "guideAdvancedDesc2": `Image to Video`,
  "guideAdvancedDesc11": `#/Image`,
  "guideAdvancedDesc22": `#/Video`,
  "guideNextBtnText": `Got it`,
  "guideSettingTitle": `Click to adjust settings`,
  "guideBasicDesc": `Create real-shot mixed video`,
  "guideBasicDesc1": `Free`,
  "guideBasicDesc2": `Limit exceeded`,
  "guideBasicDesc11": `#/Times`,
  "guideEditorTitle": `Click to edit and adjust video`,
  "guideToVideoTitle": `Click to convert images to video`,
  "guideToVideoDesc": `Convert images to video, deducts credits by count`,
  "guideReplaceTitle": `Click to replace material`,
  "guideBasicDesc12": `# Times`,
  "textToVideoLangNotSupport": `The input language is not supported. Please enter English.`,
  "speechConversionRecords": `Conversion Records`,
  "speechUploadFiles": `Click to upload/drag audio and video files here`,
  "speech3MinutesBenefits": `Benefits: First minute free, then just 1 credit per minute.\n<i>Benefits: </i>Log in daily for a free 20-minute conversion!`,
  "speech3HoursSize": `Size: Video ≤ 4G, Audio ≤ 500M, up to 3 hours`,
  "speechSupportFormat": `Format: #`,
  "speechMySpaces": `My Space`,
  "speechRecordAudio": `Record Audio`,
  "speechNoEligibleFile": `No eligible files`,
  "speechUploading": `Uploading...`,
  "speechExtractText": `Extracting text...`,
  "speechExport": `Export`,
  "speechExportContent": `Export Content`,
  "speechSummarize": `Summary`,
  "speechSaveData": `*Log in to save data, or it will be lost when you exit`,
  "speechAutoSave": `*Content auto-saved`,
  "speechSave": `Save`,
  "speechSaveSuccess": `Saved Successfully`,
  "speechRegenerate": `Regenerate`,
  "speechRegenerateCredits": `Regeneration requires credits, please log in`,
  "speechCopy": `Copy`,
  "speechNewCreate": `New`,
  "speechAiRecordsAssociated": `Associated AI records will be permanently deleted and cannot be recovered!`,
  "speechBatchProcess": `Batch processing is for business members. Please log in or upgrade to VIP.`,
  "speechUpgradeVip": `Upgrade to VIP`,
  "speechLogIn": `Log in`,
  "speechBatch10Files": `Batch processing supports up to 10 files. Please reselect.`,
  "speechExceed3Minutes": `Exceeding 3 minutes will deduct credits (1 per minute). Complete conversion?`,
  "speechCompleteConvert": `Complete conversion`,
  "speechTrialMinutes": `Trial # minutes`,
  "speechOriginText": `Original text`,
  "speechSaveFailed": `Save failed`,
  "speechRegenerate1Credit": `Regenerating the summary will consume 1 credit.`,
  "speechConfirmDelete": `Confirm deletion?`,
  "speechConverting": `Converting`,
  "speech10FilesBatch": `Batch: Upload up to 10 files (business members only)`,
  "speechCurrentNoRecords": `You currently have no conversion records`,
  "speechNoFileSummary": `The text content is too short to summarize.`,
  "mainStartForFree": `Start for Free`,
  "speechAiPolish": `AI Polished`,
  "speechExpand": `Expand`,
  "speechCollapse": `Collapse`,
  "speechTranslate": `Translate`,
  "speechBilingual": `Bilingual`,
  "speechTranslationContent": `Translation`,
  "speechStartTranslate": `Translate`,
  "speechTranslateSuccess": `Success!`,
  "speechTranslateFailed": `Failed, please retry`,
  "speechAiChat": `AI Chat`,
  "speechAiChatHello": `Hi, I'm RecCloud AI. \nNeed help with extraction? Ask me!\nTry RecCloud AI now!`,
  "speechAskAny": `Ask me anything`,
  "speechAudioListen": `You speak, I'm listening...`,
  "speechAudioPause": `Pause`,
  "speechAudioContinue": `Continue`,
  "speechAudioComplete": `Complete`,
  "speechAudioMaxRecord": `Max recording: <span class="text-primary-color font-medium">#</span> mins`,
  "speechAudioFailedRetry": `Failed, please retry`,
  "speechAudioCancel": `Cancel`,
  "speechAudioRetry": `Retry`,
  "speechTransNeedLogin": `Translation requires deduction of tokens, please log in`,
  "speechAudioPauseDesc": `Recording paused`,
  "dialogTitleDownloadPoint": `Downloading files over @ will consume credits (# credits/GB). Continue?`,
  "dialogTitleDurationPoint": `Exceeding @ minutes will deduct credits (# credits/min). Complete conversion?`,
  "dialogTitleTextLengthPoint": `Exceeding @ characters will consume credits (1 credit/# characters). Complete conversion?`,
  "clientVipShip": `Access AI features`,
  "batchProcessing": `Batch process`,
  "vipFree": `Free for members`,
  "speechToHistory": `Check Records`,
  "chatVideoErrorFixTitle": `Very sorry, we're working hard to fix it...`,
  "priceShortTermTitle": `Short-term`,
  "pricingQaqQuestions7": `What are the differences among Basic, Pro, and Business Members?`,
  "pricingQaqAnswer7": `The main difference lies in the number of complimentary credits provided. Other benefits are the same for both Basic and Pro Members, and both are strictly for personal, non-commercial, and non-profit use. Business Members, in addition to enjoying more benefits, are allowed to use the generated audio and video files for commercial and profit-making purposes.`,
  "headerWinDownload": `Download Now`,
  "aiSubtitleSubtitles": `Subtitles`,
  "aiSubtitleV2Style": `Style`,
  "aiSubtitleFindReplace": `Replace`,
  "aiSubtitleSwitch": `Switch`,
  "aiSubtitleFind": `Find`,
  "aiSubtitleEnterFindText": `Enter text`,
  "aiSubtitleReplaceWith": `Replace with`,
  "aiSubtitleEnterReplaceText": `Enter text`,
  "aiSubtitlePrevios": `Previous`,
  "aiSubtitleNext": `Next`,
  "aiSubtitleSelectLang": `Select language`,
  "aiSubtitleConfirm": `Confirm`,
  "aiSubtitleSourceTrans": `Source + Translation`,
  "aiSubtitleAlign": `Align`,
  "aiSubtitleVerticalPos": `Position`,
  "aiSubtitleLetterSpace": `Spacing`,
  "aiSubtitleOutline": `Outline`,
  "aiSubtitlePresetStyle": `Preset Style`,
  "aiSubtitleExport": `Export`,
  "aiSubtitleExportVideo": `Video (MP4)`,
  "aiSubtitleExportSrt": `Subtitles (SRT)`,
  "aiSubtitleOneMinFree": `1 minute free trial`,
  "aiSubtitleGenerateSubtitle": `Subtitle Language`,
  "aiSubtitleSubtitleGenerating": `Generating subtitles...`,
  "aiSubtitleFirstSource": `Generate source subtitles first`,
  "aiSubtitlePerMin": `Per minute:`,
  "aiSubtitleEstimated": `Estimated:`,
  "aiSubtitleStyleBg": `BG`,
  "aiSubtitleSingle": `Single`,
  "aiSubtitleDouble": `Bilingual`,
  "headerToolsSpeechTitle": `AI Speech to Text`,
  "headerToolsSpeechDesc": `Easily transcribe spoken words into accurate text.`,
  "headerToolsVoiceTitle": `AI Voice Generator`,
  "headerToolsVoiceDesc": `Transform text into natural, clear speech effortlessly.`,
  "headerToolsSubtitle": `AI Subtitle Generator`,
  "headerToolsSubtitleDesc": `Auto-generate accurate subtitles in various languages.`,
  "headerToolsVideoTitle": `AI Video Generator`,
  "headerToolsVideoDesc": `Create stunning AI videos effortlessly for all your needs.`,
  "headerToolsSummaryTitle": `AI Video/Audio Summarizer`,
  "headerToolsSummaryDesc": `Summarize long videos into concise, informative highlights.`,
  "headerToolsTransTitle": `AI Video Translator`,
  "headerToolsTransDesc": `Seamlessly translate video content into multiple languages.`,
  "headerToolsTransVocalTitle": `AI Vocal Remover`,
  "headerToolsTransVocalDesc": `Instantly remove vocals from tracks to create instrumentals.`,
  "headerAiTools": `AI Tools`,
  "headerEdit": `Record & Edit`,
  "headerResource": `Resource`,
  "headerDownload": `Download`,
  "headerAPI": `API`,
  "headerPricing": `Pricing`,
  "headerScreenOnline": `Record Screen Online`,
  "headerTrimVideo": `Trim Video Online`,
  "headerCropVideo": `Crop Video Online`,
  "headerMergeVideo": `Merge Video Online`,
  "indexMainTitle": `<span class="gradient-text">AI-Powered</span> Audio & Video <span class="gradient-text">Workshop</span> for Everyone`,
  "indexMainDesc": `Editing and creating videos/audios cannot be any easier with RecCloud AI tools!`,
  "indexMainStartFree": `Get Started Free`,
  "indexSecondTitle": `Versatile AI Toolkit for <span class="gradient-text">Video Processing</span>`,
  "indexSecondDesc": `With the right tool, you can boost efficiency and unleash creativity in ways you never imaged!`,
  "indexSpeechTittle": `AI Speech to Text`,
  "indexSpeechDesc": `Transform spoken words into accurate text with our AI Speech to Text tool. Polish and summarize texts automatically using AI, making it perfect for transcriptions, online meetings, and efficient note-taking.`,
  "indexFreeTry": `Try for free`,
  "indexVoiceTitle": `AI Voice Generator`,
  "indexVoiceDesc": `Convert text into natural-sounding speech. Support various voice types and multiple languages. You can even transform text in one language into speech in another. Perfect for content creators and marketers.`,
  "indexSubtitleTitle": `AI Subtitle Generator`,
  "indexSubtitleDesc": `Easily generate accurate subtitles in the original language and translate them into multiple languages. Improve accessibility, SEO, and audience retention across platforms—perfect for global creators and educators.`,
  "indexVideoTitle": `AI Video Generator`,
  "indexVideoDesc": `Transform text into stunning videos effortlessly with our advanced AI video generator. Bring your ideas to life with powerful AI technology—perfect for creators, marketers, and storytellers.`,
  "indexSummaryTitle": `AI Video/Audio Summarization`,
  "indexSummaryDesc": `Summarize YouTube videos and other lengthy videos, and interact with AI based on the video's content. Perfect for online courses, social media highlights, and presentations. Save time and boost engagement with concise, informative clips.`,
  "indexWorldwide": `Trusted by <span class="gradient-text">Top Brands</span> Worldwide`,
  "indexUsersRevolutionize": `See How Users <span class="gradient-text">Revolutionize</span> Their Video Work`,
  "indexUserOneName": `James Brown`,
  "indexUserOneJob": `Student`,
  "indexUserOneDesc": `RecCloud's AI Speech to Text tool is a superb for efficient note-taking and transcriptions. It accurately converts lectures into polished, summarized text effortlessly! Highly recommended!`,
  "indexUserTwoName": `Michael Watts`,
  "indexUserTwoJob": `TikTok Creator`,
  "indexUserTwoDesc": `The AI Voice Generator in RecCloud is fantastic! It converts text into natural-sounding speech in multiple languages, perfect for creating multilingual content. A game-changer!`,
  "indexUserThreeName": `Emily Smith`,
  "indexUserThreeJob": `Educator`,
  "indexGainSkill": `Get inspired, and gain new skills about <span class="gradient-text">latest AI tools</span>`,
  "indexExploreBlog": `Explore the Blog`,
  "indexLastScreenTitle": `Embrace the Future of <span class="gradient-text">AI Video Editing</span> with RecCloud`,
  "indexUserThreeDesc": `As an online educator, I rely on RecCloud's AI Subtitle Generator to produce accurate subtitles and translations for my recorded courses, thereby improving audience retention among my global student base.`,
  "footerReccloudTitle": `AI-Powered Audio/Video Workshop`,
  "footerHelpFAQ": `Help & FAQs`,
  "footerContactUs": `Contact Us`,
  "footerAboutUs": `About Us`,
  "footerMobileApp": `Mobile App`,
  "startAiTools": `AI Tools`,
  "startGroupTitle": `My Groups`,
  "startApi": `API`,
  "startAiToolsTitle": `RecCloud AI Tools`,
  "startWinTitle": `RecCloud Audio/Video Tools`,
  "startWinDesc": `Download desktop for more editing features`,
  "startWinDownloadTitle": `Download`,
  "startMobileDesc": `AI-powered editing tool on your phone`,
  "startFreeRecord": `Record Screen`,
  "textTranslate": `Translate`,
  "textStartTranslate": `Start`,
  "textSpeed": `Speed`,
  "textVolume": `Volume`,
  "textBGM": `BGM`,
  "textChooseBGM": `Choose BGM`,
  "textClickUpload": `Click to upload`,
  "textNoBGM": `No BGM`,
  "textWordsFree": `Free up to # words!`,
  "textBrowserDownload": `Download complete, please check your browser's downloads.`,
  "dialogAddFormatError": `Upload failed. Unsupported format. Please choose another file.`,
  "dialogAddSizeDurationError": `Upload failed. Exceeds maximum duration/size. Please choose another file.`,
  "dialogAddEmptyAudioTrackError": `Upload failed. No audio information detected. Please choose another file.`,
  "dialogAddNetworkError": `Addition failed, network error. Please retry later.`,
  "dialogAddFileError": `Upload failed. File not supported. Please choose another file.`,
  "dialogTitleSupportExt": `Supported Formats`,
  "dialogTitleFileSize": `Maximum Limits: Video ≤ #, Audio ≤ $, Duration ≤ @ hours.`,
  "dialogResultEmptyAudioTrackError": `Generation failed. No vocals detected. Please replace the file.`,
  "dialogResultTimeoutError": `Generation failed. Timed out, please retry.`,
  "dialogResultNetworkError": `Generation failed, network error. Please retry.`,
  "dialogResultFileError": `Generation failed. Please replace the file and retry.`,
  "dialogTitleReplace": `Replace`,
  "mobileChatTitle": `Hi! I'm your RecCloud AI assistant.`,
  "mobileChatSubtitle": `Ask me anything about transcriptions! I'm here to help with any questions.`,
  "mobileChatQATitle": `FAQ`,
  "mobileChatQ1": `What's the theme of this meeting/course? What key points were discussed?`,
  "mobileChatQ2": `Were any dates, times, or significant data mentioned in the document?`,
  "mobileChatQ3": `Based on the content, what specific tasks or action plans can be outlined?`,
  "dialogNoticeNoLoginOnlyFreeTitle": `Unregistered users can convert files up to # minute long.`,
  "dialogNoticeLoginTitle": `Log in daily for a free conversion of a file up to # minutes!`,
  "dialogLoginNowTitle": `Log in Now`,
  "noLoginResultLimitTitle": `Unregistered users can convert only the first 100 words.`,
  "loginResultNoLimitTitle": `Log in daily to enjoy unlimited text conversion once a day.`,
  "enableFreeTryEveryDayTitle": `Free trial daily`,
  "DialogEstimatedCredits": `Estimated credits:`,
  "iosTitle": `Download on the`,
  "iosDownload": `APP Store`,
  "androidTitle": `GET IT ON`,
  "androidDownload": `Google Play`,
  "polishingText": `AI Polishing`,
  "speechToTextFreeTimeTitle": `Free for # minutes`,
  "voiceManagementTitle": `Voice Management`,
  "voiceManagementAddTitle": `Add Character`,
  "voiceAddDefaultName": `Character #`,
  "saveRecordTitle": `Save`,
  "singleVoiceAudioTitle": `Default`,
  "multiVoiceAudioTitle": `Multi-voice`,
  "wordsNum": `# characters`,
  "smartMatchBtnTitle": `Smart Voice Matching`,
  "animationSmartMatchingTitle": `Click [Smart Voice Matching] to automatically identify characters and assign voices.`,
  "smartMatchLoadingTitle": `Processing`,
  "smartMatchTextTooShortTitle": `Input content is too short. Please enter at least # characters`,
  "aiSubtitleEquity": `Logged-in users can transcribe files for free once in # minutes.`,
  "aiSubtitleSignLog": `Sign Up/Log In`,
  "aiSubtitleUpgradeProcess": `Not enough credits. Upgrade to proceed.`,
  "aiSubtitleUpgradeTranscribe": `Not enough credits. Upgrade or transcribe only the first # mins.`,
  "aiSubtitlePricingDesc": `Pricing: New accounts get a 10-min free trial, then consume 1 credit per minute.`,
  "aiSubtitleFreeFirstTime": `First 10 mins free`,
  "aiSubtitleUpgradeUser": `Upgrade Now`,
  "textToSpeechDetectingTitle": `Language: #`,
  "textToSpeechDetectingTipTitle": `Note: The selected language should match the text. Translate the text first if you want to change languages.`,
  "textToSpeechDetectingTitleText": `Auto-detection`,
  "historyListSearchText": `Search...`,
  "aiSubtitleNoLoginFreeDuration": `Guests: extract source language subtitles for # minutes.`,
  "aiSubtitleLoginFreeLimitDuration": `Logged-in users: extract any language subtitles for # minutes once.`,
  "limitCountDesc": `Free storage limit of # reached. Please upgrade or delete files to continue.`,
  "limitDeleteFile": `Delete files`,
  "limitStorageDesc": `Storage full. Upgrade or organize to continue.`,
  "limitDeleteWithAudio": `Delete audio and video files too?`,
  "limitDeleteNoRecover": `Cannot recover after deletion. Sure to delete?`,
  "limitDeleteOnlyHistory": `Delete history only`,
  "aiVideoTranslateTitle": `AI Video Translator: Fast and Accurate.`,
  "textToSpeechEquityLimitLogin": `Logged-in users get one free #-character conversion daily.`,
  "aiTransVideoTranslation": `Video Translation`,
  "aiTransOriginVideoLang": `Original language`,
  "aiTransAutoDetect": `Auto detect`,
  "aiTransTargetLang": `Target language`,
  "aiTransVoiceSetting": `Voice settings`,
  "aiTransNowTranslate": `Translate Now`,
  "aiTransFreeFirstTime": `First # minutes free`,
  "aiTransNewAccountFree": `Benefits: New users get 1 free trial.`,
  "aiTransTranslating": `Translating...`,
  "aiTransSubtitleWithAudio": `Translation includes subtitles and audio.`,
  "aiTransDescStep1": `Step 1: Translate subtitles and preview audio`,
  "aiTransDescStep2": `Step 2: Create the translated video`,
  "aiTransWaitLong": `Taking too long? Your record has been saved automatically. Check it in <i>Records</i>.`,
  "aiTransBePatient": `Please be patient, for every masterpiece takes time to unfold.`,
  "aiTransRetryTranslate": `Re-translation (#)`,
  "aiTransRetryTranslateTimes": `Each task offers # free re-translations attempts.`,
  "aiTransPlayAudio": `Play audio`,
  "aiTransOriginAudio": `Original`,
  "aiTransTranslateAudio": `Translated`,
  "aiTransPreviewSentence": `Translated audio shows only the first # sentences;`,
  "aiTransFullResult": `generate to see the full result.`,
  "aiTransSubtitleShow": `Subtitle`,
  "aiTransGenerate": `Generate full video`,
  "aiTransRetryTranslateSentence": `Re-translate`,
  "aiTransSubtitleSetting": `Subtitle Settings`,
  "aiTransRetryGenerateChance": `Re-generate with different voices, you have # chances.`,
  "aiTransTranslationFail": `Translation failed`,
  "aiTransVideoProcessing": `Processing...`,
  "aiTransGenerateFail": `Generation failed`,
  "aiTransFreeDownload": `Free first download`,
  "aiTransNoEnoughJustMin": `Insufficient credits. Upgrade to try again or translate only the first 5 mins.`,
  "aiTranslateTryAudioTitle": `Preview`,
  "aiTranslateTryAudioDesc": `First # sentences`,
  "aiTranslateReStart": `Regenerate preview`,
  "aiTranslatePreviewEnded": `Preview ended`,
  "aiTranslateTryAudioLoading": `Processing...`,
  "aiTranslateTryAudioSuccess": `Success`,
  "aiTranslateTryAudioFail": `Failed`,
  "aiTranslateGuideAnimationTitle": `Video translation has two steps:`,
  "aiTranslateGuideAnimationDesc1": `Step 1: Subtitle translation and audio preview.`,
  "aiTranslateGuideAnimationDesc2": `Step 2: Voice synthesis and video generation.`,
  "aiTranslateGuideAnimationDesc3": `Price per min:`,
  "aiTranslateVolumeSetting": `Volume`,
  "limitDeleteDescTitle": `Deleting this will remove all related operation records, and they cannot be recovered!`,
  "limitDeleteLabel": `Delete this record only`,
  "textToSpeechEquityWithLogin": `Logged-in users get one free generation up to 1000 characters.`,
  "textToSpeechEquityLimitTry": `Insufficient credits. Upgrade to Pro or generate the first # characters only.`,
  "textToSpeechFreeTrial": `One free trial`,
  "speechToTextEquityWithLogin": `Log in for a free conversion of a file up to # minutes!`,
  "startAiTranslateHot": `Hot`,
  "downloadWinClientTitle": `Edit Timestamps`,
  "downloadWinClientSubtitle": `Download the app, more features await!`,
  "downloadWinClientDesc1": `Clear editor`,
  "downloadWinClientDesc2": `Flexible controls`,
  "downloadWinClientDesc3": `Free export`,
  "aiTranslateMainTitle": `Free Online <i>AI Video Translator</i>`,
  "aiTranslateMainDesc": `Translate video content into multiple languages with AI, offering both voiceover translation and subtitle translation for comprehensive video localization.`,
  "aiTranslateFreeStart": `Start for Free`,
  "aiTranslateLangToWorld": `Translate Video Into Any Language Around the World`,
  "aiTranslateLangToWorldDesc": `RecCloud offers translation in over 70 major world languages, including various accents and dialects. With this AI video translation tool, you can effortlessly reach a global audience.`,
  "aiTranslateEnglish": `English`,
  "aiTranslateChinese": `Chinese`,
  "aiTranslateFrench": `French`,
  "aiTranslateGerman": `German`,
  "aiTranslateJapanese": `Japanese`,
  "aiTranslatePortuguese": `Portuguese`,
  "aiTranslateItalian": `Italian`,
  "aiTranslateSpanish": `Spanish`,
  "aiTranslateArabic": `Arabic`,
  "aiTranslateHindi": `Hindi`,
  "aiTranslateKorean": `Korean`,
  "aiTranslateDutch": `Dutch`,
  "aiTranslateRussian": `Russian`,
  "aiTranslateThai": `Thai`,
  "aiTranslateVietnamese": `Vietnamese`,
  "aiTranslateMalaysian": `Malaysian`,
  "aiTranslateSceneOneTitle": `High-Accuracy Video Translation`,
  "aiTranslateSceneOneDesc": `Utilizing advanced AI algorithms, RecCloud delivers translations with remarkable precision, ensuring the original message is conveyed accurately across languages.`,
  "aiTranslateSceneTwoTitle": `Translate Both Voiceovers & Subtitles`,
  "aiTranslateSceneTwoDesc": `RecCloud makes translating voiceovers and subtitles easy, providing a variety of voice tones to choose from for your voiceovers.`,
  "aiTranslateSceneThreeTitle": `Manual Editing & Proofreading`,
  "aiTranslateSceneThreeDesc": `RecCloud grants you the flexibility to manually edit and proofread both original and translated texts, enhancing the quality and relevance of your content.`,
  "aiTranslatePeopleLove": `<i>People Love</i> RecCloud AI Video Translator`,
  "aiTranslateCommentOne": `RecCloud has expanded our course offerings to international students with accurate multilingual translations—transforming our educational reach.`,
  "aiTranslateCommentOneUser": `Emma, University Course Coordinator`,
  "aiTranslateCommentTwo": `With RecCloud AI, we deliver uniform training worldwide. It's efficient, reliable, and has unified our global workforce seamlessly.`,
  "aiTranslateCommentTwoUser": `Michael, Corporate Training Manager`,
  "aiTranslateCommentThree": `RecCloud boosts our social media engagement by translating user content automatically, fostering inclusive global interactions effortlessly.`,
  "aiTranslateCommentThreeUser": `Alex, TikTok Creator`,
  "aiTranslateCommentFour": `RecCloud enables us to add multilingual subtitles effortlessly, expanding our audience reach and making our content globally accessible.`,
  "aiTranslateCommentFouUser": `Sofia, Film Producer`,
  "aiTranslateFAQ": `FAQs About RecCloud AI Video Translator`,
  "aiTranslateOneQuestion": `How can I translate a video?`,
  "aiTranslateOneAnswer": `You can use RecCloud, one of the best AI video translation tools, which utilizes cutting-edge AI technology to easily translate both voiceovers and subtitles into any language.`,
  "aiTranslateTwoQuestion": `What is the best online video translator?`,
  "aiTranslateTwoAnswer": `When it comes to online video translators, RecCloud stands out as a top choice, utilizing advanced AI to provide accurate translations with a user-friendly interface, making video translation easy and efficient.`,
  "aiTranslateThreeQuestion": `What languages are supported for my video translation?`,
  "aiTranslateThreeAnswer": `RecCloud AI Video Translator supports a wide range of languages for translation, including major global languages such as English, Spanish, French, Chinese, German, Japanese, Korean, and many others.`,
  "aiTranslateFourQuestion": `Is RecCloud AI Video Translator free?`,
  "aiTranslateFourAnswer": `RecCloud offers a free trial that allows you to see the translated results. After the trial period, you will need to subscribe to continue using the service. You can check our Pricing plans.`,
  "aiTranslateFiveQuestion": `Is there a limit to the length or size of videos I can upload for translation?`,
  "aiTranslateFiveAnswer": `RecCloud supports video uploads with a maximum length of 3 hours and a file size of up to 4GB for translation.`,
  "aiTranslateToolsTitle": `Discover More Amazing AI Audio/Video Tools`,
  "aiTranslateLastTitle": `RecCloud AI Video Translator`,
  "aiTranslateLastDesc": `Let Your Videos Speak in Any Language!`,
  "aiTranslateToolsVideoGenerate": `AI Video Generator`,
  "aiTranslateToolsVideoGenerateDesc": `AI text to video`,
  "speechNewFindReplace": `Find & Replace`,
  "aiTranslateSpeakTitle": `Speaker distinguish`,
  "aiTranslateSpeakTitle1": `None`,
  "aiTranslateSpeakTitle2": `Auto`,
  "aiTranslateSpeakDesc": `Speaker distinction will match different voices.`,
  "aiTranslateSpeakVoiceSingleReplace": `Apply`,
  "aiTranslateSpeakVoiceAllReplace": `Apply All`,
  "aiTranslateTextToLongTitle": `Keep it under # characters for smoother voiceover!`,
  "limitCountNewDesc": `Limit of 5 free files reached. Upgrade or organize to continue.`,
  "limitDeleteNewFile": `Manage My Files`,
  "priceNoticeTranslate1": `Translation`,
  "priceNoticeTranslate2": `Generate`,
  "textToSpeechTitle1": `RecCloud AI Voice Generator -`,
  "textToSpeechTitle2": `Free Online <i>AI Text to Speech</i>`,
  "textToSpeechDesc1": `Convert text to voice in a precise & natural way.`,
  "textToSpeechDesc2": `Support various voices like male, female, dialects, and more.`,
  "textToSpeechDesc3": `Automatically differentiate text into multiple voice tones.`,
  "textToSpeechDesc4": `Easily translate into multiple languages.`,
  "multiDeleteCountTitle": `Selected <i>#</i> item(s)`,
  "startHistoryTitle": `AI Task History`,
  "multiVoiceMergeTitle": `Merge`,
  "multiVoiceAddTitle": `Add New`,
  "noVipCacheClearTitle": `Free account data is retained for 1 month.`,
  "noVipCacheClearBtnTitle": `Upgrade`,
  "dataRetentionTitle": `Data Retention`,
  "dataRetentionDesc1": `# month`,
  "dataRetentionDesc2": `Permanent`,
  "aiSubtitleTimestampTips": `Subtitle too short; show for at least 0.1s`,
  "aiSubtitleItemAdd": `Add Line`,
  "aiSubtitleItemDel": `Delete`,
  "aiSubtitleItemEdit": `Edit`,
  "fetchErrorTaskNoExist": `Task not found`,
  "recordNotFound": `Task not found`,
  "aiSubtitleTimestampTips2": `Timestamps can't overlap. Please revise.`,
  "aiTranslateKey1": `Video Translation`,
  "aiTranslateKey2": `Dubbing + Subtitles`,
  "aiTranslateKey3": `Subtitle Translation`,
  "aiTranslateKey4": `Subtitles only`,
  "aiTranslateKey5": `Transcription Translation`,
  "aiTranslateKey6": `Transcribed speech`,
  "aiTranslateKey7": `In development, stay tuned!`,
  "aiTranslateKey8": `Translation options`,
  "aiTranslateKey9": `Content updated. Please finish all changes before updating audio.`,
  "aiTranslateKey10": `Update`,
  "aiTranslateKey11": `Processing...`,
  "aiTranslateKey12": `Export`,
  "aiTranslateKey13": `Language: `,
  "aiTranslateKey14": `Translated`,
  "aiTranslateKey15": `Original`,
  "aiTranslateKey16": `Translated`,
  "aiTranslateKey17": `Original`,
  "aiTranslateKey18": `Bilingual`,
  "aiTranslateKey19": `No subtitles`,
  "aiTranslateKey20": `Subtitles`,
  "aiTranslateKey21": `Translated audio only`,
  "aiTranslateKey22": `Export video (MP4)`,
  "aiTranslateKey23": `Export audio (MP3)`,
  "aiTranslateKey24": `Export subtitles (SRT)`,
  "aiTranslateKey25": `Add`,
  "aiTranslateKey26": `Speaker Management`,
  "aiTranslateKey27": `Speaker#`,
  "aiTranslateKey28": `Processing...`,
  "aiTranslateKey29": `70+ languages supported`,
  "aiTranslateKey30": `Let AI take care of everything for you`,
  "aiTranslateKey31": `Speed up`,
  "aiTranslateKey32": `Got it`,
  "pricingBuyNowKey": `Buy Now`,
  "pricingExtendStorage": `Storage`,
  "pricingKey1": `Credit Usage <span class="text-primary-color">Rules</span>`,
  "pricingKey2": `Features`,
  "pricingKey3": `Rules`,
  "pricingKey4": `AI Speech-to-text`,
  "pricingKey5": `AI Video Translation`,
  "pricingKey6": `AI Subtitles`,
  "pricingKey7": `AI Text-to-Speech`,
  "pricingKey8": `AI Text-to-Video`,
  "pricingKey9": `AI Audio/Video Summary`,
  "pricingKey10": `Other`,
  "pricingKey11": `Transcription & Summary: 1 credit/min`,
  "pricingKey12": `Translation: 2 credits/task`,
  "pricingKey13": `Subtitles only: 2 credits/min`,
  "pricingKey14": `Audio + Subtitles: 8 credits/min`,
  "pricingKey15": `1 credit/min`,
  "pricingKey16": `Single voice: 1 credit/ 200 characters`,
  "pricingKey17": `Multi voice: 2 credits/ 200 characters`,
  "pricingKey18": `Text translation: 2 credits/task`,
  "pricingKey19": `Advanced model: 30 credits/video`,
  "pricingKey20": `Editing:`,
  "pricingKey21": `Text to video frame: 3 credits/frame`,
  "pricingKey22": `Frame to video: 30 credits/usage`,
  "pricingKey23": `Basic model: 10 credits/video`,
  "pricingKey24": `File(Local & Cloud): 1 credit/min`,
  "pricingKey25": `YouTube: 6 credits/usage`,
  "pricingKey26": `File download: 6 credits/GB`,
  "pricingKey27": ``,
  "aiTranslateKey33": `This project only includes translated subtitles and cann‘t export audio.`,
  "aiTranslateKey34": `Exporting, please wait.`,
  "aiTranslateKey35": `Done! Check it in your browser's download history.`,
  "aiTranslateKey36": `Content updated. Please finish all changes before updating audio.`,
  "aiTranslateKey37": `Done!`,
  "aiTranslateKey38": `Upgrading; this project cannot be edited now.`,
  "aiTranslateKey39": `Outdated`,
  "aiTranslateKey40": `Redo`,
  "aiTranslateKey41": `Please click 'Update' to modify your project due to a feature upgrade.`,
  "aiTranslateKey42": `Update`,
  "aiTranslateKey43": `Updating costs 6 credits/min.`,
  "aiTranslateKey44": `Update Now`,
  "pricingKey28": `Prices may change. Check the official website for updates.`,
  "aiTranslateKey45": `Step 1: AI translation of subtitles and audio.\nStep 2: Proofread and export.`,
  "aiTranslateKey46": `Update failed`,
  "pricingKey29": `Credits`,
  "pricingKey30": `Storage`,
  "pricingKey31": `File limit`,
  "pricingKey32": `Online recording duration`,
  "pricingKey33": `Data retention`,
  "pricingKey34": `Access AI features`,
  "pricingKey35": `Batch process`,
  "pricingKey36": `Free`,
  "pricingKey37": `0`,
  "pricingKey38": `1 min`,
  "pricingKey39": `Free trial`,
  "pricingKey40": `2,500/year\nOR\n100/week`,
  "pricingKey41": `8,800/year\nOR\n600/week`,
  "pricingKey42": `36,000/year`,
  "mobileTipsKey1": `Free Trial`,
  "mobileTipsKey2": `Unlock All AI Features`,
  "mobileTipsKey3": `Get APP`,
  "apiPricingKey1": `API Pricing`,
  "apiPricingKey2": `RecCloud API`,
  "apiPricingKey3": `API Doc`,
  "apiPricingKey4": `API Pricing Plans`,
  "apiPricingKey5": `My API Key`,
  "apiPricingKey6": `Need more? Get in touch →`,
  "apiPricingKey7": `2 QPS included. Need higher limits? <i>Let's scale →</i>`,
  "apiPricingKey8": `Contact Sales`,
  "apiPricingKey9": `API Operations`,
  "apiPricingKey10": `API Credit Usage Rules`,
  "apiPricingKey11": `Rules`,
  "apiPricingKey12": `Minimum charge: 1 minute or 200 characters.`,
  "apiPricingKey13": `Refunds available within 7 days of purchase. See our <a href='https://reccloud.com/refund-policy' class='text-primary-color'>Refund Policy</a> for details.`,
  "apiPricingKey14": `What are API Credits?`,
  "apiPricingKey15": `API Credits are used to access RecCloud API features. Example: Speech-to-text costs 1 credit/min, with a 1-min minimum.`,
  "apiPricingKey16": `What is QPS?`,
  "apiPricingKey17": `QPS (Queries Per Second) is the maximum tasks processed per second. <br>For example, QPS=2 allows 2 tasks per second. Please estimate your QPS needs based on user scale.`,
  "apiPricingKey18": `How do we protect your files?`,
  "apiPricingKey19": `Automatic Deletion: Files are permanently deleted within 60-75 minutes after processing.<br>Privacy Commitment: Your file content is never accessed or stored.<br>Proactive Support: Submit files for troubleshooting if results don't meet expectations.`,
  "apiPricingKey20": `How to request an invoice?`,
  "apiPricingKey21": `After completing your purchase, you can instantly download an electronic invoice from the payment platform. If you encounter any issues, please submit a ticket at (<a href='https://reccloud.com/contact' class='text-primary-color cursor-pointer'>https://reccloud.com/contact</a>) for assistance.`,
  "apiPricingKey22": `Where is data stored, and can it be used offline?`,
  "apiPricingKey23": `All data is stored on cloud servers and requires an internet connection.`,
  "apiPricingKey24": `What is the validity period of API credits?`,
  "apiPricingKey25": `Trial credits are valid for 1 month, while purchased credits are valid for 1 year from the date of purchase.`,
  "apiPricingKey26": `/year`,
  "apiPricingKey27": "RecCloud API Key",
  "apiDocKey1": `Quick Start`,
  "apiDocKey2": `About RecCloud API`,
  "apiDocKey3": `RecCloud offers professional AI audio and video processing API services, complete with standardized API documentation and a straightforward integration process. This enables developers and enterprise clients to quickly integrate leading global features such as AI audio and video transcription, text-to-speech, text generation for videos, and subtitle recognition. With standardized interfaces and a flexible, scalable architecture, developers can significantly reduce self-development costs by up to 90%, allowing them to focus on driving innovation in their core businesses.`,
  "apiDocKey4": `My API Key`,
  "apiDocKey5": `Notes`,
  "apiDocKey6": `1. The link returned by the interface is generally valid for 1 hour; please download and store it promptly.`,
  "apiDocKey7": `2. An HTTP status code of 200 indicates a successful HTTP request, not a successful conversion. For details, refer to the status code list.`,
  "apiDocKey8": `3. The API-KEY obtained for free has a QPS limit of 2; please do not exceed this limit to avoid affecting the normal use of the interface.`,
  "apiDocKey9": `Get`,
  "apiDocKey10": `FAQ`,
  "apiDocKey11": `API Credit Usage Rules`,
  "apiDocKey12": `Status Code`,
  "apiDocKey13": `Contact Us`,
  "apiDocKey14": `Q: What is the response speed of the service?`,
  "apiDocKey15": `A: The response speed depends on the file size and format, so an exact time cannot be provided. It is recommended to poll the task progress to get the latest status. If a task remains incomplete for an extended period, the system will automatically return a timeout error.`,
  "apiDocKey16": `Q: What is the concurrency limit (QPS) of the service?`,
  "apiDocKey17": `A: The default supported QPS is 2, with a parallel task limit of 2. To increase the QPS or parallel task limit, please contact the business team for expansion.`,
  "apiDocKey18": `Q: What are the file size and duration limits supported by each API function?`,
  "apiDocKey19": `A: Speech-to-text and AI Subtitle Generator: file size limit is no more than 4G, duration no more than 3 hours;<br/>Text-to-speech: input word limit is 30,000;<br/>Text-to-video: input word limit is 800.`,
  "apiDocKey20": `Q: How to handle task failures?`,
  "apiDocKey21": `A: Please first check the HTTP response status code and the state parameter description. If the issue persists, provide the error message, task_id, and complete request parameters to contact the business or technical team for assistance.`,
  "apiDocKey22": `Q: What to do if the URL download fails or shows a 403 Forbidden error?`,
  "apiDocKey23": `A: Ensure that the file can be downloaded properly and that there is sufficient download bandwidth. If the download is not completed within 1 hour, the system will report an error (status code: -3). A 403 error indicates no download permission; please check and ensure that the correct download permissions are provided.`,
  "apiDocKey24": `Q: What should I do if the sample code does not run properly?`,
  "apiDocKey25": `A: The sample code is for reference only; some parts need to be replaced based on actual conditions, such as techsz.aoscdn.com. Additionally, the example only shows simple parameters; for more supported parameters, please refer to the detailed parameter description table.`,
  "apiDocKey26": `Q: What to do if the URL download fails or shows an AccessDenied error?`,
  "apiDocKey27": `A: Some tools or editors may automatically convert & in the URL to \\u0026, for example, Expires=1679986124\\u0026OOSSAccessKeyId=. Please replace \\u0026 back to & to resolve the issue. Be sure to check for URL encoding issues.`,
  "apiDocKey28": `HTTP Response Status Codes`,
  "apiDocKey29": `status indicates the HTTP response status code, which is used to determine whether the request was successful, not whether the task was successful.`,
  "apiDocKey30": `Status Code`,
  "apiDocKey31": `Description`,
  "apiDocKey32": `Request successful.`,
  "apiDocKey33": `Client parameter error. Please check if parameters are missing or if their values are correct.`,
  "apiDocKey34": `Authentication failed. Please verify that the X-API-KEY is correct or if the service is activated.`,
  "apiDocKey35": `The requested URL or resource does not exist. Please check the URL or the task_id in the URL.`,
  "apiDocKey36": `Uploaded file exceeds size limit. Please check if the file size complies with service requirements.`,
  "apiDocKey37": `Request rate exceeds QPS limit. Please reduce the request rate or contact the business team to increase the QPS limit.`,
  "apiDocKey38": `Server error. Please contact the business or technical team to report the issue.`,
  "apiDocKey39": `Task Status Codes`,
  "apiDocKey40": `Task status codes are values of the state field in the HTTP response. Their specific meanings are as follows:`,
  "apiDocKey41": `Description`,
  "apiDocKey42": `No valid speech content detected`,
  "apiDocKey43": `Processing timeout (up to 10 minutes for speech recognition; up to 600 seconds for media services).`,
  "apiDocKey44": `Invalid file (The file is invalid (e.g., corrupted or incorrect format)`,
  "apiDocKey45": `Exceeds size limit (The file exceeds the size limit. Please refer to the file size requirements for each service.)`,
  "apiDocKey46": `Submission failed`,
  "apiDocKey47": `Download failed`,
  "apiDocKey48": `Upload failed`,
  "apiDocKey49": `Failed`,
  "apiDocKey50": `In queue`,
  "apiDocKey51": `Completed`,
  "apiDocKey52": `Preparing`,
  "apiDocKey53": `Waiting`,
  "apiDocKey54": `Processing`,
  "apiDocKey55": `Publishing`,
  "subtitleTranslatorKey1": `Free Online <i>AI Subtitle Translator</i>`,
  "subtitleTranslatorKey2": `Accurately translate subtitles for movies, TV shows, courses, short films, and social media videos into multiple languages with AI-powered technology. Perfect for global content creators and businesses.`,
  "subtitleTranslatorKey3": `Start for Free`,
  "subtitleTranslatorKey4": `Support for 99+ Languages – Translate Subtitles Globally`,
  "subtitleTranslatorKey5": `RecCloud AI-powered subtitle translator supports over 99 languages, including English, Spanish, French, Chinese, Japanese, and more. Want to break language barriers and reach a global audience? We've got you covered!`,
  "subtitleTranslatorKey6": `Industry-Leading Accuracy`,
  "subtitleTranslatorKey7": `Experience near-human translation quality powered by advanced AI and machine learning. RecCloud Subtitle Translator ensures precise translations, even for complex dialogues, technical terms, or slang.`,
  "subtitleTranslatorKey8": ` Fast & Efficient Translation`,
  "subtitleTranslatorKey9": `Save time with RecCloud lightning-fast subtitle editor. Translate subtitles for a 1-hour video in just minutes, not hours. Perfect for tight deadlines or high-volume projects.`,
  "subtitleTranslatorKey10": `Edit & Customize Subtitles`,
  "subtitleTranslatorKey11": `After translation, easily edit and customize subtitles to match your style. Adjust timing, font, and formatting to create a polished final product.`,
  "subtitleTranslatorKey12": `<i>People Love</i> RecCloud AI Subtitle Translator`,
  "subtitleTranslatorKey13": `I used to manually time subtitles for my travel vlogs—total nightmare. This tool not only translated my Spanish dialogues to English in seconds, but the timing matched the lip movements perfectly. Even slang came through right!`,
  "subtitleTranslatorKey14": `Maria, Travel Vlogger`,
  "subtitleTranslatorKey15": `I've used several subtitle translators, but this one stands out! The accuracy is incredible, and it supports so many languages. It saved me hours of work on my latest documentary. Highly recommend it to any filmmaker!`,
  "subtitleTranslatorKey16": `Harry, Independent Filmmaker`,
  "subtitleTranslatorKey17": `I run a small YouTube channel teaching coding. The auto-translate feature for Hindi to English subtitles is crazy accurate, especially with technical terms. Plus, the ‘edit as captions’ option saved me from re-uploading the whole video!`,
  "subtitleTranslatorKey18": `Priya, Tech Educator`,
  "subtitleTranslatorKey19": `My team needed Chinese subtitles for a last-minute client webinar. We uploaded the video at 2 AM, got the translated subtitles with timecodes by breakfast. The client thought we hired a pro translator—they had no idea it was AI!`,
  "subtitleTranslatorKey20": `Sarah, Marketing Agency Lead`,
  "subtitleTranslatorKey21": `FAQs About RecCloud AI Subtitle Translator`,
  "subtitleTranslatorKey22": `How do I use the Subtitle Translator tool?`,
  "subtitleTranslatorKey23": `First, upload your video file. Next, choose the target language you want to translate into. Thirdly, click the "Translate" button and let our AI work its magic. Within minutes, you'll have a fully translated subtitle file for you to edit or download.`,
  "subtitleTranslatorKey24": `What is the best online subtitle translator?`,
  "subtitleTranslatorKey25": `RecCloud offers advanced AI-powered translations with a user-friendly interface, making it one of the top choices for high-quality subtitle translation.`,
  "subtitleTranslatorKey26": `What video formats are supported?`,
  "subtitleTranslatorKey27": `RecCloud supports a variety of popular video formats, including mp4, mov, mkv, and webm.`,
  "subtitleTranslatorKey28": `Is RecCloud AI Subtitle Translator totally free?`,
  "subtitleTranslatorKey29": `RecCloud offers a free trial that allows you to translate subtitles for up to 5 minutes. This trial lets you experience the tool's capabilities before deciding if you want to upgrade to one of our subscription plans.`,
  "subtitleTranslatorKey30": `Is there a limit to the length or size of videos I can upload for translation?`,
  "subtitleTranslatorKey31": `RecCloud supports video uploads with a maximum length of 3 hours and a file size of up to 4GB for translation.`,
  "subtitleTranslatorKey32": `RecCloud AI Subtitle Translator`,
  "subtitleTranslatorKey33": `Translate Subtitles, Connect Globally!`,
  "headerToolsSubtitleTransTitle": `AI Subtitle Translator`,
  "headerToolsSubtitleTransDesc": `Change video subtitles to different languages.`,
  "apiDocKey56": `More Status Codes`,
  "apiDocKey57": `Languages`,
  "apiDocKey58": `Code`,
  "apiDocKey62": `Mode (Voice Library) <br/>1: Classic Voice <br/>2: Extended Voice`,
  "apiDocKey63": `Language`,
  "apiDocKey64": `Voice<br/>Male: [Male 1, Male 2, ...] <br/> Female: [Female 1, Female 2, ...]`,
  "apiDocKey65": `*Includes all voices from 1, along with the extended voices on the right.`,
  "speechToTextApiTitleKey": 'AI Speech to Text',
  "speechToTextApiDescKey": 'AI quickly converts audio and video into text with high accuracy using advanced AI models. It also supports translation into major global languages.',
  "speechToTextApiCreateTaskKey": `1.1 Create Task`,
  "speechToTextApiQueryTaskKey": `1.2 Query Results`,
  "speechToTextApiRequestKey1": 'Mode: Enter 4',
  "speechToTextApiRequestKey2": 'File URL: Must be a resolvable HTTP URL. If the URL lacks a file extension, specify extension, e.g., extension=mp3. Max 512 characters. Download timeout is 5 minutes (failure if not completed within 5 minutes).',
  "speechToTextApiRequestKey3": 'File Extension: Required if the URL lacks a file extension.',
  "speechToTextApiRequestKey4": 'Audio Language: Optional. Defaults to automatic detection. <span class="active-text" id="speech-to-text-create-support-lang-id">Supported languages</span> are listed in the appendix.',
  "speechToTextApiRequestKey5": `Speaker Recognition: Detect different speakers, 0 - no (default), 1 - yes.`,
  "speechToTextApiRequestKey6": 'Content Type: Enter 1',
  "speechToTextApiRequestKey7": 'Task ID: For querying results.',
  "speechToTextApiResponseKey1": 'Task ID: Needed for progress tracking.',
  "speechToTextApiResponseKey4": 'Task ID',
  "speechToTextApiResponseKey6": 'Processing Time (Timestamp)',
  "speechToTextApiResponseKey7": 'Completion Time (Timestamp)',
  "speechToTextApiResponseKey8": 'Progress: 0-100',
  "speechToTextApiResponseKey9": 'File Duration: In seconds',
  "speechToTextApiResponseKey10": 'Source Language: Detected from the first 30 seconds',
  "speechToTextApiResponseKey11": 'Output Language: Based on user input',
  "speechToTextApiResponseKey12": 'Recognized Content',
  "aiSubtitleApiTitleKey": 'AI Subtitle Generator',
  "aiSubtitleApiDescKey": `AI generates timestamped subtitles with high accuracy using top AI models. It also supports translations in multiple languages.<br/> 1. Generate Subtitles: Creates timestamped subtitles in JSON format.<br/> 2. Synthesize Video: Adds subtitles to a video and returns it as an MP4 file.`,
  "aiSubtitleApiCreateTaskKey": `1.1 Generate Subtitles (Create Task)`,
  "aiSubtitleApiQueryTaskKey": `1.2 Generate Subtitles (Query Results)`,
  "aiSubtitleApiRequestKey1": 'Mode: Enter 4',
  "aiSubtitleApiRequestKey2": 'File URL: The URL of the file must be a resolvable HTTP protocol URL. If the URL path lacks a file extension, specify the extension parameter, e.g., extension=mp3. Maximum of 512 characters. Download timeout is 5 minutes (failure if not completed within 5 minutes).',
  "aiSubtitleApiRequestKey3": 'File Extension: Required if the URL path lacks a file extension.',
  "aiSubtitleApiRequestKey4": `The output subtitle language is optional. By default, the system detects the audio and video languages automatically. You can specify a language for subtitle translation.<span class=active-text id=speech-to-text-create-support-lang-id>Supported languages</span> .`,
  "aiSubtitleApiRequestKey5": `Speaker Recognition: Detect different speakers, 0 - do not recognize (default), 1 - recognize.`,
  "aiSubtitleApiRequestKey6": 'Task ID: Used to query task results.',
  "aiSubtitleApiResponseKey1": 'Task ID: Required for subsequent progress queries.',
  "aiSubtitleApiResponseKey2": 'Task Status: <0: Failed; 0: Queued; 1: Completed; 4: In Progress. For details, please check the <button class="active-text status-class">Status Code</button>.',
  "aiSubtitleApiResponseKey3": 'Status Details: For example, Complete',
  "aiSubtitleApiResponseKey4": 'Task ID',
  "aiSubtitleApiResponseKey5": 'Creation Time (Timestamp)',
  "aiSubtitleApiResponseKey6": 'Processing Time (Timestamp)',
  "aiSubtitleApiResponseKey7": 'Completion Time (Timestamp)',
  "aiSubtitleApiResponseKey8": 'Progress: Range 0-100',
  "aiSubtitleApiResponseKey9": 'File Duration: In seconds',
  "aiSubtitleApiResponseKey10": 'Source Language of File: Detected from the first 30 seconds of the file',
  "aiSubtitleApiResponseKey11": 'Output Language of Results: Based on the user\'s specified language',
  "aiSubtitleApiResponseKey12": 'Recognized Content from File: Each array element contains start (segment start time in milliseconds), end (segment end time in milliseconds), and text (recognized text content).',
  "aiSubtitleApiResponseKey13": 'Segment start time in milliseconds',
  "aiSubtitleApiResponseKey14": 'Segment end time in milliseconds',
  "aiSubtitleApiResponseKey15": 'Recognized text content',
  "textToSpeechApiTitleKey": 'AI Text to Speech',
  "textToSpeechApiDescKey": 'AI quickly converts text into ultra-realistic speech. Supports various voice types, including male, female, child, formal, and other voices. It also supports multiple languages, including Spanish, French, German, Portuguese, Japanese and more.',
  "textToSpeechApiCreateTaskKey": `1.1 Create Task`,
  "textToSpeechApiQueryTaskKey": `1.2 Query Results`,
  "textToSpeechApiRequestKey1": 'Text to be Synthesized\nChoose between text and srt, with text having higher priority.',
  "textToSpeechApiRequestKey2": 'Subtitles to be Synthesized\nChoose between text and srt, with text having higher priority.',
  "textToSpeechApiRequestKey3": 'Start Time\nIn milliseconds',
  "textToSpeechApiRequestKey4": 'End Time\nIn milliseconds',
  "textToSpeechApiRequestKey5": `Your text`,
  "textToSpeechApiRequestKey6": 'Pronunciation Mode\n1: Normal tone\n2: Enhanced tone',
  "textToSpeechApiRequestKey7": 'Download Filename upon Task Completion\nRequired, must start with wx-tts',
  "textToSpeechApiRequestKey8": `Voice: Default is Xiaoyun<br/> *Voices are matched based on the selected language. Click to view the <button class="active-text" id="text-to-speech-support-lang-id">supported voices</button>.`,
  "textToSpeechApiRequestKey9": 'Volume: Range is 0-100, default is 50',
  "textToSpeechApiRequestKey10": 'Speech Rate: Range is -500 to 500, default is 0',
  "textToSpeechApiRequestKey11": `Voice Language (based on the selected mode) <br/> You can find the specific languages in the table below.`,
  "textToSpeechApiRequestKey12": 'Audio Encoding Format\nDefault is mp3, aac is optional',
  "textToSpeechApiRequestKey13": 'Task ID: Used to query task results.',
  "textToSpeechApiResponseKey1": 'Task ID: Required for subsequent progress queries.',
  "textToSpeechApiResponseKey4": 'Task ID',
  "textToSpeechApiResponseKey6": 'Processing Time (Timestamp)',
  "textToSpeechApiResponseKey7": 'Completion Time (Timestamp)',
  "textToSpeechApiResponseKey8": 'Progress: Range 0-100',
  "textToSpeechApiResponseKey9": 'File Duration: In seconds',
  "textToSpeechApiResponseKey10": 'File URL',
  "textToSpeechApiResponseKey11": 'Filename',
  "textToVideoApiTitleKey": `AI Text to Video<nav-hidden>(Basic Model)</nav-hidden>`,
  "textToVideoApiDescKey": 'Generate stunning montage videos with one-click by inputting prompt text, automatically adding voiceover, subtitles, and background music. Suitable for content creation, advertising, and other scenarios.',
  "textToVideoApiCreateTaskKey": `1.1 Create Task`,
  "textToVideoApiQueryTaskKey": `1.2 Query Results`,
  "textToVideoApiRequestKey1": 'Video Generation Theme: Specify a title to automatically generate text. Choose between video_script and video_subject.',
  "textToVideoApiRequestKey2": 'Custom Text: Specify text. Choose between video_script and video_subject.',
  "textToVideoApiRequestKey3": `Video Aspect Ratio. Options: 0 - 16:9 (default), 1 - 9:16`,
  "textToVideoApiRequestKey4": `Voice type, used for video dubbing.<br/>* Please select the voice corresponding to video_script/video_subject. Click to view <button class=active-text id=text-to-video-create-support-voice-id>supported voices</button>.`,
  "textToVideoApiRequestKey5": `Background Music Type: Options: 0-none (default), 1-random, 2-specified background music`,
  "textToVideoApiRequestKey6": `The URL of the Uploaded File. Required when bgm_type is 2.`,
  "textToVideoApiRequestKey7": `Enable Subtitles: Default is enabled`,
  "textToVideoApiRequestKey8": `Subtitle Position: Options: 0-bottom, 1-center, 2-top (default)`,
  "textToVideoApiRequestKey9": `Font Type ID: Options: 0-andalemo (default), 1-open sans, 2-comic, 3-impat, 4-times new roman, 5-SimSun, 6-SimHei, 7-KaiTi, 8-LiShu, 9-Microsoft YaHei`,
  "textToVideoApiRequestKey10": `Subtitle Font Size: default is 60`,
  "textToVideoApiRequestKey11": `Subtitle Font Color: default is #FFF`,
  "textToVideoApiRequestKey12": `Subtitle Border Color: default is #000`,
  "textToVideoApiRequestKey13": `Subtitle Border Width: default is 1`,
  "textToVideoApiRequestKey14": 'Task ID: Used to query task results.',
  "textToVideoApiResponseKey1": 'Task ID: Required for subsequent progress queries.',
  "textToVideoApiResponseKey2": 'Task ID: Used to identify specific video generation tasks.',
  "textToVideoApiResponseKey4": 'Task Start Processing UNIX Timestamp',
  "textToVideoApiResponseKey5": 'Task Completion UNIX Timestamp',
  "textToVideoApiResponseKey8": 'Task Completion Progress Percentage: Range 0-100',
  "textToVideoApiResponseKey9": 'Generated Video File URL',
  "aiTranslateKey47": `Changing the voice will update all audio and incur charges based on duration (Price per min: # credits).`,
  "aiTranslateKey48": `Audio updates will incur charges based on duration (Price per min: # credits).`,
  "aiTranslateKey49": `First update is free.`,
  "aiTranslateKey50": `Subtitles modified. Tap to update the audio (best to update after all changes).`,
  "pricingKey43": `Dubbing + Subtitles(single voice): 8 credits/min`,
  "pricingKey44": `Dubbing + Subtitles(multi-voice): 10 credits/min`,
  "pricingKey45": `Voice cloning: 20 credits/min`,
  "pricingKey46": `Retranslate: 4 credits/min`,
  "pricingKey47": `OR`,
  "pricingKey48": `Re-subscribing to Pro extends your membership and credits, but storage cannot be combined. Upgrade or buy separately for more storage.`,
  "aiTranslateApiKey1": `File URL: The file URL must be a valid HTTP URL. If the URL lacks a file extension, specify the extension parameter (e.g., extension=mp3). The maximum length is 512 characters. The download timeout is 5 minutes (failure if not completed in that time). The file duration is 3 hours and size is 4GB.`,
  "aiTranslateApiKey2": `AI Video Translation`,
  "aiTranslateApiKey3": `AI video translation supports over 70 languages and provides one-stop services for subtitle translation and voiceover. It's efficient, accurate, and convenient for all video translation needs. <br/> 1. Video Translation: Extracts and translates subtitles, returning them in JSON format. <br/> 2. Video Synthesis: Creates translated audio and renders a video, delivering an MP4 file with audio and subtitles.`,
  "aiTranslateApiKey4": `1.1 Video Translation (Create Task)`,
  "aiTranslateApiKey5": `Target language: <span class="active-text" id="speech-to-text-create-support-lang-id">supported languages</span>`,
  "aiTranslateApiKey6": `1.2 Video Translation (Check Results)`,
  "aiTranslateApiKey7": `2.1 Video Synthesis (Create Task)`,
  "aiTranslateApiKey8": `Subtitles`,
  "aiTranslateApiKey9": `Generate subtitles? 0 - No, 1 - Yes (default)`,
  "aiTranslateApiKey10": `Subtitle style`,
  "aiTranslateApiKey11": `Alignment: 0 - Left, 2 - Center (default), 3 - Right`,
  "aiTranslateApiKey12": `Background color (rgba): Default black, 80% opacity (0,0,0,0.8)`,
  "aiTranslateApiKey13": `Enable background: true (enabled, default), false (disabled)`,
  "aiTranslateApiKey14": `Enable text outline: true (enabled), false (disabled, default)`,
  "aiTranslateApiKey15": `Bold text: true (enabled), false (disabled, default)`,
  "aiTranslateApiKey16": `Font color (rgba), default pure white and opaque (255,255,255,1)`,
  "aiTranslateApiKey17": `Italic text: true (enabled), false (disabled, default)`,
  "aiTranslateApiKey18": `Font size: unit in pixels, default 20`,
  "aiTranslateApiKey19": `Underline: true (enabled), false (disabled, default)`,
  "aiTranslateApiKey20": `Outline color (rgba), default pure white and opaque (255,255,255,1)`,
  "aiTranslateApiKey21": `Outline width: unit in pixels, default 0`,
  "aiTranslateApiKey22": `Bottom vertical distance: unit in percentage, default 0`,
  "aiTranslateApiKey23": `Export file name`,
  "aiTranslateApiKey24": `2.2 Video Synthesis (Check Results)`,
  "aiTranslateApiKey25": `Export file download link`,
  "aiTranslateApiKey26": `Subtitle File URL`,
  "pricingKey50": `Video Translation`,
  "pricingKey51": `Video Synthesis`,
  "pricingKey52": `Generate Subtitles`,
  "aiSubtitleDetect": `Detect Subtitles`,
  "aiSubtitleTranslate": `Translate Subtitles`,
  "aiSubtitleGenerateNow": `Generate Now`,
  "pricingKey53": `Detect: 1 credit/min`,
  "pricingKey54": `Translate: 2 credits/min`,
  "headerToolsYoutubeSummarizerTitle": `AI YouTube Video Summarizer`,
  "headerToolsYoutubeSummarizerDesc": `Extract key points from YouTube videos`,
  "aiYoutubeSummarizerTitle": `<i>YouTube</i> Video Summarizer`,
  "aiYoutubeSummarizerDesc": `Transcribe and summarize YouTube videos in seconds.`,
  "aiYoutubeSummarizerInputPlaceholder": `Paste a YouTube URL here`,
  "aiYoutubeSummarizerStartTaskBtnTitle": `Summarize`,
  "aiYoutubeSummarizerStepTitle": `How to Summarize YouTube Videos`,
  "aiYoutubeSummarizerStepDesc1": `Copy YouTube link`,
  "aiYoutubeSummarizerStepDesc2": `Grab the URL of any video you want summarized.`,
  "aiYoutubeSummarizerStepDesc3": `Paste your link`,
  "aiYoutubeSummarizerStepDesc4": `Paste the link into the box above.`,
  "aiYoutubeSummarizerStepDesc5": `Get summary`,
  "aiYoutubeSummarizerStepDesc6": `Click 'Summarize' to see key points in seconds!`,
  "aiYoutubeSummarizerChooseTitle": `Why Choose RecCloud YouTube Video Summarizer`,
  "aiYoutubeSummarizerChooseDesc1": `Easy to Use`,
  "aiYoutubeSummarizerChooseDesc2": `No install—just paste & go`,
  "aiYoutubeSummarizerChooseDesc3": `Efficient`,
  "aiYoutubeSummarizerChooseDesc4": `Skip the fluff, get the point.`,
  "aiYoutubeSummarizerChooseDesc5": `Accurate`,
  "aiYoutubeSummarizerChooseDesc6": `Reliable summaries by AI`,
  "aiYoutubeSummarizerChooseDesc7": `Multiple Languages`,
  "aiYoutubeSummarizerChooseDesc8": `Summarizes videos in multiple languages`,
  "aiYoutubeSummarizerChooseStartBtnTitle": `Try Now`,
  "aiYoutubeSummarizerUserTitle": `YouTube Summarizer Loved by Over 100,000 Users`,
  "aiYoutubeSummarizerUserDesc1": `This tool saves me hours! As an analyst, I need quick insights—it delivers accurate summaries in seconds. Game-changer for research.`,
  "aiYoutubeSummarizerUserDesc2": `Alex, Data Analyst`,
  "aiYoutubeSummarizerUserDesc3": `Perfect for lectures! Cuts my study time in half by highlighting key points. The summaries are surprisingly detailed. Lifesaver!`,
  "aiYoutubeSummarizerUserDesc4": `Priya, Medical Student`,
  "aiYoutubeSummarizerUserDesc5": `Summarizes client videos in a click. No more wasting time on irrelevant content. Our team’s productivity has skyrocketed!`,
  "aiYoutubeSummarizerUserDesc6": `James, Marketing Manager`,
  "aiYoutubeSummarizerUserDesc7": `I use this daily for video research. The AI nails the main ideas every time. Super intuitive—no learning curve at all!`,
  "aiYoutubeSummarizerUserDesc8": `Sophie, Freelance Writer`,
  "aiYoutubeSummarizerQATitle": `Frequently asked questions`,
  "aiYoutubeSummarizerQADesc1": `What types of YouTube videos can be summarized?`,
  "aiYoutubeSummarizerQADesc2": `RecCloud supports summarization for standard YouTube videos (public or unlisted). However, live streams, members-only content, and age-restricted videos are not currently supported.`,
  "aiYoutubeSummarizerQADesc3": `Is YouTube video summarization free to use?`,
  "aiYoutubeSummarizerQADesc4": `The free plan includes 3 video summaries (up to 20 minutes each). Additional summaries or longer videos require a Premium upgrade.`,
  "aiYoutubeSummarizerQADesc5": `Do I need to download the YouTube video first?`,
  "aiYoutubeSummarizerQADesc6": `Nope! Just copy and paste the YouTube URL—no downloads needed.`,
  "aiYoutubeSummarizerQADesc7": `Does summarization work with YouTube Shorts?`,
  "aiYoutubeSummarizerQADesc8": `No, YouTube Shorts are often too brief (under 60 seconds) for meaningful summarization.`,
  "aiYoutubeSummarizerQADesc9": `Can I edit or export the summary?`,
  "aiYoutubeSummarizerQADesc10": `Yes! You can copy the text or download it as a file for further editing.`,
  "aiYoutubeSummarizerReadyTitle": `Ready to Summarize Your YouTube Video?`,
  "aiYoutubeSummarizerReadyDesc": `Summarize your first video with RecCloud now!`,
  "aiYoutubeSummarizerInputErrorText": `Oops! Please enter a valid YouTube URL.`,
  "aiYoutubeSummarizerTaskLoadingText": `Processing... (1 min left).\nFeel free to leave - we're saving your work!`,
  "aiYoutubeSummarizerResultText1": `Summary`,
  "aiYoutubeSummarizerResultText2": `Transcript`,
  "aiYoutubeSummarizerResultText3": `Copy`,
  "aiYoutubeSummarizerResultText4": `Download`,
  "aiYoutubeSummarizerResultFail": `Summary failed. Please try again.`,
  "aiYoutubeSummarizerResultFailReason1": `You've used your # free summaries. Upgrade to continue.`,
  "aiYoutubeSummarizerResultFailReason2": `The trial supports videos up to # mins. Upgrading allows for a full summary or the first # mins only.`,
  "aiYoutubeSummarizerResultFailReason3": `The maximum supported video length is # hours. Please choose another.`,
  "aiYoutubeSummarizerStartTaskBtnLoadingTitle": `Processing...`,
  "aiYoutubeSummarizerResultFailReason4": `The maximum supported video length without CC subtitles is # hours. Please choose another.`,
  "pricingKey55": `Detect`,
  "priceISOTitle": `Your Data <i>Security</i> Guaranteed`,
  "priceISODesc": `RecCloud meets international security standards with ISO/IEC 27001, ISO/IEC 27701 certifications and full GDPR compliance. Our advanced encryption protocols ensure your data remains protected throughout every step of processing.`,
  "priceISODesc1": `ISO/IEC Security Management`,
  "priceISODesc2": `ISO/IEC Privacy Management`,
  "priceISODesc3": `General Data Protection Regulation`,
  "headerToolsYoutubeSubtitleTitle": `AI YouTube Transcript Generator`,
  "headerToolsYoutubeSubtitleDesc": `Get the transcript of the YouTube video`,
  "aiYoutubeSubtitleTitle": `<i>YouTube</i> Transcript Generator`,
  "aiYoutubeSubtitleDesc": `Get a YouTube video transcript instantly by entering the link—no uploads required.`,
  "aiYoutubeSubtitleInputPlaceholder": `Paste a YouTube URL here`,
  "aiYoutubeSubtitleStartTaskBtnTitle": `Get Transcript`,
  "aiYoutubeSubtitleStepTitle": `How to Get the Transcript of a YouTube Video`,
  "aiYoutubeSubtitleStepDesc1": `Copy YouTube link`,
  "aiYoutubeSubtitleStepDesc2": `Grab the URL of any video you want to get the transcript for.`,
  "aiYoutubeSubtitleStepDesc3": `Paste your link`,
  "aiYoutubeSubtitleStepDesc4": `Paste the link into the box above.`,
  "aiYoutubeSubtitleStepDesc5": `Get Transcript`,
  "aiYoutubeSubtitleStepDesc6": `Click 'Get Transcript' to obtain YouTube subtitles.`,
  "aiYoutubeSubtitleChooseTitle": `Why Choose RecCloud YouTube Transcript Generator`,
  "aiYoutubeSubtitleChooseDesc1": `Easy to Use`,
  "aiYoutubeSubtitleChooseDesc2": `No install—just paste & go`,
  "aiYoutubeSubtitleChooseDesc3": `Efficient`,
  "aiYoutubeSubtitleChooseDesc4": `Extracts subtitles with one click`,
  "aiYoutubeSubtitleChooseDesc5": `Accurate`,
  "aiYoutubeSubtitleChooseDesc6": `Ensures precise text extraction`,
  "aiYoutubeSubtitleChooseDesc7": `Multiple Languages`,
  "aiYoutubeSubtitleChooseDesc8": `Various languages supported for global reach`,
  "aiYoutubeSubtitleChooseStartBtnTitle": `Try Now`,
  "aiYoutubeSubtitleUserTitle": `Transcript Generator Loved by Over <i>100,000 Users</i>`,
  "aiYoutubeSubtitleUserDesc1": `The transcript generator has saved me so much time! I can now focus on creating more content instead of manually typing out subtitles.`,
  "aiYoutubeSubtitleUserDesc2": `Johnson, Video Creator`,
  "aiYoutubeSubtitleUserDesc3": `As a marketer, accurate transcripts are essential for SEO. This tool makes it incredibly easy to generate subtitles that boost my video's reach!`,
  "aiYoutubeSubtitleUserDesc4": `Sarah, Marketing Specialist`,
  "aiYoutubeSubtitleUserDesc5": `I use this generator for my online courses. The transcripts help my students follow along easily, and it's super user-friendly!`,
  "aiYoutubeSubtitleUserDesc6": `Brian, Teacher`,
  "aiYoutubeSubtitleUserDesc7": `This tool is a game changer! I can quickly generate transcripts for my clients' videos, making it easier to engage their audience on multiple platforms.`,
  "aiYoutubeSubtitleUserDesc8": `Emily, Social Manager`,
  "aiYoutubeSubtitleQATitle": `FAQ`,
  "aiYoutubeSubtitleQADesc1": `What is the RecCloud YouTube Transcript Generator?`,
  "aiYoutubeSubtitleQADesc2": `RecCloud's YouTube Transcript Generator is a tool that automatically generates text transcripts from YouTube videos, streamlining subtitle creation and improving viewer accessibility.`,
  "aiYoutubeSubtitleQADesc3": `How accurate are the YouTube transcripts generated by RecCloud?`,
  "aiYoutubeSubtitleQADesc4": `RecCloud aims for high accuracy in YouTube transcripts, though results may vary depending on audio clarity and accents.`,
  "aiYoutubeSubtitleQADesc5": `Does RecCloud support multiple languages for YouTube video transcripts?`,
  "aiYoutubeSubtitleQADesc6": `Yes, RecCloud generates accurate transcripts in the video's original language and supports translation into various languages for user convenience.`,
  "aiYoutubeSubtitleQADesc7": `How long does it typically take to generate a transcript?`,
  "aiYoutubeSubtitleQADesc8": `Transcript generation typically takes about 1 minute, depending on video length and system performance.`,
  "aiYoutubeSubtitleQADesc9": `Is there a cost to access RecCloud YouTube transcript generator?`,
  "aiYoutubeSubtitleQADesc10": `The free plan includes 3 transcriptions (up to 20 minutes each). After the trial, a premium subscription is required to access advanced features.`,
  "aiYoutubeSubtitleReadyTitle": `Ready to Transcribe Your YouTube Videos?`,
  "aiYoutubeSubtitleReadyDesc": `Transcribe your first video with RecCloud now!`,
  "aiYoutubeSubtitleResultText4": `Download Transcript`,
  "aiYoutubeSubtitleResultText5": `Original`,
  "aiYoutubeSubtitleResultText6": `Auto detect`,
  "aiYoutubeSubtitleResultText7": `Target language`,
  "aiYoutubeSubtitleResultFail": `Transcript failed, please try again.`,
  "aiYoutubeSubtitleResultText8": `Translate to`,
  "column1Title": `Transcription`,
  "column1Item1": `Transcribe Audio to Text`,
  "column1Item2": `Transcribe Video to Text`,
  "column1Item3": `Medical Transcription`,
  "column1Item4": `Transcribe YouTube Video`,
  "column1Item5": `Zoom Transcription`,
  "column2Title": `Translator`,
  "column2Item1": `Subtitle Translator`,
  "column2Item2": `Translate YouTube Video`,
  "column2Item3": `Translate Spanish Video to English`,
  "column2Item4": `Translate Japanese Video to English`,
  "column2Item5": `Movie Translator`,
  "column3Title": `Summarizer`,
  "column3Item1": `YouTube Video Summarizer`,
  "column3Item2": `Audio Summarizer`,
  "column3Item3": `Meeting Summarizer`,
  "column3Item4": `Transcript Summarizer`,
  "column3Item5": `Lecture Summarizer`,
  "column4Title": `Generator`,
  "column4Item1": `AI Subtitle Generator`,
  "column4Item2": `AI Cartoon Generator`,
  "column4Item3": `AI Animation Video Generator`,
  "column4Item4": `AI Marketing Video Generator`,
  "column4Item5": `AI Training Video Generator`,
  "headerTabTitle1": `Products`,
  "headerTabTitle2": `Solutions`,
  "aiYoutubeSubtitleStartTaskBtnLoadingTitle": `Generating`,
  "aiYoutubeSubtitleResultFailReason1": `You've used all your free transcripts. Upgrade to continue.`,
  "aiYoutubeSubtitleResultFailReason2": `Free trial transcripts just the first # mins; upgrade for full transcription.`,
  "aiYoutubeSubtitleResultFailReason3": `The maximum supported video length is # hours. Please choose another.`,
  "aiYoutubeHistoryTitle": `History`,
  "headerToolsYoutubeTranslateTitle": `AI YouTube Video Translator`,
  "headerToolsYoutubeTranslateDesc": `Translate YouTube videos to your preferred language.`,
  "aiYoutubeTranslateTitle": `<i>YouTube</i> Video Translator`,
  "aiYoutubeTranslateDesc": `Translate YouTube subtitles into multiple languages for global accessibility.`,
  "aiYoutubeTranslateInputPlaceholder": `Paste a YouTube URL here`,
  "aiYoutubeTranslateStartTaskBtnTitle": `Translate Video`,
  "aiYoutubeTranslateStepTitle": `How to Translate YouTube Videos`,
  "aiYoutubeTranslateStepDesc1": `Copy & Paste YouTube link`,
  "aiYoutubeTranslateStepDesc2": `Enter the YouTube video URL you want to translate.`,
  "aiYoutubeTranslateStepDesc3": `Select language`,
  "aiYoutubeTranslateStepDesc4": `Select the target language
                                  from dropdown menu.`,
  "aiYoutubeTranslateStepDesc5": `Get Translation`,
  "aiYoutubeTranslateStepDesc6": `Click 'Translate Video' and download translated YouTube subtitles.`,
  "aiYoutubeTranslateChooseTitle": `Why Choose RecCloud Video Translator`,
  "aiYoutubeTranslateChooseDesc1": `Easy to Use`,
  "aiYoutubeTranslateChooseDesc2": `No install—just paste & translate`,
  "aiYoutubeTranslateChooseDesc3": `Efficient`,
  "aiYoutubeTranslateChooseDesc4": `One-click YouTube translation`,
  "aiYoutubeTranslateChooseDesc5": `Accurate`,
  "aiYoutubeTranslateChooseDesc6": `Context-aware translations with 95% accuracy`,
  "aiYoutubeTranslateChooseDesc7": `Multiple Languages`,
  "aiYoutubeTranslateChooseDesc8": `Translates YouTube in multiple languages`,
  "aiYoutubeTranslateChooseStartBtnTitle": `Try Now`,
  "aiYoutubeTranslateUserTitle": `YouTube Video Translator Loved by Over <i>100,000 Users</i>`,
  "aiYoutubeTranslateUserDesc1": `As a Brazilian student in Germany, this tool is perfect for translating lecture videos from English to Portuguese. Just paste the YouTube link and get perfect subtitles in seconds - it's helped me understand complex topics so much better!`,
  "aiYoutubeTranslateUserDesc2": `Emma, International Student`,
  "aiYoutubeTranslateUserDesc3": `As an ESL instructor, I use this daily to create bilingual subtitles for my students. The accuracy with idioms is impressive - saves me hours of manual work!`,
  "aiYoutubeTranslateUserDesc4": `David, Language Teacher`,
  "aiYoutubeTranslateUserDesc5": `Our agency manages YouTube channels in 8 languages. This translator saves us hours every week - we just input the video link and get accurate, formatted subtitles ready to publish. The Spanish translations are particularly natural.`,
  "aiYoutubeTranslateUserDesc6": `Ryan, Digital Marketer`,
  "aiYoutubeTranslateUserDesc7": `I create bilingual teaching materials from YouTube videos. The side-by-side translation feature (original + target language) is exactly what I need for my students. So much faster than doing it manually!`,
  "aiYoutubeTranslateUserDesc8": `Lina, Language Tutor`,
  "aiYoutubeTranslateQATitle": `FAQ`,
  "aiYoutubeTranslateQADesc1": `What languages does RecCloud YouTube Translator support?`,
  "aiYoutubeTranslateQADesc2": `RecCloud supports a wide range of languages, from widely spoken ones like Spanish, Chinese, French, and Arabic to lesser-known dialects—helping you access global content in your preferred language.`,
  "aiYoutubeTranslateQADesc3": `How accurate are the translations?`,
  "aiYoutubeTranslateQADesc4": `RecCloud uses advanced AI to provide highly accurate translations, with continuous improvements to handle context, slang, and cultural nuances.`,
  "aiYoutubeTranslateQADesc5": `Can RecCloud translate videos in real-time?`,
  "aiYoutubeTranslateQADesc6": `Currently, RecCloud provides translation after you submit a YouTube link. Real-time translation may be considered for future updates, depending on user demand and technical feasibility.`,
  "aiYoutubeTranslateQADesc7": `Do I need to download software to use RecCloud YouTube Video Translator?`,
  "aiYoutubeTranslateQADesc8": `No, RecCloud YouTube Video Translator is a web-based platform, so you can use it directly in your browser without downloading. It works on major browsers like Chrome, Firefox, and Safari.`,
  "aiYoutubeTranslateQADesc9": `Can RecCloud translate YouTube videos without subtitles?`,
  "aiYoutubeTranslateQADesc10": `Absolutely! Our AI translates directly from the audio track, whether subtitles exist or not. (Note: Requires clear speech for best results).`,
  "aiYoutubeTranslateReadyTitle": `Ready to Translate Your YouTube videos?`,
  "aiYoutubeTranslateReadyDesc": `Translate your first video with RecCloud now!`,
  "aiYoutubeTranslateInputErrorText": `Oops! Please enter a valid YouTube URL.`,
  "aiYoutubeTranslateTaskLoadingText": `Processing... (1 min left).\nFeel free to leave - we're saving your work!`,
  "aiYoutubeTranslateResultText4": `Download subtitles`,
  "aiYoutubeTranslateResultFail": `Translation failed, please try again.`,
  "aiYoutubeTranslateResultFailReason1": `You've used all your free translations. Upgrade to continue.`,
  "aiYoutubeTranslateResultFailReason2": `Free trial translates just the first # mins; upgrade for full translation.`,
  "aiYoutubeTranslateResultFailReason3": `The maximum supported video length is # hours. Please choose another.`,
  "pricingTabTitle1": `PRO`,
  "pricingTabTitle2": `Pay as you go`,
  "aiAudioToTextOnlineTitle": `Free online Al Transcribe <i>Audio to Text</i>`,
  "aiAudioToTextOnlineDesc": `Convert audio to text in seconds with AI—accurate, secure & multilingual.`,
  "aiAudioToTextOnlineDragInputTitle": `Drag and Drop files to upload or`,
  "aiAudioToTextOnlineUploadFileBtnTitle": `Browse`,
  "aiAudioToTextOnlineSupportFmt": `Supported formats: mp3/mp4/mov/webm/wav... (and 14+ more)`,
  "aiAudioToTextOnlineEnableSpeakerTitle": `Identify Speakers`,
  "aiAudioToTextOnlineEnableSpeakerDesc": `Price per min`,
  "aiAudioToTextOnlineStepTitle": `Convert Audio to Text in 3 Steps`,
  "aiAudioToTextOnlineStepDesc1": `Upload`,
  "aiAudioToTextOnlineStepDesc2": `Upload your audio file`,
  "aiAudioToTextOnlineStepDesc3": `Transcribe`,
  "aiAudioToTextOnlineStepDesc4": `Convert audio to text automatically`,
  "aiAudioToTextOnlineStepDesc5": `Preview & Get Results`,
  "aiAudioToTextOnlineStepDesc6": `Copy text or download transcript`,
  "aiAudioToTextOnlineChooseTitle": `Why Choose RecCloud Audio Transcription?`,
  "aiAudioToTextOnlineChooseDesc1": `Accuracy`,
  "aiAudioToTextOnlineChooseDesc2": `98% transcription accuracy, even with accents.`,
  "aiAudioToTextOnlineChooseDesc3": `Multilingual`,
  "aiAudioToTextOnlineChooseDesc4": `Supports 100+ languages including English, Spanish & Chinese & more`,
  "aiAudioToTextOnlineChooseDesc5": `Privacy`,
  "aiAudioToTextOnlineChooseDesc6": `100% Private: Your data is never leaked, sold or shared`,
  "aiAudioToTextOnlineChooseDesc7": `Easy to Use`,
  "aiAudioToTextOnlineChooseDesc8": `Simple - Upload & get transcripts in seconds`,
  "aiAudioToTextOnlineChooseStartBtnTitle": `Try Now`,
  "aiAudioToTextOnlineUserTitle": `AI Audio Transcription Loved by Over <i>100,000 Users</i>`,
  "aiAudioToTextOnlineUserDesc1": `My production team switched to RecCloud last quarter. What used to take 3 hours of editing now takes 45 minutes! The automatic punctuation and 'um' removal feature is magic. We've processed 200+ episodes flawlessly.`,
  "aiAudioToTextOnlineUserDesc2": `Sarah, Podcast Producer`,
  "aiAudioToTextOnlineUserDesc3": `Client confidentiality is non-negotiable in my field. RecCloud's military-grade encryption and auto-delete policy meet our compliance requirements. The deposition transcripts require minimal corrections - worth every penny.`,
  "aiAudioToTextOnlineUserDesc4": `James, Corporate Lawyer`,
  "aiAudioToTextOnlineUserDesc5": `Between classes and clinical rotations, I don't have time for manual transcriptions. This tool has become essential for my studies - it handles complex medical terminology better than others I've tested.`,
  "aiAudioToTextOnlineUserDesc6": `Michael, Medical Student`,
  "aiAudioToTextOnlineUserDesc7": `Perfect for transcribing 2-hour lectures with complex terminology. I upload while walking to my next class, and the transcript with timestamps is ready before office hours. The student accessibility team loves it!`,
  "aiAudioToTextOnlineUserDesc8": `Aisha, University Lecturer`,
  "aiAudioToTextOnlineQATitle": `FAQ`,
  "aiAudioToTextOnlineQADesc1": `Does RecCloud identify different speakers?`,
  "aiAudioToTextOnlineQADesc2": `Yes! RecCloud's AI automatically identifies and labels different speakers - perfect for transcribing meetings, interviews, and group discussions.`,
  "aiAudioToTextOnlineQADesc3": `Which languages do you support?`,
  "aiAudioToTextOnlineQADesc4": `Our service covers 100+ languages including English, Spanish, Mandarin, French, and German, with dialect recognition for major variants.`,
  "aiAudioToTextOnlineQADesc5": `What audio formats do you accept?`,
  "aiAudioToTextOnlineQADesc6": `We support 19 file formats including MP3, M4A, WAV, MOV, and WEBM. Video files are automatically processed for transcription.`,
  "aiAudioToTextOnlineQADesc7": `Is RecCloud audio transcription free?`,
  "aiAudioToTextOnlineQADesc8": `We offer a 10-minute free transcription trial, with flexible subscription options afterwards.`,
  "aiAudioToTextOnlineQADesc9": `Are my audio files private with RecCloud?`,
  "aiAudioToTextOnlineQADesc10": `Your audio is protected by end-to-end encryption with enterprise-grade security protocols. As a GDPR/ISO compliant service, we enforce strict zero-retention policies and never share data with third parties.`,
  "aiAudioToTextOnlineReadyTitle": `Ready to transcribe your audio?`,
  "aiAudioToTextOnlineReadyDesc": `Transcribe your first audio with RecCloud now!`,
  "aiAudioToTextOnlineResultFailReason": `Speaker identification supports audios under 1 hour. Please try a shorter file.`,
  "aiAudioToTextOnlineResultFailReason1": `You've used your # free transcriptions. Upgrade to continue.`,
  "aiAudioToTextOnlineResultFailReason2": `Unlogged users: # mins file limit. Log in for full processing.`,
  "aiAudioToTextOnlineFreeBtnTitle": `Free trial`,
  "aiAudioToTextOnlineResultFailReason4": `Pricing: New accounts get # free trials, then % credits per minute.`,
  "aiAudioToTextOnlineResultTitle1": `AI polished`,
  "aiAudioToTextOnlineResultTitle2": `Summary`,
  "aiAudioToTextOnlineResultTitle3": `Transcript`,
  "priceKey3DaysFreeTitle": `3-Day Free Trial`,
  "pricingDialogPickTitle": `Pick Your <i>Plan</i>`,
  "pricingDialogPickDesc1": `All features? Just one tap away.`,
  "pricingDialogSeeDetailTitle": `See all plans →`,
  "aiSubtitleExportOnlyVipCanDoTitle": `Export/Download: Members only. Upgrade to access.`,
  "textToSpeechNoLoginOnlyCharsTitle": `Guest limit: # chars. Login for full access →`,
  "costPointCountsTitle": `Estimated cost: # credits`,
  "dialogOnlyVipTitle": `Your free trial is used up. Upgrade to continue.`,
  "noPointNoVipToUpgradeTitle": `Not enough credits. Upgrade to proceed.`,
  "pricingDialogTab1": `Plans`,
  "pricingDialogTab2": `Credits`,
  "pricingDialogTab3": `Storage`,
  "RecCloudAI": 'RecCloud AI',
  "aiSubtitleNoTrialCountTitle": `Guests: 10-min source language only. <br/> Unlimited languages with login.`,
  "zoomTranscriptionKey1": `AI-Powered <i>Zoom Meeting Transcription</i>`,
  "zoomTranscriptionKey2": `Convert Zoom meetings to text in seconds—highly accurate, secure, and speaker-identified.`,
  "zoomTranscriptionKey3": `How to Transcribe Zoom Meetings Easily`,
  "zoomTranscriptionKey4": `Upload Recording`,
  "zoomTranscriptionKey5": `Upload a Zoom meeting recording file`,
  "zoomTranscriptionKey6": `Customize Settings`,
  "zoomTranscriptionKey7": `Select speaker identification or language preferences`,
  "zoomTranscriptionKey8": `Get Transcript`,
  "zoomTranscriptionKey9": `Copy text directly or download your transcript for easy sharing`,
  "zoomTranscriptionKey10": `Why Choose RecCloud Zoom Transcription?`,
  "zoomTranscriptionKey11": `95%+ Accuracy`,
  "zoomTranscriptionKey12": `AI transcription with smart corrections for terms & accents.`,
  "zoomTranscriptionKey13": `Speaker Identification`,
  "zoomTranscriptionKey14": `Auto-detects speakers for clear meeting notes.`,
  "zoomTranscriptionKey15": `User-friendly & Secure`,
  "zoomTranscriptionKey16": `No installation needed with end-to-end encryption.`,
  "zoomTranscriptionKey17": `Multilingual`,
  "zoomTranscriptionKey18": `Supports 100+ languages for Zoom transcripts.`,
  "zoomTranscriptionKey19": `Zoom Transcription Trusted by <i>100,000 Professionals</i>`,
  "zoomTranscriptionKey20": `I record client consultations on Zoom, and manually transcribing them was exhausting. With RecCloud, I get accurate, searchable transcripts in minutes—huge time-saver for my law practice!"`,
  "zoomTranscriptionKey21": `Rachel, Attorney`,
  "zoomTranscriptionKey22": `As a project manager, I host weekly Zoom meetings with global teams. RecCloud’s transcripts save me hours—I get perfectly formatted notes with speaker labels, making follow-ups a breeze!`,
  "zoomTranscriptionKey23": `David, Project Manager`,
  "zoomTranscriptionKey24": `I run a podcast where I interview guests on Zoom. RecCloud turns our conversations into clean, editable text so I can quickly pull quotes for show notes. Lifesaver!"`,
  "zoomTranscriptionKey25": `Nina, Podcast Host`,
  "zoomTranscriptionKey26": `Being a non-native English speaker, I sometimes miss details in Zoom lectures. RecCloud’s transcripts help me review everything clearly—it’s like having a personal note-taker!"`,
  "zoomTranscriptionKey27": `Carlos, MBA Student`,
  "zoomTranscriptionKey28": `FAQ`,
  "zoomTranscriptionKey29": `How accurate is RecCloud’s Zoom transcription?`,
  "zoomTranscriptionKey30": `Our AI delivers 95%+ accuracy for clear audio. (Note: Transcription quality may be affected by background noise in recordings)`,
  "zoomTranscriptionKey31": `Does RecCloud support speaker identification?`,
  "zoomTranscriptionKey32": `Yes! It auto-labels speakers (e.g., "Speaker 1," "Speaker 2") for multi-person meetings.`,
  "zoomTranscriptionKey33": `Are my Zoom files secure?`,
  "zoomTranscriptionKey34": `At RecCloud, we encrypt all files and maintain full GDPR/ISO compliance for your security.`,
  "zoomTranscriptionKey35": `Which languages do you support?`,
  "zoomTranscriptionKey36": `RecCloud supports 100+ languages, covering English, Spanish, French, German, Mandarin, Japanese and more.`,
  "zoomTranscriptionKey37": `Do you offer a free trial?`,
  "zoomTranscriptionKey38": `Yes! Get started with RecCloud's free Zoom meeting transcription. When you're ready for more, upgrade to unlock full features and unlimited usage.`,
  "zoomTranscriptionKey39": `Ready to Transcribe Your Zoom Meetings?`,
  "zoomTranscriptionKey40": `Get accurate transcripts in seconds—try RecCloud for free today!`,
  "medicalTranscriptionKey1": `AI-powered <i>Medical Transcription</i>`,
  "medicalTranscriptionKey2": `Instantly transcribe medical conversations and notes.`,
  "medicalTranscriptionKey3": `How RecCloud Medical Transcription Works`,
  "medicalTranscriptionKey4": `Upload Audio/Video`,
  "medicalTranscriptionKey5": `Drag and drop patient recordings`,
  "medicalTranscriptionKey6": `AI Transcribe`,
  "medicalTranscriptionKey7": `AI transcribes clinical conversations accurately`,
  "medicalTranscriptionKey8": `Review & Export`,
  "medicalTranscriptionKey9": `Copy text or download transcript`,
  "medicalTranscriptionKey10": `Why Choose RecCloud Medical Transcription`,
  "medicalTranscriptionKey11": `Accuracy`,
  "medicalTranscriptionKey12": `AI trained on 500K+ medical dialogues for precise terminology capture.`,
  "medicalTranscriptionKey13": `Instant`,
  "medicalTranscriptionKey14": `60x faster than manual transcription - Process 10-min recordings in 1 minute.`,
  "medicalTranscriptionKey15": `Multispeaker Detection`,
  "medicalTranscriptionKey16": `Automatically labels speakers for clear documentation.`,
  "medicalTranscriptionKey17": `Easy to Use`,
  "medicalTranscriptionKey18": `Upload recordings—get accurate medical notes in minutes. No setup needed.`,
  "medicalTranscriptionKey19": `Medical Transcription Trusted by Over <i>100,000 Users</i>`,
  "medicalTranscriptionKey20": `Our nursing staff uses RecCloud to transcribe post-op instructions. It’s cut documentation errors by 30% and integrates perfectly with our EHR.`,
  "medicalTranscriptionKey21": `Sarah, RN, Surgical Unit`,
  "medicalTranscriptionKey22": `As a busy cardiologist, I dictate 50+ patient notes daily. RecCloud’s transcripts save me 2 hours/day, and the accuracy with medical terms like ‘tachycardia’ is flawless.`,
  "medicalTranscriptionKey23": `James, MD, Cardiology`,
  "medicalTranscriptionKey24": `As a solo practitioner, I needed affordable transcription. RecCloud’s AI is as reliable as my old $5/minute service—but 80% cheaper.`,
  "medicalTranscriptionKey25": `Emily, Family Physician`,
  "medicalTranscriptionKey26": `For my medical billing company, accuracy is non-negotiable. RecCloud’s transcripts match the audio 100%, reducing claim denials.`,
  "medicalTranscriptionKey27": `Michael, Medical Billing Specialist`,
  "medicalTranscriptionKey28": `FAQ`,
  "medicalTranscriptionKey29": `How accurate is RecCloud for complex terms?`,
  "medicalTranscriptionKey30": `Our AI achieves 99% accuracy for specialties (e.g., orthopedics, oncology) via a proprietary medical lexicon.`,
  "medicalTranscriptionKey31": `Does RecCloud support rapid speech dictation?`,
  "medicalTranscriptionKey32": `Our AI is trained on diverse speech patterns for speech speed (e.g., ER doctors’ rapid dictation).`,
  "medicalTranscriptionKey33": `What’s the turnaround time?`,
  "medicalTranscriptionKey34": `Typically 1/10 the audio length (e.g., 10-minute audio = 1 minute processing). Final speed depends on your connection and server demand.`,
  "medicalTranscriptionKey35": `How secure are my files?`,
  "medicalTranscriptionKey36": `All audio and transcripts are encrypted in transit and at rest with enterprise-grade security.`,
  "medicalTranscriptionKey37": `Is there a free trial?`,
  "medicalTranscriptionKey38": `Yes! Try RecCloud's medical transcription for free—no credit card required. Upgrade anytime for full features.`,
  "medicalTranscriptionKey39": `Ready for Smarter Medical Transcription?`,
  "medicalTranscriptionKey40": `Begin your medical transcription journey with RecCloud today!`,
  "aiHelpNoPointTitle": `Upgrade for unlimited AI writing`,
  "legalTranscriptionKey1": `Free Online AI <i>Legal Transcription</i>`,
  "legalTranscriptionKey2": `Real-time, accurate, and secure transcription for legal professionals.`,
  "legalTranscriptionKey3": `Legal Transcription in 3 Simple Steps`,
  "legalTranscriptionKey4": `Upload Audio/Video`,
  "legalTranscriptionKey5": `Drag & drop legal recordings in any format`,
  "legalTranscriptionKey6": `AI Transcribe`,
  "legalTranscriptionKey7": `Verbatim conversion with automatic speaker identification`,
  "legalTranscriptionKey8": `Review & Export`,
  "legalTranscriptionKey9": `Download formatted transcripts or copy directly`,
  "legalTranscriptionKey10": `Why RecCloud AI Legal Transcription?`,
  "legalTranscriptionKey11": `Courtroom-Level Accuracy`,
  "legalTranscriptionKey12": `Trained on 500K+ legal dialogues with 95%+ term recognition`,
  "legalTranscriptionKey13": `60x Faster`,
  "legalTranscriptionKey14": `10-minute audio → 1-minute transcript (avg.)`,
  "legalTranscriptionKey15": `Multi-Speaker Tracking`,
  "legalTranscriptionKey16": `Clear speaker labels for depositions & hearings`,
  "legalTranscriptionKey17": `No Training Required`,
  "legalTranscriptionKey18": `Get courtroom-ready transcripts in 3 clicks`,
  "legalTranscriptionKey19": `Trusted by <i>100,000+</i> Legal Professionals`,
  "legalTranscriptionKey20": `Game-changer! We integrated RecCloud with our case management system, cutting document prep time by 30%.`,
  "legalTranscriptionKey21": `Ethan | Defense Attorney`,
  "legalTranscriptionKey22": `As a judge, I process 50+ hours of recordings weekly. RecCloud's legal terminology recognition is unparalleled.`,
  "legalTranscriptionKey23": `Ramirez | District Judge`,
  "legalTranscriptionKey24": `From crime scene notes to client meetings, I get searchable transcripts before my coffee cools.`,
  "legalTranscriptionKey25": `Nia | Paralegal`,
  "legalTranscriptionKey26": `The only AI that keeps up with my corporate merger negotiations - worth every penny.`,
  "legalTranscriptionKey27": `Vikram | M&A Counsel`,
  "legalTranscriptionKey28": `FAQ`,
  "legalTranscriptionKey29": `How accurate is RecCloud for legal terms?`,
  "legalTranscriptionKey30": `Trained on 500K+ legal dialogues with 95%+ accuracy – handles Latin phrases, case law jargon, and fast-paced courtroom speech.`,
  "legalTranscriptionKey31": `Can it keep up with rapid legal discussions?`,
  "legalTranscriptionKey32": `Yes. Designed to keep up with rapid speech, such as witness examinations and objections, with adaptive real-time processing.`,
  "legalTranscriptionKey33": `What’s the turnaround time?`,
  "legalTranscriptionKey34": `Typically 1/10 audio length (e.g., 10min file ≈ 1min process). Depends on audio quality and speaker clarity.`,
  "legalTranscriptionKey35": `Is my data safe?`,
  "legalTranscriptionKey36": `Absolutely! All audio files and transcripts are protected with enterprise-grade encryption during transmission and storage.`,
  "legalTranscriptionKey37": `Do you offer a free trial?`,
  "legalTranscriptionKey38": `Yes! RecCloud AI Legal Transcription offers a free trial, with the option to upgrade for additional needs.`,
  "legalTranscriptionKey39": `Don't wait—try RecCloud AI Legal Transcription now!`,
  "legalTranscriptionKey40": `Accurate and fast, protecting every moment of legal communication!`,
  "iPhoneSpeechToTextKey1": `AI-Powered iPhone <i>Voice to Text</i>`,
  "iPhoneSpeechToTextKey2": `Precise iPhone voice transcription in 99+ languages with AI summaries.`,
  "iPhoneSpeechToTextKey3": `How to Convert iPhone Voice to Text`,
  "iPhoneSpeechToTextKey4": `Upload Audio`,
  "iPhoneSpeechToTextKey5": `Click to upload or drag and drop files, supports MP4/WAV/MP3 and other formats`,
  "iPhoneSpeechToTextKey6": `AI Transcribe`,
  "iPhoneSpeechToTextKey7": `Accurate iPhone voice recognition with speaker identification support`,
  "iPhoneSpeechToTextKey8": `Review & Export`,
  "iPhoneSpeechToTextKey9": `Copy and download transcript with one click`,
  "iPhoneSpeechToTextKey10": `Why Choose RecCloud iPhone Voice to Text?`,
  "iPhoneSpeechToTextKey11": `Accuracy`,
  "iPhoneSpeechToTextKey12": `98% ultra-high accuracy rate, accurately transcribes professional terms and complex scenarios.`,
  "iPhoneSpeechToTextKey13": `Multilingual`,
  "iPhoneSpeechToTextKey14": `Supports 99 languages (Chinese / English / Japanese / French) and mixed-language conversations.`,
  "iPhoneSpeechToTextKey15": `Secure`,
  "iPhoneSpeechToTextKey16": `Enterprise-grade encryption for all data transfers and storage.`,
  "iPhoneSpeechToTextKey17": `Optimized`,
  "iPhoneSpeechToTextKey18": `AI-powered formatting and polishing for professional-quality text.`,
  "iPhoneSpeechToTextKey19": `iPhone Voice Transcription Trusted by <i>19,000+ Users</i>`,
  "iPhoneSpeechToTextKey20": `This iPhone transcription is scary accurate! I record lectures all day and it nails even complex medical terms.`,
  "iPhoneSpeechToTextKey21": `Brianna | Nursing Student`,
  "iPhoneSpeechToTextKey22": `As a contractor, I do 20+ site reports daily. This thing saves me 2 hours - catches all my construction jargon perfectly.`,
  "iPhoneSpeechToTextKey23": `Devin | General Contractor`,
  "iPhoneSpeechToTextKey24": `Game changer for podcast editing. The speaker IDs are so precise, I cut post-production time in half.`,
  "iPhoneSpeechToTextKey25": `Marcus | Audio Producer`,
  "iPhoneSpeechToTextKey26": `My kids scream in the background but this still gets every word right. Teacher conferences just got 100x easier.`,
  "iPhoneSpeechToTextKey27": `Tasha | PTA Mom`,
  "iPhoneSpeechToTextKey28": `FAQ`,
  "iPhoneSpeechToTextKey29": `How to use RecCloud iPhone voice-to-text feature?`,
  "iPhoneSpeechToTextKey30": `Upload any audio file - our AI instantly converts speech to text and generates concise summaries. Simply review, then copy or download your transcript.`,
  "iPhoneSpeechToTextKey31": `What is the best iPhone voice-to-text tool?`,
  "iPhoneSpeechToTextKey32": `Without a doubt, it's RecCloud iPhone voice-to-text, with accurate recognition and fast conversion.`,
  "iPhoneSpeechToTextKey33": `What audio formats does iPhone voice-to-text support?`,
  "iPhoneSpeechToTextKey34": `Supports all major formats: MP3, MP4, MOV, WEBM, WAV, and 14+ others for maximum compatibility.`,
  "iPhoneSpeechToTextKey35": `Is iPhone voice-to-text completely free?`,
  "iPhoneSpeechToTextKey36": `We offer a free trial—experience full iPhone voice-to-text conversion instantly!`,
  "iPhoneSpeechToTextKey37": `Is RecCloud iPhone voice to text safe?`,
  "iPhoneSpeechToTextKey38": `Absolutely. Your audio is end-to-end encrypted and we never share anything with third parties — ever.`,
  "iPhoneSpeechToTextKey39": `Try RecCloud iPhone Voice to Text today!`,
  "iPhoneSpeechToTextKey40": `Boost your productivity with AI-powered, hands-free convenience!`,
  "recordingToTextKey1": `AI <i>Transcribe Audio Recording to Text</i>`,
  "recordingToTextKey2": `98% ultra-high accuracy, effortlessly converting meetings, interviews, lectures, and more.`,
  "recordingToTextKey3": `Transcribe Recording to Text in 3 Quick Steps`,
  "recordingToTextKey4": `Upload Audio`,
  "recordingToTextKey5": `Supports 10+ formats like MP3/WAV/M4A. One-click upload from PC or mobile.`,
  "recordingToTextKey6": `AI Transcribe`,
  "recordingToTextKey7": `AI automatically distinguishes speakers and summarizes key content.`,
  "recordingToTextKey8": `Export`,
  "recordingToTextKey9": `Copy, download, or share transcribed text with ease.`,
  "recordingToTextKey10": `Why Choose RecCloud for Audio Transcription?`,
  "recordingToTextKey11": `Ultra-High Accuracy`,
  "recordingToTextKey12": `Professional terms and multi-speaker dialogues—all accurately recognized.`,
  "recordingToTextKey13": `Multilingual Support`,
  "recordingToTextKey14": `Supports 100+ languages, including mixed speech.`,
  "recordingToTextKey15": `Enterprise-Grade Security`,
  "recordingToTextKey16": `Military-grade encryption and GDPR-compliant data handling.`,
  "recordingToTextKey17": `AI Enhancement`,
  "recordingToTextKey18": `Automatically formatted, publication-ready transcripts.`,
  "recordingToTextKey19": `Voice Recording Transcription Loved by <i>100,000+</i> Users`,
  "recordingToTextKey20": `I spend 10+ hours weekly transcribing interviews. RecCloud saves me 70% time—its accuracy is amazing!`,
  "recordingToTextKey21": `Emily, Journalist`,
  "recordingToTextKey22": `As a lawyer, accuracy is crucial. RecCloud handles legal terms flawlessly—highly recommended!`,
  "recordingToTextKey23": `James, Attorney`,
  "recordingToTextKey24": `Team meetings with RecCloud cut minutes-taking from 2 hours to 20 minutes. My boss praised my efficiency!`,
  "recordingToTextKey25": `Sophia, HR Manager`,
  "recordingToTextKey26": `Lecture transcriptions are a game-changer! Auto-generated summaries double my study efficiency.`,
  "recordingToTextKey27": `Ryan, Student`,
  "recordingToTextKey28": `FAQ`,
  "recordingToTextKey29": `What's the accuracy of RecCloud?`,
  "recordingToTextKey30": `Our AI delivers 98% accuracy for Mandarin, with specialized term support and bilingual recognition. We continuously improve our algorithms.`,
  "recordingToTextKey31": `Which languages and file formats do you support?`,
  "recordingToTextKey32": `We support 100+ languages (including Chinese, English, Japanese, Korean) and all major formats: MP3, WAV, M4A, and more.`,
  "recordingToTextKey33": `How can I export transcripts?`,
  "recordingToTextKey34": `Instant copy, one-click Word download, or secure link sharing.`,
  "recordingToTextKey35": `Is RecCloud available on mobile?`,
  "recordingToTextKey36": `Yes! Use it on any browser (mobile/PC) or download our apps for advanced features.`,
  "recordingToTextKey37": `Is my audio recording secure?`,
  "recordingToTextKey38": `Encrypted transmission, strict privacy compliance—zero data leaks.`,
  "recordingToTextKey39": `Transcribe Recordings to Text with RecCloud`,
  "recordingToTextKey40": `Skip Manual Typing - Get AI Transcripts in Minutes!`,
  "teamTranscriptionKey1": `AI-Powered <i>Teams Meeting</i> Transcriber`,
  "teamTranscriptionKey2": `Transcribe Teams meetings in seconds—AI-powered, accurate, and secure.`,
  "teamTranscriptionKey3": `Transcribe Teams Meetings in 3 Steps`,
  "teamTranscriptionKey4": `Upload`,
  "teamTranscriptionKey5": `Import your Microsoft Teams meeting file.`,
  "teamTranscriptionKey6": `AI Transcribe`,
  "teamTranscriptionKey7": `Automatically transcribe Teams meetings.`,
  "teamTranscriptionKey8": `Export`,
  "teamTranscriptionKey9": `Review the accurate transcript, then copy or export.`,
  "teamTranscriptionKey10": `Why Choose RecCloud for Teams Transcription?`,
  "teamTranscriptionKey11": `Accuracy`,
  "teamTranscriptionKey12": `Achieve up to 98% transcription accuracy and speaker identification with our cutting-edge AI model.`,
  "teamTranscriptionKey13": `Multilingual`,
  "teamTranscriptionKey14": `MP4, MOV, MP3, WAV, and more, with 100+ languages—fully compatible with Teams transcription.`,
  "teamTranscriptionKey15": `Secure & Private`,
  "teamTranscriptionKey16": `All files are encrypted, and no one can access them without your permission.`,
  "teamTranscriptionKey17": `Easy to Use`,
  "teamTranscriptionKey18": `No software to install—upload and transcribe in a few clicks.`,
  "teamTranscriptionKey19": `AI Transcription Trusted by over <i>100,000 Teams Users</i>`,
  "teamTranscriptionKey20": `RecCloud makes it easy to keep track of everything discussed in our HR meetings. No more manual note-taking!`,
  "teamTranscriptionKey21": `Sarah, HR Specialist`,
  "teamTranscriptionKey22": `As a product manager, I need quick access to accurate meeting notes. RecCloud helps me transcribe team meetings effortlessly—it’s a huge time-saver.`,
  "teamTranscriptionKey23": `James, Product Manager`,
  "teamTranscriptionKey24": `I’m a marketing coordinator juggling multiple team meetings. RecCloud’s transcription tool helps me summarize and share key takeaways easily.`,
  "teamTranscriptionKey25": `Emily, Marketing Coordinator`,
  "teamTranscriptionKey26": `Being a remote developer, I often miss live calls. This tool lets me catch up through clean transcripts. Super efficient!`,
  "teamTranscriptionKey27": `Daniel, Software Developer`,
  "teamTranscriptionKey28": `FAQ`,
  "teamTranscriptionKey29": `Do I need to install anything?`,
  "teamTranscriptionKey30": `No installation needed—RecCloud's Teams Transcription works entirely online.`,
  "teamTranscriptionKey31": `Is my data safe?`,
  "teamTranscriptionKey32": `Yes. All files on RecCloud are encrypted with enterprise-grade security.`,
  "teamTranscriptionKey33": `Does it support multiple speakers?`,
  "teamTranscriptionKey34": `Yes, RecCloud AI can detect and label different speakers.`,
  "teamTranscriptionKey35": `What languages are supported?`,
  "teamTranscriptionKey36": `RecCloud supports 100+ languages, including English, Spanish, German, French, Chinese, and more.`,
  "teamTranscriptionKey37": `Can I use RecCloud for free?`,
  "teamTranscriptionKey38": `Of course! RecCloud offers a free trial, so you can try our Microsoft Teams transcription online. If you want unlimited access to all functions, you can upgrade for more.`,
  "teamTranscriptionKey39": `Ready to Transcribe Your Teams Meetings?`,
  "teamTranscriptionKey40": `Get your first Teams transcription now!`,
  "aiNoteTakerKey1": `Smart <i>AI Note Taker</i> for Instant Transcription`,
  "aiNoteTakerKey2": `Turn meetings & lectures into organized notes with AI—auto-capture key points and insights.`,
  "aiNoteTakerKey3": `How to Use RecCloud AI Notetaker?`,
  "aiNoteTakerKey4": `Upload`,
  "aiNoteTakerKey5": `Add your meeting/lecture audio or video file.`,
  "aiNoteTakerKey6": `Process`,
  "aiNoteTakerKey7": `Automatically transcribe and organize content.`,
  "aiNoteTakerKey8": `Review & Share`,
  "aiNoteTakerKey9": `One-click export and sharing of formatted notes.`,
  "aiNoteTakerKey10": `Why Choose RecCloud AI Notetaker?`,
  "aiNoteTakerKey11": `Searchable Archives`,
  "aiNoteTakerKey12": `Find any information from past meetings using powerful search functionality.`,
  "aiNoteTakerKey13": `Multi-Speaker Recognition`,
  "aiNoteTakerKey14": `Accurately identify and label different speakers in your meetings.`,
  "aiNoteTakerKey15": `Privacy Focused`,
  "aiNoteTakerKey16": `End-to-end encryption ensures that your sensitive meeting data stays secure.`,
  "aiNoteTakerKey17": `Time-Saving`,
  "aiNoteTakerKey18": `Capture and organize notes as conversations happen—no waiting or delays.`,
  "aiNoteTakerKey19": `AI Notetaker Trusted by <i>100,000+ Users</i>`,
  "aiNoteTakerKey20": `In my medical practice, accurate documentation is critical. This AI note taker captures patient consultations perfectly, allowing me to focus entirely on my patients instead of writing notes. It's HIPAA-compliant and incredibly accurate with medical terminology.`,
  "aiNoteTakerKey21": `Sarah, Physician`,
  "aiNoteTakerKey22": `As a project manager handling multiple teams, RecCloud's AI Notetaker has been a game-changer. I can finally participate fully in meetings while capturing every important detail and action item. My team productivity has increased dramatically.`,
  "aiNoteTakerKey23": `Michael, IT Project Manager`,
  "aiNoteTakerKey24": `Running a startup means endless meetings with investors and team members. RecCloud's AI Notetaker ensures that I never miss critical feedback or ideas. The summary feature helps me quickly review what matters most without rewatching recordings.`,
  "aiNoteTakerKey25": `Lyra, Startup Founder`,
  "aiNoteTakerKey26": `Teaching online courses means managing a lot of content. RecCloud helps me create perfect lecture notes for my students automatically. The topic organization feature is brilliant for structuring educational materials.`,
  "aiNoteTakerKey27": `Brian, University Professor`,
  "aiNoteTakerKey28": `FAQ`,
  "aiNoteTakerKey29": `How Accurate is RecCloud's AI Note Taker?`,
  "aiNoteTakerKey30": `RecCloud's note-taking AI achieves over 98% accuracy in most professional settings, continually improving through machine learning from millions of meetings.`,
  "aiNoteTakerKey31": `Is My Meeting Information Secure?`,
  "aiNoteTakerKey32": `RecCloud employs bank-level encryption and is GDPR and HIPAA compliant. Your data is never used for training our models without your explicit permission.`,
  "aiNoteTakerKey33": `Can I Edit the AI-generated Notes?`,
  "aiNoteTakerKey34": `Yes, RecCloud provides a user-friendly editor to refine, add to, or restructure your notes after they're generated.`,
  "aiNoteTakerKey35": `Can RecCloud Capture Notes in Languages Other Than English?`,
  "aiNoteTakerKey36": `RecCloud currently supports note-taking in English, Spanish, French, German, Portuguese, Japanese, and Chinese, with more languages being added regularly.`,
  "aiNoteTakerKey37": `Is There a Free Trial Available?`,
  "aiNoteTakerKey38": `Yes, we offer a free trial with full features so you can experience how our AI notetaker transforms your productivity.`,
  "aiNoteTakerKey39": `Ready to Transform Your Note-Taking Experience?`,
  "aiNoteTakerKey40": `Try RecCloud AI Notetaker free now!`,
  "speechRecognitionKey1": `Free Online AI Automatic <i>Speech Recognition</i>`,
  "speechRecognitionKey2": `Instantly transcribe speech into text & summarize content.`,
  "speechRecognitionKey3": `How to Convert Speech to Text?`,
  "speechRecognitionKey4": `Upload`,
  "speechRecognitionKey5": `Upload your audio to our platform.`,
  "speechRecognitionKey6": `Process`,
  "speechRecognitionKey7": `Our AI automatically processes your content.`,
  "speechRecognitionKey8": `Download`,
  "speechRecognitionKey9": `Review and download your completed transcript.`,
  "speechRecognitionKey10": `Why Choose RecCloud?`,
  "speechRecognitionKey11": `Accuracy`,
  "speechRecognitionKey12": `98%+ accuracy across most accents and specialized terms.`,
  "speechRecognitionKey13": `Global Support`,
  "speechRecognitionKey14": `100+ languages and dialects for international use.`,
  "speechRecognitionKey15": `Cost Savings`,
  "speechRecognitionKey16": `Up to 80% less than traditional transcription services.`,
  "speechRecognitionKey17": `Rapid Transcription`,
  "speechRecognitionKey18": `5x faster than real-time processing.`,
  "speechRecognitionKey19": `Trusted and Loved by <i>100,000+ </i> Global Users`,
  "speechRecognitionKey20": `Our medical practice implemented RecCloud last year, and it's transformed our patient documentation process. The accuracy with medical terminology is impressive.`,
  "speechRecognitionKey21": `Emily, Healthcare Administrator`,
  "speechRecognitionKey22": `As a journalist conducting dozens of interviews weekly, RecCloud has greatly improved my workflow. I can now focus on asking better questions instead of worrying about note-taking.`,
  "speechRecognitionKey23": `James, Investigative Reporter`,
  "speechRecognitionKey24": `As a podcast producer, I've tried numerous transcription tools, but RecCloud is in a different league. It effectively handles multiple speakers and saves us countless editing hours.`,
  "speechRecognitionKey25": `Michael, Audio Production Specialist`,
  "speechRecognitionKey26": `Our legal team relies on accurate transcripts for depositions and case research. RecCloud delivers exceptional results even with complex legal terminology.`,
  "speechRecognitionKey27": `Amanda, Legal Operations Director`,
  "speechRecognitionKey28": `FAQ`,
  "speechRecognitionKey29": `How accurate is RecCloud?`,
  "speechRecognitionKey30": `Our system achieves 98%+ accuracy for clear audio recordings and handles challenging conditions with remarkable precision compared to standard solutions.`,
  "speechRecognitionKey31": `What file formats does it support?`,
  "speechRecognitionKey32": `We support all major audio formats including MP3, WAV, M4A, MOV, and WEBM. You can also upload videos directly through our platform.`,
  "speechRecognitionKey33": `Can it distinguish multiple speakers?`,
  "speechRecognitionKey34": `Yes, our advanced AI technology identifies different speakers automatically, making it perfect for interviews, meetings, and group discussions.`,
  "speechRecognitionKey35": `Is my data secure with this service?`,
  "speechRecognitionKey36": `Absolutely. We employ enterprise-grade encryption and strict data protection protocols. Your files are processed securely and can be automatically deleted afterward.`,
  "speechRecognitionKey37": `How does RecCloud handle accents?`,
  "speechRecognitionKey38": `Our system is trained on diverse speech patterns and continuously improves through machine learning, delivering excellent results across various accents and dialects.`,
  "speechRecognitionKey39": `Boost Productivity via Speech Recognition`,
  "speechRecognitionKey40": `Start converting your audio to text with RecCloud today!`,
  "googleMeetTranscriptionKey1": `Effortless AI <i>Google Meet Transcription</i>`,
  "googleMeetTranscriptionKey2": `Auto-convert Google Meet calls to precise transcripts—AI captures context & terms.`,
  "googleMeetTranscriptionKey3": `How to Transcribe Google Meet Calls`,
  "googleMeetTranscriptionKey4": `Upload`,
  "googleMeetTranscriptionKey5": `Upload your Google Meet recording`,
  "googleMeetTranscriptionKey6": `AI Transcribe`,
  "googleMeetTranscriptionKey7": `AI converts Google Meet to text in minutes`,
  "googleMeetTranscriptionKey8": `Access Your Transcript`,
  "googleMeetTranscriptionKey9": `Preview, copy, or download your transcript instantly.`,
  "googleMeetTranscriptionKey10": `Why Choose RecCloud Google Meet Transcription`,
  "googleMeetTranscriptionKey11": `Superior Accuracy`,
  "googleMeetTranscriptionKey12": `AI-powered transcription with 95% accuracy even with multiple speakers and accents.`,
  "googleMeetTranscriptionKey13": `Speaker Identification`,
  "googleMeetTranscriptionKey14": `Automatically labels who said what throughout your Google Meet calls.`,
  "googleMeetTranscriptionKey15": `Secure & Private`,
  "googleMeetTranscriptionKey16": `End-to-end encryption keeps your Google Meet transcriptions confidential.`,
  "googleMeetTranscriptionKey17": `Multi-Language Support`,
  "googleMeetTranscriptionKey18": `Transcribe Google Meet conversations in 100+ languages and dialects.`,
  "googleMeetTranscriptionKey19": `Google Meet Transcription Loved by <i>120,000+ Users</i>`,
  "googleMeetTranscriptionKey20": `Teaching virtual classes became so much easier when I started using RecCloud to transcribe Google Meet sessions. My students with hearing disabilities can follow along perfectly, and everyone benefits from searchable transcripts when studying for exams.`,
  "googleMeetTranscriptionKey21": `Jennifer, University Professor`,
  "googleMeetTranscriptionKey22": `As a project manager running remote teams across three time zones, RecCloud's Google Meet transcription has been invaluable. I can share accurate meeting notes instantly, and team members who couldn't attend can quickly catch up on discussions without watching entire recordings.`,
  "googleMeetTranscriptionKey23": `Bobby, Technical Project Manager`,
  "googleMeetTranscriptionKey24": `As a freelance consultant, I need to reference client meetings frequently. RecCloud transcribes my Google Meet calls so accurately that I've eliminated manual note-taking completely. The searchable transcripts help me quickly find specific requests or agreements without scanning through video recordings.`,
  "googleMeetTranscriptionKey25": `Stephanie, Marketing Consultant`,
  "googleMeetTranscriptionKey26": `In my legal practice, documenting client consultations accurately is essential. RecCloud's Google Meet transcribing tool captures every detail with remarkable precision, even with legal terminology. It's saved me countless hours of note-taking and reviewing recordings.`,
  "googleMeetTranscriptionKey27": `Christopher, Corporate Attorney`,
  "googleMeetTranscriptionKey28": `FAQ`,
  "googleMeetTranscriptionKey29": `Does RecCloud work with all Google Meet calls?`,
  "googleMeetTranscriptionKey30": `Yes, RecCloud works with any recording file of Google Meet calls, including free and Google Workspace accounts, one-on-one conversations, and large meetings with multiple participants.`,
  "googleMeetTranscriptionKey31": `Can RecCloud identify different speakers in Google Meet calls?`,
  "googleMeetTranscriptionKey32": `Yes, our advanced speaker diarization technology identifies and labels different participants in your Google Meet transcriptions, even with overlapping voices.`,
  "googleMeetTranscriptionKey33": `Is my Google Meet transcription data secure?`,
  "googleMeetTranscriptionKey34": `Yes, RecCloud uses enterprise-grade encryption and strict data protection policies. We are GDPR and ISO compliant, and never use your transcription content to train our models without explicit consent.`,
  "googleMeetTranscriptionKey35": `What languages does RecCloud support for Google Meet transcription?`,
  "googleMeetTranscriptionKey36": `RecCloud currently supports 100+ languages including English, Spanish, French, German, Japanese, Chinese, and more, with new languages added regularly.`,
  "googleMeetTranscriptionKey37": `Is there a free trial for Google Meet transcription?`,
  "googleMeetTranscriptionKey38": `Yes! RecCloud offers a free trial with full access to all Google Meet transcribing features, allowing you to check how good our Google Meet Transcription is.`,
  "googleMeetTranscriptionKey39": `Ready to Transcribe Your Google Meet Calls?`,
  "googleMeetTranscriptionKey40": `Try RecCloud's Google Meet Transcription free now!`,
  "voiceRecorderTranscriptionKey1": `Powerful <i>Voice Recorder with Transcription</i>`,
  "voiceRecorderTranscriptionKey2": `Record & transcribe instantly with AI—accurate, secure, and 90+ languages.`,
  "voiceRecorderTranscriptionKey3": `How to Use RecCloud Voice Recorder & Transcription`,
  "voiceRecorderTranscriptionKey4": `Record/Upload`,
  "voiceRecorderTranscriptionKey5": `Click "Record" or upload an audio file.`,
  "voiceRecorderTranscriptionKey6": `AI Transcribes`,
  "voiceRecorderTranscriptionKey7": `AI transcribes your voice to text instantly`,
  "voiceRecorderTranscriptionKey8": `Get Transcripts`,
  "voiceRecorderTranscriptionKey9": `Download or share ready transcripts`,
  "voiceRecorderTranscriptionKey10": `Why Choose RecCloud Voice Recorder with Transcription`,
  "voiceRecorderTranscriptionKey11": `Unmatched Accuracy`,
  "voiceRecorderTranscriptionKey12": `Industry-leading 95% transcription accuracy with advanced AI voice recognition`,
  "voiceRecorderTranscriptionKey13": `Speaker Identification`,
  "voiceRecorderTranscriptionKey14": `Automatically distinguishes and labels different speakers in conversations`,
  "voiceRecorderTranscriptionKey15": `Multi-Language Support`,
  "voiceRecorderTranscriptionKey16": `Voice recording and transcription in 90+ languages and regional accents`,
  "voiceRecorderTranscriptionKey17": `Privacy-Focused`,
  "voiceRecorderTranscriptionKey18": `End-to-end encryption ensures your sensitive voice recordings stay secure`,
  "voiceRecorderTranscriptionKey19": `Voice Recording & Transcription Loved by <i>100,000+ Users</i>`,
  "voiceRecorderTranscriptionKey20": `As a journalist conducting multiple interviews daily, RecCloud's voice recorder with transcription has revolutionized my workflow. I can focus on meaningful conversations without frantically taking notes, and the transcription accuracy is remarkable even with different accents. It's cut my post-interview processing time by 70%.`,
  "voiceRecorderTranscriptionKey21": `Thomas, Investigative Reporter`,
  "voiceRecorderTranscriptionKey22": `I'm a psychologist who needs accurate session notes while maintaining eye contact with clients. RecCloud's voice recording and transcription tool captures everything discreetly while I focus on therapy. The speaker identification feature perfectly separates my voice from my clients', and the GDPR compliance gives me peace of mind.`,
  "voiceRecorderTranscriptionKey23": `Elly, Clinical Psychologist`,
  "voiceRecorderTranscriptionKey24": `As a law student with dyslexia, taking notes during lectures was always challenging. RecCloud's voice transcription has been life-changing. I record classes and get perfect transcripts that I can highlight, annotate, and search through later. My grades have improved significantly since I started using it.`,
  "voiceRecorderTranscriptionKey25": `Stephen, Law Student`,
  "voiceRecorderTranscriptionKey26": `Managing a remote team across time zones means I can't always attend every meeting. RecCloud's voice recorder with transcription lets me quickly catch up on what I missed without watching entire recordings. The action item highlighting feature ensures I never miss important tasks or decisions. It's become essential for our team's productivity.`,
  "voiceRecorderTranscriptionKey27": `Carrie, Product Manager`,
  "voiceRecorderTranscriptionKey28": `FAQ`,
  "voiceRecorderTranscriptionKey29": `How accurate is RecCloud's voice transcription?`,
  "voiceRecorderTranscriptionKey30": `RecCloud achieves up to 95% accuracy for clear audio and standard accent, with excellent performance across different accents and specialized terminology through our continuously improving AI models.`,
  "voiceRecorderTranscriptionKey31": `What languages does RecCloud support for voice recording and transcription?`,
  "voiceRecorderTranscriptionKey32": `We currently support voice transcription in over 90 languages including English, Spanish, French, German, Japanese, Chinese, Arabic, and many more, with new languages added regularly.`,
  "voiceRecorderTranscriptionKey33": `Can RecCloud identify different speakers in a conversation?`,
  "voiceRecorderTranscriptionKey34": `Yes, our advanced speaker diarization technology automatically identifies and labels different speakers in your voice recordings, making conversation transcripts clear and easy to follow.`,
  "voiceRecorderTranscriptionKey35": `Is my voice recording data secure?`,
  "voiceRecorderTranscriptionKey36": `Absolutely. RecCloud employs bank-level encryption for all voice recordings and transcriptions. We are GDPR and ISO compliant, with strict data protection policies that keep your information private.`,
  "voiceRecorderTranscriptionKey37": `Is there a free trial available?`,
  "voiceRecorderTranscriptionKey38": `Yes! RecCloud offers a free trial so users can check the result of voice transcription, no payment required to start.`,
  "voiceRecorderTranscriptionKey39": `Ready to Use RecCloud Voice Transcription for Free?`,
  "voiceRecorderTranscriptionKey40": `Try RecCloud's Voice Recorder with Transcription free today!`,
  "transcribeSoundToTextKey1": `AI Transcribe <i>Sound to Text</i>`,
  "transcribeSoundToTextKey2": `Rapid sound to text transcription: AI-powered with 95% accuracy.`,
  "transcribeSoundToTextKey3": `How to Easily Transcribe Sound to Text in 3 Steps`,
  "transcribeSoundToTextKey4": `Upload Audio`,
  "transcribeSoundToTextKey5": `Drag & drop your audio file to get started`,
  "transcribeSoundToTextKey6": `AI Transcribe`,
  "transcribeSoundToTextKey7": `AI automatically transcribes sound to text`,
  "transcribeSoundToTextKey8": `Get Your Text`,
  "transcribeSoundToTextKey9": `Download or copy your transcript`,
  "transcribeSoundToTextKey10": `Why Choose RecCloud Sound Transcription?`,
  "transcribeSoundToTextKey11": `Precise`,
  "transcribeSoundToTextKey12": `AI-powered soud-to-text with 95%+ accuracy and speaker identification.`,
  "transcribeSoundToTextKey13": `Fast`,
  "transcribeSoundToTextKey14": `Get accurate transcripts in seconds—no typing needed.`,
  "transcribeSoundToTextKey15": `Secure`,
  "transcribeSoundToTextKey16": `Your data is encrypted and never accessible to third parties.`,
  "transcribeSoundToTextKey17": `Multilingual`,
  "transcribeSoundToTextKey18": `AI-powered multilingual transcription—supports English, Spanish, Chinese, French, and more.`,
  "transcribeSoundToTextKey19": `AI Sound Transcription Loved by Over <i>100,000 Users</i>`,
  "transcribeSoundToTextKey20": `This voice-to-text tool is my lifesaver! Blazing-fast transcription with pinpoint accuracy lets me trust every word.`,
  "transcribeSoundToTextKey21": `Ethan, Clinical Researcher`,
  "transcribeSoundToTextKey22": `As a reporter, I need fast and reliable transcription—RecCloud delivers every time.`,
  "transcribeSoundToTextKey23": `Olivia, News Correspondent`,
  "transcribeSoundToTextKey24": `Perfect for transcribing my podcast episodes. The speaker detection feature is a game-changer.`,
  "transcribeSoundToTextKey25": `Nathan, Audio Producer`,
  "transcribeSoundToTextKey26": `Helped me transcribe lecture recordings effortlessly. Lifesaver for grad students!`,
  "transcribeSoundToTextKey27": `Ava, PhD Candidate`,
  "transcribeSoundToTextKey28": `FAQ`,
  "transcribeSoundToTextKey29": `What sound formats are supported?`,
  "transcribeSoundToTextKey30": `MP3, WAV, M4A, and more—just upload or paste a link.`,
  "transcribeSoundToTextKey31": `Can it distinguish between different sound?`,
  "transcribeSoundToTextKey32": `Absolutely! Our AI analyzes sound patterns to identify and label each speaker automatically—perfect for meetings, interviews, and multi-speaker audio.`,
  "transcribeSoundToTextKey33": `Is there a free trial?`,
  "transcribeSoundToTextKey34": `We offer free sound-to-text transcription. Upgrade anytime to unlock longer files and premium features.`,
  "transcribeSoundToTextKey35": `Do I need to download software?`,
  "transcribeSoundToTextKey36": `No downloads needed—works 100% online. If you'd like easier access on your computer or phone, you can also download our app.`,
  "transcribeSoundToTextKey37": `Does it work accurately for non-English languages?`,
  "transcribeSoundToTextKey38": `Yes! Our AI supports 100+ languages with high accuracy, including Spanish (95%), French (94%), and Mandarin (97%). Real-world testing shows 90%+ accuracy for most languages.`,
  "transcribeSoundToTextKey39": `Ready to Transcribe Your Sound to Text?`,
  "transcribeSoundToTextKey40": `Get fast, accurate transcripts with RecCloud—try it free now!`,
  "transcribeSoundToTextKey41": `Try Now`,
  "transcribeVoiceMemosKey1": `AI Transcribe <i>Voice Memos to Text</i>`,
  "transcribeVoiceMemosKey2": `Turn voice recordings into text in seconds—98% AI accuracy and enterprise-grade security.`,
  "transcribeVoiceMemosKey3": `Transcribe Voice Memos in 3 Simple Steps`,
  "transcribeVoiceMemosKey4": `Upload`,
  "transcribeVoiceMemosKey5": `Drag & drop your voice memo file`,
  "transcribeVoiceMemosKey6": `AI Transcribe`,
  "transcribeVoiceMemosKey7": `Click "Transcribe Now" – our AI handles the rest.`,
  "transcribeVoiceMemosKey8": `Get Results`,
  "transcribeVoiceMemosKey9": `Review, copy or download your text in seconds.`,
  "transcribeVoiceMemosKey10": `Why Choose RecCloud for Voice Memo Transcription`,
  "transcribeVoiceMemosKey11": `Accurate`,
  "transcribeVoiceMemosKey12": `98%+ accurate transcription—catches slang, abbreviations, and mumbled words.`,
  "transcribeVoiceMemosKey13": `Secure`,
  "transcribeVoiceMemosKey14": `Your private memos stay private: Bank-grade encryption and never shared with third parties.`,
  "transcribeVoiceMemosKey15": `Fast`,
  "transcribeVoiceMemosKey16": `10x faster than typing—skip rewinding and get perfect text instantly.`,
  "transcribeVoiceMemosKey17": `Multi-Speaker`,
  "transcribeVoiceMemosKey18": `Identifies every speaker automatically—ideal for group memos with multiple voices.`,
  "transcribeVoiceMemosKey19": `Voice Memos Transcriber Loved by Over <i>100,000 Users</i>`,
  "transcribeVoiceMemosKey20": `I dictate story ideas on hikes, but my phone mic picks up wind noise. RecCloud still gets every word right—now I never lose a creative spark!`,
  "transcribeVoiceMemosKey21": `Emma, Fiction Writer`,
  "transcribeVoiceMemosKey22": `I record lectures while sketching diagrams. RecCloud syncs the transcribed text with my drawings later—no more missing key concepts!`,
  "transcribeVoiceMemosKey23": `Liam, Architecture Student`,
  "transcribeVoiceMemosKey24": `Juggling kids' schedules, I live by voice memos. Now RecCloud auto-sorts my grocery lists vs soccer practice times like magic!`,
  "transcribeVoiceMemosKey25": `Sophia, Mom of Twins`,
  "transcribeVoiceMemosKey26": `After client meetings, I dictate key points while driving. RecCloud turns them into organized summaries before I reach the office - like having an AI assistant.`,
  "transcribeVoiceMemosKey27": `Nathan, Sales Director`,
  "transcribeVoiceMemosKey28": `FAQ`,
  "transcribeVoiceMemosKey29": `How accurate is RecCloud?`,
  "transcribeVoiceMemosKey30": `98%+ accuracy for real-life moments—recording quick ideas, messy notes, or sudden inspirations.`,
  "transcribeVoiceMemosKey31": `How does speaker identification work?`,
  "transcribeVoiceMemosKey32": `AI analyzes voice patterns to label speakers (e.g., "Speaker 1," "Speaker 2").`,
  "transcribeVoiceMemosKey33": `Is there a free trial?`,
  "transcribeVoiceMemosKey34": `Yes! Try our voice memo transcription for free with no credit card required. Upgrade anytime for advanced features and heavier usage needs.`,
  "transcribeVoiceMemosKey35": `Which languages are supported?`,
  "transcribeVoiceMemosKey36": `RecCloud supports 100+ languages, including all major global languages: English (US/UK), Spanish, French, Chinese (Mandarin), German, Japanese, and many more—all with native-level accuracy`,
  "transcribeVoiceMemosKey37": `Is my data safe?`,
  "transcribeVoiceMemosKey38": `Your privacy comes first. All voice memos are encrypted during processing . We never share your recordings with third parties.`,
  "transcribeVoiceMemosKey39": `Ready to Transcribe Your Voice Memos?`,
  "transcribeVoiceMemosKey40": `Transcribe your first voice memo to text with RecCloud Now.`,
  "transcribeSpanishAudioKey1": `AI-powered <i>Spanish Audio Transcription</i>`,
  "transcribeSpanishAudioKey2": `Quickly transcribe Spanish audio to text—98% AI accuracy.`,
  "transcribeSpanishAudioKey3": `Transcribe Spanish Audio in 3 Steps`,
  "transcribeSpanishAudioKey4": `Upload Audio`,
  "transcribeSpanishAudioKey5": `Drag & drop Spanish audio`,
  "transcribeSpanishAudioKey6": `AI Transcribe`,
  "transcribeSpanishAudioKey7": `AI converts audio to text automatically`,
  "transcribeSpanishAudioKey8": `Get Results`,
  "transcribeSpanishAudioKey9": `Preview, copy or download transcripts`,
  "transcribeSpanishAudioKey10": `Why Choose RecCloud Spanish Audio Transcription?`,
  "transcribeSpanishAudioKey11": `98% Accuracy`,
  "transcribeSpanishAudioKey12": `AI delivers Spanish transcription with near-human-level and industry-leading precision.`,
  "transcribeSpanishAudioKey13": `Efficient`,
  "transcribeSpanishAudioKey14": `AI delivers accurate transcripts in minutes—perfect for tight deadlines.`,
  "transcribeSpanishAudioKey15": `Secure`,
  "transcribeSpanishAudioKey16": `End-to-end encrypted AI processing keeps files confidential.`,
  "transcribeSpanishAudioKey17": `Clear & Easy`,
  "transcribeSpanishAudioKey18": `Get Spanish transcripts in one click with AI - no tech skills or setup needed.`,
  "transcribeSpanishAudioKey19": `Spanish Audio Transcription Trusted by <i>10,000+ Users</i>`,
  "transcribeSpanishAudioKey20": `Between lectures and study groups, RecCloud turns my recorded class discussions into organized notes in minutes. It's been a lifesaver for my Spanish literature course!`,
  "transcribeSpanishAudioKey21": `Sophia, University Student`,
  "transcribeSpanishAudioKey22": `I interview local chefs for my food blog, and RecCloud nails every Spanish accent—even when they're rattling off recipes at lightning speed!`,
  "transcribeSpanishAudioKey23": `Carlos, Food Journalist`,
  "transcribeSpanishAudioKey24": `On my business trip to Barcelona, RecCloud saved me during back-to-back meetings - it transcribed all the Spanish discussions perfectly, even with technical jargon.`,
  "transcribeSpanishAudioKey25": `Priya, International Sales Director`,
  "transcribeSpanishAudioKey26": `I use this daily for my Spanish-language news show. The automatic punctuation feature makes my workflow twice as efficient!`,
  "transcribeSpanishAudioKey27": `James, Broadcast Journalist`,
  "transcribeSpanishAudioKey28": `FAQ`,
  "transcribeSpanishAudioKey29": `How accurate is RecCloud Spanish speech transcription?`,
  "transcribeSpanishAudioKey30": `RecCloud's AI delivers up to 98% accuracy for clear Spanish audio. Accuracy may vary with background noise, strong accents, or technical terminology.`,
  "transcribeSpanishAudioKey31": `How fast do I get the transcript?`,
  "transcribeSpanishAudioKey32": `Our AI processes files rapidly – most Spanish speech transcripts are ready within 1 minute.`,
  "transcribeSpanishAudioKey33": `Do you support speaker identification for Spanish conversations?`,
  "transcribeSpanishAudioKey34": `Yes! Our system automatically labels different speakers in Spanish dialogues as "Speaker 1," "Speaker 2," etc.`,
  "transcribeSpanishAudioKey35": `Is my data secure?`,
  "transcribeSpanishAudioKey36": `Your files are protected with end-to-end encryption and never shared with third parties. We adhere to strict data privacy protocols throughout processing and storage.`,
  "transcribeSpanishAudioKey37": `Is there a free trial?`,
  "transcribeSpanishAudioKey38": `We offer a free trial to experience our service. If you need to process more files, you can upgrade anytime after trying it out.`,
  "transcribeSpanishAudioKey39": `Ready to Transcribe Spanish Audio?`,
  "transcribeSpanishAudioKey40": `Convert your first audio file to text with RecCloud!`,
  "transcriptionSoftwareKey1": `Free Online <i>Transcription Software</i>`,
  "transcriptionSoftwareKey2": `Convert audio to text accurately with our free online transcription solution.`,
  "transcriptionSoftwareKey3": `Transcribe Audio to Text in 3 Simple Steps`,
  "transcriptionSoftwareKey4": `Upload`,
  "transcriptionSoftwareKey5": `Drag and drop your audio or video file to upload it.`,
  "transcriptionSoftwareKey6": `Process`,
  "transcriptionSoftwareKey7": `Our AI engine transcribes your files instantly.`,
  "transcriptionSoftwareKey8": `Download`,
  "transcriptionSoftwareKey9": `Get your perfectly formatted transcript in seconds.`,
  "transcriptionSoftwareKey10": `Why RecCloud is the Best AI Transcription Software?`,
  "transcriptionSoftwareKey11": `Accuracy`,
  "transcriptionSoftwareKey12": `Our AI delivers 98% transcription accuracy, even with different accents, noise, and terminology.`,
  "transcriptionSoftwareKey13": `Multi-Language`,
  "transcriptionSoftwareKey14": `Transcribe content in over 100 languages, making RecCloud an outstanding transcription program.`,
  "transcriptionSoftwareKey15": `Security`,
  "transcriptionSoftwareKey16": `With end-to-end encryption and strict privacy policies, we provide robust security features.`,
  "transcriptionSoftwareKey17": `Efficiency`,
  "transcriptionSoftwareKey18": `Our AI transcription software features automatic speaker identification.`,
  "transcriptionSoftwareKey19": `Trusted by <i>100,000+</i> Professionals Across Industries`,
  "transcriptionSoftwareKey20": `Client confidentiality is non-negotiable for us. RecCloud is the best transcription program for legal work due to its combination of military-grade security and exceptional accuracy with legal terminology.`,
  "transcriptionSoftwareKey21": `Elena, Legal Operations Manager`,
  "transcriptionSoftwareKey22": `After testing five different services, we found RecCloud to be undoubtedly the best transcription software for our podcast network. The speaker identification and automatic removal of filler words save us hours of editing time per episode.`,
  "transcriptionSoftwareKey23": `Marcus, Podcast Director`,
  "transcriptionSoftwareKey24": `As a researcher conducting qualitative interviews, I needed free online transcription software that could handle academic terminology. RecCloud not only transcribes accurately but also exports in formats compatible with analysis software.`,
  "transcriptionSoftwareKey25": `James, University Researcher`,
  "transcriptionSoftwareKey26": `In healthcare, accuracy isn't just convenient—it's critical. RecCloud has proven to be the best AI transcription software for patient interviews and medical dictation. HIPAA compliance and the ability to recognize complex medical terminology set it apart.`,
  "transcriptionSoftwareKey27": `Sarah, Healthcare Administrator`,
  "transcriptionSoftwareKey28": `FAQ`,
  "transcriptionSoftwareKey29": `How does RecCloud compare?`,
  "transcriptionSoftwareKey30": `RecCloud outperforms competitors in accuracy tests, especially with challenging audio and diverse accents.`,
  "transcriptionSoftwareKey31": `Is RecCloud really free?`,
  "transcriptionSoftwareKey32": `Yes, we offer free transcription with a ten-minute allowance, plus affordable subscription plans.`,
  "transcriptionSoftwareKey33": `What file formats work?`,
  "transcriptionSoftwareKey34": `We support 19 formats, including MP3, WAV, M4A, MP4, MOV, and WEBM.`,
  "transcriptionSoftwareKey35": `How secure is my data?`,
  "transcriptionSoftwareKey36": `We use end-to-end encryption, GDPR compliance, and automatic file deletion after processing.`,
  "transcriptionSoftwareKey37": `Can it identify different speakers?`,
  "transcriptionSoftwareKey38": `Yes, our technology automatically identifies and labels different speakers in recordings.`,
  "transcriptionSoftwareKey39": `Try the Best Transcription Software Today`,
  "transcriptionSoftwareKey40": `Join thousands using RecCloud to transcribe audio into text!`,
  "videoTranscriptionKey1": `Free Online <i>Video Transcription</i>`,
  "videoTranscriptionKey2": `Quickly convert videos to scripts—no downloads or installations required.`,
  "videoTranscriptionKey3": `How to Transcribe Video to Text?`,
  "videoTranscriptionKey4": `Upload`,
  "videoTranscriptionKey5": `Simply drag and drop your video file onto our platform.`,
  "videoTranscriptionKey6": `Transcribe`,
  "videoTranscriptionKey7": `RecCloud processes the file automatically.`,
  "videoTranscriptionKey8": `Download`,
  "videoTranscriptionKey9": `Once done, you can preview and download the transcript.`,
  "videoTranscriptionKey10": `Why Choose Our Video Transcription Service`,
  "videoTranscriptionKey11": `Precision`,
  "videoTranscriptionKey12": `AI technology delivering 98% transcript accuracy.`,
  "videoTranscriptionKey13": `Language Diversity`,
  "videoTranscriptionKey14": `Support for 100+ languages worldwide.`,
  "videoTranscriptionKey15": `Confidentiality`,
  "videoTranscriptionKey16": `End-to-end encryption protects your content.`,
  "videoTranscriptionKey17": `User-Friendly`,
  "videoTranscriptionKey18": `Convert videos to text with minimal effort.`,
  "videoTranscriptionKey19": `Praised and Recommended by <i>100,000+ </i> Users`,
  "videoTranscriptionKey20": `I interview experts for my dissertation research and needed accurate transcriptions. This online video transcription tool saved me countless hours and produced text that required minimal editing.`,
  "videoTranscriptionKey21": `Emily, Research Scholar`,
  "videoTranscriptionKey22": `As a content creator with over 200 videos, I needed a reliable way to transcribe video to text to create captions and blog posts. This tool is lightning-fast and surprisingly accurate, even with my technical terminology.`,
  "videoTranscriptionKey23": `Mark, Technology YouTuber`,
  "videoTranscriptionKey24": `Our company produces webinars and product demos weekly. Being able to transcribe video to text free online has revolutionized our content workflow. We repurpose the transcripts into blog posts, email sequences, and social media content.`,
  "videoTranscriptionKey25": `Carlos, Marketing Director`,
  "videoTranscriptionKey26": `Working in our university's media team, we needed to make our teaching videos accessible for students with disabilities. This tool was a lifesaver - it helped us add text to hundreds of our classroom videos quickly and without breaking our budget, making our content usable for all students.`,
  "videoTranscriptionKey27": `Jennifer, Education Administrator`,
  "videoTranscriptionKey28": `FAQ`,
  "videoTranscriptionKey29": `How accurate is your video transcription tool?`,
  "videoTranscriptionKey30": `Our advanced AI-powered system achieves 98% accuracy for most clear audio in videos. Factors affecting accuracy include audio quality, background noise, accents, and technical terminology.`,
  "videoTranscriptionKey31": `Can I transcribe videos in multiple languages?`,
  "videoTranscriptionKey32": `Yes! Our online video transcription service supports 100+ languages including Spanish, French, German, Japanese, Chinese, and more.`,
  "videoTranscriptionKey33": `Is there a length limit for videos?`,
  "videoTranscriptionKey34": `Our free plan allows videos up to 10 minutes in length. For longer videos, you can either split your file or upgrade to our premium plan.`,
  "videoTranscriptionKey35": `How secure is my uploaded video content?`,
  "videoTranscriptionKey36": `We take data security seriously. All uploaded videos are processed through encrypted connections, are never shared with third parties, and are automatically deleted from our servers after 24 hours.`,
  "videoTranscriptionKey37": `Can I export my transcript to different formats?`,
  "videoTranscriptionKey38": `Absolutely! Once your video is transcribed to text, you can download it in plain text (.txt) format. You can also copy the text and paste it anywhere.`,
  "videoTranscriptionKey39": `Start Transcribing Your Videos to Text Today`,
  "videoTranscriptionKey40": `Access RecCloud Professional-Quality Video Transcription Online For Free!`,
  "transcribeInstagramKey1": `1-Click <i>Instagram Video to Text</i> Transcription`,
  "transcribeInstagramKey2": `Convert Instagram content into accurate text within minutes.`,
  "transcribeInstagramKey3": `How to Transcribe Instagram Videos?`,
  "transcribeInstagramKey4": `Upload`,
  "transcribeInstagramKey5": `Simply upload your Instagram videos.`,
  "transcribeInstagramKey6": `Transcribe`,
  "transcribeInstagramKey7": `Our system will process your content.`,
  "transcribeInstagramKey8": `Download`,
  "transcribeInstagramKey9": `Review and save transcript after completion.`,
  "transcribeInstagramKey10": `Why Choose RecCloud Instagram Transcription?`,
  "transcribeInstagramKey11": `Accuracy`,
  "transcribeInstagramKey12": `98% precise transcription, even with background music.`,
  "transcribeInstagramKey13": `Multilingual`,
  "transcribeInstagramKey14": `100+ languages including trending slang.`,
  "transcribeInstagramKey15": `Security`,
  "transcribeInstagramKey16": `Private content with zero data sharing.`,
  "transcribeInstagramKey17": `Speed`,
  "transcribeInstagramKey18": `Get transcripts within seconds.`,
  "transcribeInstagramKey19": `Highly rated with <i>100,000+ </i> satisfied customers`,
  "transcribeInstagramKey20": `This Instagram transcription service has completely transformed my workflow. I can now repurpose my viral Reels into blog content in minutes instead of hours. The accuracy is impressive even with background music!`,
  "transcribeInstagramKey21": `Emily, Content Creator`,
  "transcribeInstagramKey22": `We needed to transcribe Instagram video content for our client reports and SEO strategies. This free online tool delivers professional-quality transcripts that help us demonstrate content performance and plan future campaigns.`,
  "transcribeInstagramKey23": `Marcus, Digital Marketing Manager`,
  "transcribeInstagramKey24": `I use Instagram videos in my digital media classes and needed accurate transcripts for my students with hearing impairments. RecCloud has made my course materials fully accessible with minimal effort.`,
  "transcribeInstagramKey25": `Jacobs, Education Technology Specialist`,
  "transcribeInstagramKey26": `When researching trending topics, I often need to transcribe Instagram interview clips. This tool saves me countless hours and ensures I quote sources accurately in my articles.`,
  "transcribeInstagramKey27": `Lisa, Freelance Journalist`,
  "transcribeInstagramKey28": `FAQ`,
  "transcribeInstagramKey29": `How accurate is the Instagram video transcription?`,
  "transcribeInstagramKey30": `Our advanced AI delivers 98%+ accuracy for most clear audio. Factors affecting accuracy include background noise, multiple speakers talking simultaneously, strong accents, and technical terminology.`,
  "transcribeInstagramKey31": `Can I transcribe Instagram videos in various languages?`,
  "transcribeInstagramKey32": `Yes! Our service supports over 100 languages including Spanish, French, German, Chinese, Japanese, and many more. Just upload your video in the target language for conversion.`,
  "transcribeInstagramKey33": `Is there a limit to the video length I can transcribe?`,
  "transcribeInstagramKey34": `Free users can transcribe videos up to 10 minutes without an account. For extended capabilities, create a free account or upgrade to premium for full access to all features.`,
  "transcribeInstagramKey35": `How is my video data protected?`,
  "transcribeInstagramKey36": `We maintain strict privacy protocols. All uploaded videos are encrypted and automatically deleted from our servers after 24 hours. We never share your content with third parties.`,
  "transcribeInstagramKey37": `Does it work with all Instagram video formats?`,
  "transcribeInstagramKey38": `Yes! Our tool supports all major Instagram video formats (including Reels, Stories, and IGTV) for effortless transcription.`,
  "transcribeInstagramKey39": `Start Transcribing Instagram Videos`,
  "transcribeInstagramKey40": `Get accurate transcriptions with RecCloud!`,
  "podcastTranscriptKey1": `AI-powered <i>Podcast Transcription</i>`,
  "podcastTranscriptKey2": `Transcribe podcast audio in seconds – save time, effort, and enjoy ultra-high accuracy!`,
  "podcastTranscriptKey3": `How to Use RecCloud AI Podcast Transcription?`,
  "podcastTranscriptKey4": `Upload Audio/Video`,
  "podcastTranscriptKey5": `Click to upload or drag and drop your podcast recording.`,
  "podcastTranscriptKey6": `AI-Powered Transcription`,
  "podcastTranscriptKey7": `AI automatically converts speech to text and generates key summaries.`,
  "podcastTranscriptKey8": `Edit & Export`,
  "podcastTranscriptKey9": `Copy text, download transcripts, or refine further.`,
  "podcastTranscriptKey10": `Why Choose RecCloud AI Podcast Transcription?`,
  "podcastTranscriptKey11": `High Accuracy`,
  "podcastTranscriptKey12": `Trained on vast speech data, RecCloud AI accurately recognizes colloquial expressions and multilingual terms, suitable for all podcast genres.`,
  "podcastTranscriptKey13": `Blazing Fast`,
  "podcastTranscriptKey14": `60x faster than manual transcription—10-minute audio processed in just 1 minute. Turn ideas into content instantly.`,
  "podcastTranscriptKey15": `Speaker Identification`,
  "podcastTranscriptKey16": `Smartly distinguishes hosts and guests, making conversations structured for easy editing and review.`,
  "podcastTranscriptKey17": `User-Friendly`,
  "podcastTranscriptKey18": `No software download needed. Intuitive interface—beginners can start transcribing in seconds.`,
  "podcastTranscriptKey19": `Trusted by <i>100,000+</i> Content Creators`,
  "podcastTranscriptKey20": `RecCloud saves me hours on podcast transcriptions and streamlines my editing workflow. Super practical!`,
  "podcastTranscriptKey21": `Emily, Content Creator`,
  "podcastTranscriptKey22": `As a podcast editor, I handle multiple episodes under tight deadlines. RecCloud has multiplied my productivity!`,
  "podcastTranscriptKey23": `Liam, Podcast Producer`,
  "podcastTranscriptKey24": `No more manual note-taking! This AI tool transcribes and extracts key summaries in one click.`,
  "podcastTranscriptKey25": `James, Independent Host`,
  "podcastTranscriptKey26": `Our team uses RecCloud to generate marketing transcripts and highlight reels—cutting costs and speeding up content delivery.`,
  "podcastTranscriptKey27": `Olivia, Content Marketing Lead`,
  "podcastTranscriptKey28": `FAQ`,
  "podcastTranscriptKey29": `How accurate is RecCloud for conversational podcasts?`,
  "podcastTranscriptKey30": `Our AI is optimized for spoken language, with 95%+ accuracy—ideal for multi-speaker podcasts.`,
  "podcastTranscriptKey31": `Does it work with fast speech or background noise?`,
  "podcastTranscriptKey32": `Yes! RecCloud is trained on varying speeds and noise levels for robust performance.`,
  "podcastTranscriptKey33": `How long does transcription take?`,
  "podcastTranscriptKey34": `Typically 1/10 of audio length (e.g., 20 mins → 2 mins).`,
  "podcastTranscriptKey35": `Is my content secure?`,
  "podcastTranscriptKey36": `Absolutely! Enterprise-grade encryption ensures full data protection.`,
  "podcastTranscriptKey37": `Is there a free plan?`,
  "podcastTranscriptKey38": `Yes! Try our free tier or upgrade for advanced features.`,
  "podcastTranscriptKey39": `Don’t Wait—Try RecCloud AI Podcast Transcription Now!`,
  "podcastTranscriptKey40": `Fast, precise, and effortless—turn podcasts into clear text instantly!`,
  "interviewTranscriptKey1": `Free Online AI <i>Interview Transcription</i>`,
  "interviewTranscriptKey2": `Transcribe interview audio fast—save time, effort, and enjoy ultra-high accuracy!`,
  "interviewTranscriptKey3": `How to Use RecCloud AI Interview-to-Text?`,
  "interviewTranscriptKey4": `Upload Audio/Video`,
  "interviewTranscriptKey5": `Click to upload or drag and drop interview recordings or video files`,
  "interviewTranscriptKey6": `AI Smart Transcription`,
  "interviewTranscriptKey7": `AI automatically converts conversations into text and highlights key candidate responses`,
  "interviewTranscriptKey8": `Review & Export`,
  "interviewTranscriptKey9": `Copy text, download transcripts, or edit and save for future use`,
  "interviewTranscriptKey10": `Why Choose RecCloud AI Interview-to-Text?`,
  "interviewTranscriptKey11": `High Accuracy`,
  "interviewTranscriptKey12": `Trained on vast speech datasets, RecCloud AI accurately recognizes tones and industry terms, capturing every word of interview Q&A.`,
  "interviewTranscriptKey13": `Blazing Fast`,
  "interviewTranscriptKey14": `60x faster than manual transcription—30-minute interviews transcribed in just 3 minutes, supercharging HR workflows.`,
  "interviewTranscriptKey15": `Speaker Identification`,
  "interviewTranscriptKey16": `Smartly distinguishes between interviewer and candidate voices for organized data, simplifying evaluation and review.`,
  "interviewTranscriptKey17": `Easy to Use`,
  "interviewTranscriptKey18": `No software installation—use it online with an intuitive interface, perfect for beginners and professionals alike.`,
  "interviewTranscriptKey19": `Interview Transcription Trusted by <i>100,000+ </i> Users`,
  "interviewTranscriptKey20": `I use RecCloud to transcribe both online and in-person interviews. It quickly organizes content and lets me replay key moments—super practical!`,
  "interviewTranscriptKey21": `Emily, HR Specialist`,
  "interviewTranscriptKey22": `As an HR manager handling tons of interview notes daily, RecCloud saves me so much time by transcribing and syncing notes simultaneously.`,
  "interviewTranscriptKey23": `Jason, Human Resources Manager`,
  "interviewTranscriptKey24": `Before, I had to replay every interview for notes. Now, one-click transcription and quick highlighting boost my efficiency dramatically.`,
  "interviewTranscriptKey25": `Sophia, Recruitment Consultant`,
  "interviewTranscriptKey26": `Our team uses RecCloud for campus recruitment summaries—standardized, fast, and incredibly easy for data consolidation.`,
  "interviewTranscriptKey27": `David, Corporate Training Lead`,
  "interviewTranscriptKey28": `FAQ`,
  "interviewTranscriptKey29": `How accurate is RecCloud AI for transcribing interviews?`,
  "interviewTranscriptKey30": `Our AI is optimized for two-way conversations, with over 95% accuracy, especially suited for interview contexts.`,
  "interviewTranscriptKey31": `Can it transcribe poor-quality audio or fast speech?`,
  "interviewTranscriptKey32": `Absolutely! RecCloud is trained on varied audio qualities and speeds, adapting to real-world interview scenarios.`,
  "interviewTranscriptKey33": `How long does transcription take?`,
  "interviewTranscriptKey34": `Typically 1/10 of the recording length—e.g., a 30-minute interview takes just 3 minutes.`,
  "interviewTranscriptKey35": `Is my recording data secure?`,
  "interviewTranscriptKey36": `RecCloud uses enterprise-grade encryption to ensure full privacy and data protection.`,
  "interviewTranscriptKey37": `Is there a free version?`,
  "interviewTranscriptKey38": `Yes! Try our free plan and experience efficient, accurate interview transcription today.`,
  "interviewTranscriptKey39": `Don’t wait—Try RecCloud AI Interview-to-Text now!`,
  "interviewTranscriptKey40": `Precise and fast, transforming interview content into text in no time!`,
  "meetingTranscriptionKey1": `Free Video AI <i>Meeting Transcription</i>`,
  "meetingTranscriptionKey2": `Transcribe meeting audio quickly, save time and manpower, with high accuracy and no lag!`,
  "meetingTranscriptionKey3": `How to Use RecCloud AI Meeting Audio to Text?`,
  "meetingTranscriptionKey4": `Upload Audio or Video`,
  "meetingTranscriptionKey5": `Click to upload or drag and drop your recording/meeting video file`,
  "meetingTranscriptionKey6": `AI Transcribe`,
  "meetingTranscriptionKey7": `AI automatically recognizes multi-speaker conversations, quickly converting content into clear text with speaker labels`,
  "meetingTranscriptionKey8": `Proofread & Export`,
  "meetingTranscriptionKey9": `Edit text, copy content, or download verbatim transcripts—complete data integration in one stop`,
  "meetingTranscriptionKey10": `Why Choose RecCloud AI Meeting Transcription?`,
  "meetingTranscriptionKey11": `High Accuracy`,
  "meetingTranscriptionKey12": `Trained on extensive multilingual data, RecCloud AI recognizes various accents and speech rates, capturing every detail`,
  "meetingTranscriptionKey13": `Ultra-Fast`,
  "meetingTranscriptionKey14": `Transcription speed far exceeds manual work—60-minute meetings take just 6 minutes, boosting administrative efficiency`,
  "meetingTranscriptionKey15": `Speaker Identification`,
  "meetingTranscriptionKey16": `Automatically distinguishes different speakers, making dialogue structure clearer and data organization easier`,
  "meetingTranscriptionKey17": `Easy to Use`,
  "meetingTranscriptionKey18": `No software installation required—use directly in your browser, even beginners can master it effortlessly`,
  "meetingTranscriptionKey19": `Meeting Transcription Trusted by Over <i>100,000+</i> Users`,
  "meetingTranscriptionKey20": `Our team frequently holds remote meetings. With RecCloud, reports are generated instantly—efficiency doubled!`,
  "meetingTranscriptionKey21": `Michael, Project Manager`,
  "meetingTranscriptionKey22": `In the past, organizing meeting notes was time-consuming. With RecCloud, clear transcripts are just one click away.`,
  "meetingTranscriptionKey23": `Emily, Marketing Director`,
  "meetingTranscriptionKey24": `The transcription feature is incredibly convenient—not just converting speech to text but also highlighting key sections for follow-up discussions.`,
  "meetingTranscriptionKey25": `David, Product Designer`,
  "meetingTranscriptionKey26": `RecCloud saves us tons of time on meeting notes, making cross-department collaboration smoother.`,
  "meetingTranscriptionKey27": `Sophia, Corporate Admin Manager`,
  "meetingTranscriptionKey28": `FAQ`,
  "meetingTranscriptionKey29": `Can RecCloud AI accurately transcribe multi-speaker meetings?`,
  "meetingTranscriptionKey30": `Absolutely! Our AI is optimized for multi-speaker dialogues, with over 95% accuracy, handling various meeting scenarios with ease`,
  "meetingTranscriptionKey31": `Does it work with poor-quality recordings or background noise?`,
  "meetingTranscriptionKey32": `Yes! Our AI is trained to handle different audio qualities, noise levels, and speech speeds, adapting to various recording conditions`,
  "meetingTranscriptionKey33": `How long does it take to transcribe a meeting?`,
  "meetingTranscriptionKey34": `Typically 1/10 of the recording length—e.g., a 60-minute meeting takes about 6 minutes to transcribe`,
  "meetingTranscriptionKey35": `Is my data secure?`,
  "meetingTranscriptionKey36": `No worries! RecCloud uses enterprise-grade encryption to ensure full privacy protection`,
  "meetingTranscriptionKey37": `Is there a free trial?`,
  "meetingTranscriptionKey38": `Of course! Sign up now for free and experience efficient, smart, and accurate AI meeting transcription`,
  "meetingTranscriptionKey39": `Stop taking manual meeting notes—try RecCloud AI Meeting Transcription!`,
  "meetingTranscriptionKey40": `Fast and precise, ensuring every meeting is clearly documented!`,
  "transcribeTiktokVideoKey1": `AI-powered <i>TikTok Video Transcription</i>`,
  "transcribeTiktokVideoKey2": `Smartly convert video speech into structured text—never miss another viral quote again!`,
  "transcribeTiktokVideoKey3": `How to Use RecCloud TikTok Video Transcription?`,
  "transcribeTiktokVideoKey4": `Upload Video`,
  "transcribeTiktokVideoKey5": `Drag and drop a video file`,
  "transcribeTiktokVideoKey6": `AI Transcribe`,
  "transcribeTiktokVideoKey7": `AI instantly converts speech to text, extracting key phrases while detecting tone and speaker emphasis`,
  "transcribeTiktokVideoKey8": `Proofread & Export`,
  "transcribeTiktokVideoKey9": `Quickly edit, copy, or export full captions`,
  "transcribeTiktokVideoKey10": `Why Use RecCloud to Transcribe TikTok Videos?`,
  "transcribeTiktokVideoKey11": `High Accuracy`,
  "transcribeTiktokVideoKey12": `RecCloud AI combines speech and semantic recognition to precisely extract trending keywords and viral phrases`,
  "transcribeTiktokVideoKey13": `Ultra-Fast`,
  "transcribeTiktokVideoKey14": `A 90-second video takes under 10 seconds to transcribe, boosting content creation efficiency`,
  "transcribeTiktokVideoKey15": `Multilingual`,
  "transcribeTiktokVideoKey16": `Extracts captions in Chinese, English, and 10+ other languages—perfect for global TikTok content`,
  "transcribeTiktokVideoKey17": `Beginner-Friendly`,
  "transcribeTiktokVideoKey18": `No software download needed—use directly in your browser with intuitive, one-time setup`,
  "transcribeTiktokVideoKey19": `Trusted by Over <i>100,000+</i> Users for TikTok Caption Extraction`,
  "transcribeTiktokVideoKey20": `As a content creator, I analyze viral videos daily. RecCloud triples my workflow speed!`,
  "transcribeTiktokVideoKey21": `James, Content Creator`,
  "transcribeTiktokVideoKey22": `Previously caption extraction was tedious—now just uploading a TikTok. So convenient!`,
  "transcribeTiktokVideoKey23": `Olivia, Short-Form Video Marketer`,
  "transcribeTiktokVideoKey24": `RecCloud saves me hours of prep work, giving more inspiration with less stress.`,
  "transcribeTiktokVideoKey25": `Ethan, Copywriter`,
  "transcribeTiktokVideoKey26": `I source TikTok for ideas constantly. RecCloud’s extraction is fast and precise—highly recommended!`,
  "transcribeTiktokVideoKey27": `Ava, Brand Content Editor`,
  "transcribeTiktokVideoKey28": `FAQ`,
  "transcribeTiktokVideoKey29": `What TikTok video formats does RecCloud support?`,
  "transcribeTiktokVideoKey30": `MP4 files, or mobile recordings—AI automatically transcribes speech from any supported format.`,
  "transcribeTiktokVideoKey31": `Which languages can RecCloud extract?`,
  "transcribeTiktokVideoKey32": `Supports 10+ languages including Chinese, English, Japanese, and Korean for global content.`,
  "transcribeTiktokVideoKey33": `Can I export results directly?`,
  "transcribeTiktokVideoKey34": `Yes! Extract, copy, and edit captions immediately for seamless repurposing.`,
  "transcribeTiktokVideoKey35": `Is my video data stored?`,
  "transcribeTiktokVideoKey36": `No! RecCloud prioritizes privacy—all uploads use enterprise encryption and auto-delete post-processing.`,
  "transcribeTiktokVideoKey37": `Is there a free trial?`,
  "transcribeTiktokVideoKey38": `Yes! Sign up now for full-featured free access.`,
  "transcribeTiktokVideoKey39": `Stop manual TikTok transcription—try RecCloud now!`,
  "transcribeTiktokVideoKey40": `Simple and powerful—unlock the full potential of every short video!`,
  "navGitmindAddonToChrome": `Add to Chrome`,
  "navGitmindAddonToEdge": `Add to Edge`,
  "navGitmindAddonToFirefox": `Add to Firefox`,
  "aiTranslateKey51": `Clone Translation`,
  "aiTranslateKey52": `Voice clone Dubbing + Subtitles`,
  "aiTranslateKey53": `Max support: Video ≤4G, Audio ≤500M, Duration ≤5 min`,
  "aiTranslateKey54": `Voice clone dubbing: Single speaker (<5min)`,
  "aiTranslateKey55": `Processing...`,
  "aiTranslateKey56": `Translating subtitles + Cloning voice...`,
  "aiTranslateKey57": `AI Proofreading...`,
  "aiClipTitle": `<i>AI Clip Maker</i> – Auto-Cut Video Highlights`,
  "aiClipDesc": `Generate viral clips in seconds. AI extracts the best moments automatically!`,
  "aiClipSupportFmt": `Supported formats: mp3/mp4/mov/webm/wav... (and 14+ more)`,
  "aiClipScene": `Video Type`,
  "aiClipScene1": `General`,
  "aiClipScene2": `Live Stream`,
  "aiClipScene3": `Sports Event`,
  "aiClipScene4": `Talk/Interview`,
  "aiClipScene5": `Film/TV Show`,
  "aiClipMode": `Clip Mode`,
  "aiClipMode1": `Continuous Clip`,
  "aiClipMode2": `Smart Merge`,
  "aiClipLength": `Length`,
  "aiClipLength1": `Auto`,
  "aiClipModeTitle": `Continuous vs Smart`,
  "aiClipModeBtnTitle": `Got it`,
  "aiClipModeTitle1": `Continuous Clip`,
  "aiClipModeTitle2": `Keep full parts`,
  "aiClipModeTitle3": `Good for: Tutorials (like cooking steps)`,
  "aiClipModeTitle4": `Smart Merge`,
  "aiClipModeTitle5": `Combine Pieces`,
  "aiClipModeTitle6": `Good for: Talks (like interview highlights)`,
  "aiClipStartBtnTitle": `Get Clips`,
  "aiClipUseCasesTitle": `Use Cases`,
  "aiClipUseCasesDesc": `Clip smarter, share faster`,
  "aiClipUseCasesContent1": `Live Stream`,
  "aiClipUseCasesContent2": `Turn livestreams into viral-ready shorts.`,
  "aiClipUseCasesContent3": `Sports Event`,
  "aiClipUseCasesContent4": `Capture key goals, highlights, or controversial calls.`,
  "aiClipUseCasesContent5": `Speech Highlights`,
  "aiClipUseCasesContent6": `Extract key moments from long speeches for quick sharing.`,
  "aiClipUseCasesContent7": `Film/TV Show`,
  "aiClipUseCasesContent8": `Auto-capture top moments from shows or film.`,
  "aiClipUseCasesContent9": `Product Demo`,
  "aiClipUseCasesContent10": `Showcase product highlights in seconds.`,
  "aiClipUseCasesContent11": `Course Snippets`,
  "aiClipUseCasesContent12": `Chunk lessons into digestible clips for faster learning.`,
  "aiClipStartMakeNow": `Make Clips Now`,
  "aiClipWhyTitle": `Powerful AI Clips`,
  "aiClipWhyDesc": `10X faster AI clipping—focus on storytelling`,
  "aiClipWhyContent1": `AI-Curated Highlights`,
  "aiClipWhyContent2": `Automatically identifies key moments and generates shareable clips.`,
  "aiClipWhyContent3": `Auto Subtitles`,
  "aiClipWhyContent4": `97% accurate auto-subtitles for better reach.`,
  "aiClipWhyContent5": `Smart Reframing`,
  "aiClipWhyContent6": `Dynamically adjusts framing for speakers and moving objects.`,
  "aiClipStepsTitle": `Create Clips in 3 Simple Steps`,
  "aiClipStepsDesc": `Easy clips, anyone can do it!`,
  "aiClipStepsContent1": `Upload Video`,
  "aiClipStepsContent2": `Drag & drop any video file (MP4, MOV, etc.)`,
  "aiClipStepsContent3": `AI Magic`,
  "aiClipStepsContent4": `Auto-detects highlights & adds subtitles.`,
  "aiClipStepsContent5": `Download & Share`,
  "aiClipStepsContent6": `Download and share to TikTok/IG/YouTube`,
  "aiClipStartMakeNow2": `Start Free Trial`,
  "aiClipUserTitle": `AI Clip Generator Loved by <i>100,000+ Users</i>`,
  "aiClipUserDesc": `My lecture clips now take minutes instead of hours. Lifesaver for this overwhelmed professor!`,
  "aiClipUserContent1": `Dr. Naomi Chen, University Lecturer`,
  "aiClipUserContent2": `RecCloud's AI clipping saves me 10+ hours weekly. The auto-highlight detection is scarily accurate!`,
  "aiClipUserContent3": `Marcus, Twitch Streamer`,
  "aiClipUserContent4": `As a sports coach, I use this daily to clip team highlights. The smart merging feature is a game-changer.`,
  "aiClipUserContent5": `Derek, High School Basketball Coach`,
  "aiClipUserContent6": `From 3-hour sermons to 3-minute clips—our church’s social media engagement doubled thanks to this tool.`,
  "aiClipUserContent7": `Pastor Emily, Digital Ministry Leader`,
  "aiClipQATitle": `FAQ`,
  "aiClipQADesc": `How accurate is RecCloud AI highlight detection?`,
  "aiClipQAContent1": `Our AI identifies key moments with 92% precision, using both visual and audio cues to ensure no important scene is missed.`,
  "aiClipQAContent2": `What video formats do you support?`,
  "aiClipQAContent3": `We support 19 common video formats, including MP4, MOV, AVI, MKV, and WEBM.`,
  "aiClipQAContent4": `How does 'Smart Merge' work?`,
  "aiClipQAContent5": `AI groups related fragments (e.g., interview Q&As) into cohesive clips using topic modeling.`,
  "aiClipQAContent6": `Do you offer a free trial?`,
  "aiClipQAContent7": `Yes! You can try our AI video clipping feature for free. Upgrade anytime to unlock advanced tools and higher clip limits.`,
  "aiClipQAContent8": `Is my video data safe?`,
  "aiClipQAContent9": `Absolutely. We use advanced encryption to protect your videos, ensuring secure transmission and storage with no risk of leakage.`,
  "aiClipLastStartTitle": `Tired of manual clipping?`,
  "aiClipLastStartDesc": `Get it done in seconds with RecCloud AI Clips Maker!`,
  "aiClipStartMakeNow3": `Start Free Trial`,
  "aiClipTaskResultCountTitle": `Clips`,
  "aiClipTaskResultEditTitle": `Edit`,
  "aiClipTaskResultDownloadTitle": `Download`,
  "aiClipTaskResultDownloadingTitle": `Downloading...`,
  "aiClipTaskResultAdjustDuration": `Drag to adjust clip start/end points`,
  "aiClipTaskResultAdjustDurationDesc": `+10s buffer will be added to both ends`,
} as const

import {defineConfig} from 'astro/config'
import svelte from '@astrojs/svelte'
import vue from '@astrojs/vue'
import deploy from "@central/deploy";
import polyfill from '@central/polyfill'
import tracking from '@central/tracking'
import i18n from '@central/i18n/plugin'
import hostEnvPlugin from '@central/host-env'
import assets from '@central/assets'
import UnoCSS from 'unocss/astro'
import transformerDirectives from '@unocss/transformer-directives'
import transformerVariantGroup from '@unocss/transformer-variant-group'

// https://astro.build/config
export default defineConfig({
  vite: {
    ssr: {
      noExternal: [/@reccloud/, /element-plus/],
    },
    // 预构建排除： 将部分第三方依赖纳入 vite 构建系统， 而非进入预构建流程。
    optimizeDeps: {
      exclude: ['@central/loginer', "swiper/vue", "swiper/types", "docx"],
      include: ["lodash-es", "element-plus"],
    },
    plugins: [],
  },
  integrations: [
    deploy(
      {
        projectId: 'reccloud',
      }
    ),
    assets(),
    i18n(),
    hostEnvPlugin({
      clients: {
        wechat: 'wx1d396e446d0ecf21',
        qq: '101326744',
        dingtalk: 'dingoay8cqutmug5tmgjiu',
        google: '803827820086-89rj7bt5f2t145s7hfjg3igep0q0vvet.apps.googleusercontent.com',
        facebook: '1111300526807510',
      },
      product_id: 293,
      forceBindPhone: {
        tutorial: "",
      },
    }),
    polyfill(),
    svelte({
      onwarn() {
      },
    }),
    vue({
      reactivityTransform: true,
      jsx: true,
      devtools: {
        launchEditor: 'webstorm'
      }
    }),
    UnoCSS({
      content: {
        pipeline: {
          include: [
            "node_modules/@central/*/src/**/*.{astro,html,js,jsx,svelte,ts,tsx,vue}",
            "node_modules/@central-legos/*/src/**/*.{astro,html,js,jsx,svelte,ts,tsx,vue}",
            "src/**/*.ts",
            "src/pages/**/*.astro",
            'src/component/**/*.{vue,astro,tsx,ts}',
            'src/render/**/*.{vue,astro,tsx,ts}'
          ]
        },
        filesystem: [
          "node_modules/@central/*/src/**/*.{astro,html,js,jsx,svelte,ts,tsx,vue}",
          "node_modules/@central-legos/*/src/**/*.{astro,html,js,jsx,svelte,ts,tsx,vue}",
          "src/**/*.ts",
          "src/pages/**/*.astro",
          'src/component/**/*.{vue,astro,tsx,ts}',
          'src/render/**/*.{vue,astro,tsx,ts}'
        ],
      },
      injectReset: true,
      transformers: [transformerDirectives(), transformerVariantGroup()]
    }),
  ],
  devToolbar: {
    enabled: false,
  },
})

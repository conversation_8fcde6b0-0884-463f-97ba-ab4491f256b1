#!/bin/bash


# 定义输出红色信息的函数
function danger_log() {
    local RED='\033[31m'
    local RESET='\033[0m'
    echo -e "${RED}$1${RESET}"
}

echo '打包构建前检查开始...'

#danger_log "由于后台接口问题, 已经合并到master的代码不能发布, 所以暂时请不要发布任何页面"

#exit 1
#
## 提示用户输入 y/n，并只读取一个字符
#read -n 1 -p "是否继续发布?(y/n): " choice
#
#echo ""
#
## 检查用户输入是否为 y 或 Y
#if [[ "$choice" == [Yy] ]]; then
#   echo "打包构建开始"
#   exit 0
#else
#   echo "打包构建停止"
#   exit 1
#fi

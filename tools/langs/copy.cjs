const {readLangsFile, writeLangsResultToFile} = require("./util.cjs");
const path = require("path");
const fs = require('fs');

copyLangKey().then()

/**
 * 复制指定key的值到新的targetKey
 * @param content 文件内容
 * @param sourceKey 源key
 * @param targetKey 目标key
 * @return {string} 处理后的内容
 */
function handleCopy(content, sourceKey, targetKey) {
  // 查找sourceKey的值
  const sourceKeyRegex = new RegExp(`["']${sourceKey}["']\\s*:\\s*\`([^\`]*)\``, 'g');
  const match = sourceKeyRegex.exec(content);

  if (!match) {
    console.log(`未找到源key: ${sourceKey}`);
    return content;
  }

  const value = match[1];
  // 构建新的键值对
  const newEntry = `  "${targetKey}": \`${value}\`,\n`;

  // 在最后一个键值对前插入新的条目
  return content.replace("} as const", newEntry + "} as const");
}

/**
 * 读取用户输入
 * @param prompt 提示信息
 * @return {Promise<string>}
 */
function readUserInput(prompt) {
  return new Promise((resolve) => {
    const scanner = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    scanner.question(prompt, (input) => {
      scanner.close();
      resolve(input.trim());
    });
  });
}

async function copyLangKey() {
  // 获取用户输入
  const sourceKey = await readUserInput('请输入要复制的源key：');
  if (!sourceKey) {
    console.log('源key不能为空');
    return;
  }

  const targetKey = await readUserInput('请输入目标key：');
  if (!targetKey) {
    console.log('目标key不能为空');
    return;
  }

  // 处理所有语言文件
  const pathUrl = path.resolve(__dirname, '../', '../', 'src', 'locales');
  const dir = fs.readdirSync(pathUrl);

  // 过滤掉不需要处理的文件
  const filesToProcess = dir.filter(file => {
    return file !== 'index.ts' && file !== 'it.ts';
  });

  for (const langFile of filesToProcess) {
    const filePath = `${pathUrl}/${langFile}`;
    const content = await readLangsFile(filePath);

    // 检查目标key是否已存在
    const targetKeyExists = new RegExp(`["']${targetKey}["']\\s*:`).test(content);
    if (targetKeyExists) {
      console.log(`警告: ${langFile} 中目标key ${targetKey} 已存在，跳过处理`);
      continue;
    }

    const result = handleCopy(content, sourceKey, targetKey);
    if (result !== content) {
      await writeLangsResultToFile(filePath, result);
      console.log(`${langFile} 处理完成`);
    } else {
      console.log(`${langFile} 中未找到源key ${sourceKey}`);
    }
  }

  console.log('复制操作完成，请检查文件确保正确性');
}

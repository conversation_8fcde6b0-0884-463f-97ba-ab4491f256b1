const {readSheet, getSheetRowCode, writeLangsResultToFile, readLangsFile, deleteCol} = require("./util.cjs");
const path = require("path");
const filePath = path.resolve(__dirname, '../', 'assets', '录咖多语言翻译.xlsx')
const langsPath = path.resolve(__dirname, '../', '../', 'src', 'locales')
//非专员语言
const noMainLangs = {
  cs: 'Z',
  da: 'Z',
  fi: 'Z',
  hu: 'Z',
  it: 'Z',
  nl: 'Z',
  no: 'Z',
  pl: 'Z',
  sl: 'Z',
  sv: 'Z',
  tr: 'Z',
}

startAddLangs().then()

/**
 * 增量写入多语言[js]文件中
 * 不存在的key就新增
 * 存在的key就替换
 * @return {Promise<void>}
 */
async function startAddLangs() {
  const sheetObj = readSheet(filePath);
  //删除第一行[图示]一列
  deleteCol(sheetObj, 0)
  const {numList, characterList} = getSheetRowCode(sheetObj)
  //{zh:B, en:C, tw:D } lang:列号
  let langLenObj = {};
  for (let i = 1; i < characterList.length; i++) {
    if (!sheetObj[characterList[i] + '1']) {
      continue
    }
    let langStr = sheetObj[characterList[i] + '1'].v
    langStr = langStr.replace(/\n.*$/, '');
    langStr = langStr.replace(/\(.*$/, '');
    langStr = langStr.trim().toLowerCase();
    langLenObj[langStr] = characterList[i];
  }

  // 0029992:2 key与行号
  let keyWithRowNum = {};
  for (let i = 1; i < numList.length; i++) {
    let key = sheetObj[characterList[0] + numList[i]];
    const reg = /^\w/
    if (key && reg.test(key.v)) {
      key = key.v.trim();
      keyWithRowNum[key] = numList[i];
    }
  }

  //en下的所有k-v 存储起来给其他非专员语言使用
  let enObj = {}
  for (let lang in langLenObj) {
    const obj = {}
    for (const keyWithRowNumKey in keyWithRowNum) {
      const code = langLenObj[lang] + keyWithRowNum[keyWithRowNumKey]
      obj[keyWithRowNumKey] = sheetObj[code]?.v || enObj[keyWithRowNumKey] || ''
    }
    const back = lang === 'ja' ? 'jp' : lang
    const url = path.resolve(langsPath, back + ".ts")
    const data = await readLangsFile(url)
    const {addObj, updateObj} = filterObj(obj, data);
    if (lang.toString().toLowerCase() === 'en') {
      enObj = obj
    }
    if (data) {
      //开始新增key
      let str = '';
      for (let key in addObj) {
        str += `  "${key}": \`${obj[key]}\`,\n`;
      }

      let result = data.replace('} as const', str + "} as const")

      //开始替换
      for (const updateObjKey in updateObj) {
        const reg = new RegExp(`['|"]${updateObjKey}['|"].+?["|'\`],`, 'gs')
        result = result.replace(reg, `"${updateObjKey}": \`${updateObj[updateObjKey]}\`,`)
      }

      await writeLangsResultToFile(url, result)
    } else {
      console.log(url, '有问题,请检查是否存在')
    }
  }
}


function filterObj(obj, data) {
  //需要添加的obj
  const addObj = {}
  //需要更新的obj
  const updateObj = {}

  for (const objKey in obj) {
    const reg = new RegExp(`['|"]${objKey}['|"]\s?:`, 'g')
    const result = reg.test(data)
    if (result) {
      updateObj[objKey] = obj[objKey]
    } else {
      addObj[objKey] = obj[objKey]
    }
  }
  return {addObj, updateObj}
}


